import 'package:my_video/app_imports.dart';

final GoRouter router = GoRouter(
  initialLocation: AppRoutes.splash,
  routes: [
    // Authentication Routes
    GoRoute(
      path: AppRoutes.splash,
      builder: (context, state) => const SplashPage(),
    ),
    GoRoute(
      path: AppRoutes.login,
      builder: (context, state) => const LoginPage(),
    ),
    GoRoute(
      path: AppRoutes.signup,
      builder: (context, state) => const SignupPage(),
    ),
    GoRoute(
      path: AppRoutes.forgotPassword,
      builder: (context, state) => const ForgotPasswordPage(),
    ),

    // Main App Routes
    GoRoute(
      path: AppRoutes.mainNavigation,
      builder: (context, state) => const MainNavigationPage(),
    ),
    GoRoute(
      path: AppRoutes.noInternet,
      builder: (context, state) => const NoInternetConnectionPage(),
    ),

    // Movie Related Routes
    GoRoute(
      path: AppRoutes.moviePlayer,
      builder: (context, state) {
        final movie = state.extra as MovieModel;
        return VideoPlayerPage(movie: movie);
      },
    ),

    // User Profile & Settings Routes
    GoRoute(
      path: AppRoutes.profile,
      builder: (context, state) => const ProfilePage(),
    ),
    GoRoute(
      path: AppRoutes.termsOfService,
      builder: (context, state) => const TermsOfServicePage(),
    ),
    GoRoute(
      path: AppRoutes.privacyPolicy,
      builder: (context, state) => const PrivacyPolicyPage(),
    ),
    GoRoute(
      path: AppRoutes.contactSupport,
      builder: (context, state) => const ContactSupportPage(),
    ),
  ],
  errorBuilder: (context, state) {
    MySize.init(context);
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: MySize.size64,
              color: AppColorConstants.colorRed,
            ),
            Space.height(16),
            AppText(
              text: 'Page not found',
              fontSize: MySize.fontSize(20),
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
            ),
            Space.height(8),
            AppText(
              text: 'The page you are looking for does not exist.',
              fontSize: MySize.fontSize(14),
              color: AppColorConstants.textSecondary,
            ),
            Space.height(24),
            AppButton(
              text: 'Go Home',
              onPressed: () => context.go(AppRoutes.splash),
            ),
          ],
        ),
      ),
    );
  },
);
