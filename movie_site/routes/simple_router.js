const express = require("express");
const router = express.Router();
const bodyParser = require("body-parser");
const multer = require("multer");
const crypto = require("crypto");
require("dotenv").config();

const controller = require("../controller/simple_controller");

jsonparser = bodyParser.json();
const form = multer();

// Uploading profile in server using multer
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, "uploads/profile");
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + "-" + crypto.randomBytes(6).toString("hex");
    cb(null, file.originalname);
  },
});

const upload = multer({
  storage: storage,
  fileFilter: (req, file, cb) => {
    if (
      file.mimetype == "image/png" ||
      file.mimetype == "image/jpg" ||
      file.mimetype == "image/jpeg" ||
      file.mimetype == "image/webp" ||
      file.mimetype == "image/avif" ||
      file.mimetype == "image/avi"
    ) {
      cb(null, true);
    } else {
      cb(null, false);
      return cb("Only .png, .jpg and .jpeg format allowed!");
    }
  },
  limits: {
    fileSize: 2 * 1024 * 1024,
  },
});

// Uploading post pic in server using multer
const storage2 = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, "uploads/thumbnail");
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + "-" + crypto.randomBytes(6).toString("hex");
    cb(null, file.originalname);
  },
});

const upload_post_img = multer({
  storage: storage2,
  fileFilter: (req, file, cb) => {
    if (
      file.mimetype == "image/jpg" ||
      file.mimetype == "image/png" ||
      file.mimetype == "image/jpeg" ||
      file.mimetype == "image/webp" ||
      file.mimetype == "image/avif" ||
      file.mimetype == "image/avi"
    ) {
      cb(null, true);
    } else {
      cb(null, false);
      return cb("Only .png, .jpg .mp4 .avi and .jpeg format allowed!");
    }
  },
  limits: {
    fileSize: 2 * 1024 * 1024,
  },
});

// Simple middleware for basic auth (you can enhance this)
const basicAuth = (req, res, next) => {
  // For now, just pass through - you can add authentication logic here
  next();
};

// Basic routes
router.get("/", (req, res) => {
  res.json({ 
    status: 1, 
    message: "Movie Streaming API is running!",
    endpoints: [
      "POST /dashboard",
      "POST /showposts/:page",
      "POST /searchpost/:page", 
      "POST /showfilter"
    ]
  });
});

// Dashboard endpoint
router.post("/dashboard", controller.dashboard);
router.post("/dashboard_test", controller.adminDashboardTest);

// Admin authentication endpoints
router.post("/adminlogin", form.array(), controller.adminLogin);

// Movie/Post endpoints
router.post("/showposts/:page?", form.array(), controller.UserShowPosts);
router.post("/searchpost/:page", basicAuth, controller.UserSearchPost);

// Filter endpoints
router.post("/showfilter", basicAuth, controller.ShowFilter);

// Health check
router.get("/health", (req, res) => {
  res.json({ 
    status: "OK", 
    timestamp: new Date().toISOString(),
    database: "SQLite Connected"
  });
});

// Test endpoint
router.get("/test", (req, res) => {
  res.json({ 
    status: 1, 
    message: "Test endpoint working",
    data: {
      server: "Movie Streaming Backend",
      version: "1.0.0",
      database: "SQLite"
    }
  });
});

module.exports = router;
