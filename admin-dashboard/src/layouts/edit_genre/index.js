/* eslint-disable */
import React, { useEffect } from "react";
import { useState } from "react";
import { useCookies } from "react-cookie";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import Footer from "examples/Footer";
import MDBox from "components/MDBox";
import { Card, Grid, Typography } from "@mui/material";
import MDTypography from "components/MDTypography";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { FormatIndentDecreaseTwoTone } from "@mui/icons-material";
import { EditText } from "react-edit-text";
import "react-edit-text/dist/index.css";
import DataTable from "examples/Tables/DataTable";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Modal from "examples/Cards/ProjectCards/DefaultProjectCard/Modal";

import useMediaQuery from "@material-ui/core/useMediaQuery";
import { useTheme } from "@material-ui/core/styles";

//require("dotenv").config()
function Tables() {
  const [cookie, setcookie] = useCookies();
  const [result, setresult] = useState(null);
  const token = cookie.admin;
  const getfilter = async () => {
    const fetchfilter = await fetch(`${window.path}/adminshowfilters`, {
      method: "POST",
      headers: {
        auth: token,
      },
    });
    const resp = await fetchfilter.json();
    console.log(resp);
    return resp;
  };
  useEffect(() => {
    getfilter().then((e) => {
      setresult(e);
    });
  }, []);

  const dragfuntion = async (e) => {
    const form = new FormData();
    const value = JSON.stringify(e);
    if (e.destination.droppableId == "genre") {
      form.append("genre", value);
    }
    if (e.destination.droppableId == "category") {
      form.append("category", value);
    }
    if (e.destination.droppableId == "language") {
      form.append("lang", value);
    }
    const sendData = await fetch(`${window.path}/adminsetgenre`, {
      method: "post",
      headers: {
        auth: token,
      },
      body: form,
    });
    const res = await sendData.json();
    if (res.status == 1) {
      window.location.reload(false);
    }
  };

  const delCategory = async (id) => {
    const delform = new FormData();
    delform.append("id", id);
    const del = await fetch(`${window.path}/admindelcategory`, {
      method: "post",
      headers: {
        auth: token,
      },
      body: delform,
    });
    const res = await del.json();
    if (res.status === 1) {
      toast.success("deleted successfully !", {
        position: toast.POSITION.TOP_CENTER,
      });
      window.location.reload(false);
    } else {
      toast.error("internal error !", {
        position: toast.POSITION.TOP_CENTER,
      });
    }
  };
  const delLanguage = async (id) => {
    const delform = new FormData();
    setLangDelete(false);
    delform.append("id", id);
    const del = await fetch(`${window.path}/admindellanguage`, {
      method: "post",
      headers: {
        auth: token,
      },
      body: delform,
    });
    const res = await del.json();
    if (res.status === 1) {
      toast.success("deleted successfully !", {
        position: toast.POSITION.TOP_CENTER,
      });
      window.location.reload(false);
    } else {
      toast.error("internal error !", {
        position: toast.POSITION.TOP_CENTER,
      });
    }
  };
  const delGenre = async (id) => {
    const delform = new FormData();
    setGenreDelete(false);
    delform.append("id", id);
    const del = await fetch(`${window.path}/admindelgnere`, {
      method: "post",
      headers: {
        auth: token,
      },
      body: delform,
    });
    const res = await del.json();
    if (res.status === 1) {
      toast.success("deleted successfully !", {
        position: toast.POSITION.TOP_CENTER,
      });
      window.location.reload(false);
    } else {
      toast.error("internal error !", {
        position: toast.POSITION.TOP_CENTER,
      });
    }
  };

  const updatecategory = async (e, j) => {
    const updateform = new FormData();
    updateform.append("id", j);
    updateform.append("value", e.value);

    const updatecat = await fetch(`${window.path}/adminupdatecat`, {
      method: "post",
      headers: {
        auth: token,
      },
      body: updateform,
    });
    const response = await updatecat.json();
    // console.log(response)
    if (response.status === 1) {
      toast.success("updated successfully !", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 100,
      });
    }
  };
  const updatelanguage = async (e, j) => {
    const updateform = new FormData();
    updateform.append("id", j);
    updateform.append("value", e.value);

    const updatecat = await fetch(`${window.path}/adminupdatelang`, {
      method: "post",
      headers: {
        auth: token,
      },
      body: updateform,
    });
    const response = await updatecat.json();
    // console.log(response)
    if (response.status === 1) {
      toast.success("updated successfully !", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 100,
      });
    }
  };
  const [isOpen, setIsOpen] = useState(false);
  const [GenreDeleteOpen, setGenreDeleteOpen] = useState(false);
  const [languageDeleteOpen, setLanguageDeleteOpen] = useState(false);
  const [deletee, setdeletee] = useState(false);
  const [genreDelete, setGenreDelete] = useState(false);
  const [langDelete, setLangDelete] = useState(false);
  const [catid, setcatid] = useState();
  const [genid, setgenid] = useState();
  const [langid, setlangid] = useState();
  const [newGenre, setNewGenre] = useState("");
  const [newCat, setNewCat] = useState("");
  const [newLang, setNewLang] = useState("");

  if (deletee == true) {
    delCategory(catid);
  }
  if (genreDelete == true) {
    delGenre(genid);
  }
  if (langDelete == true) {
    delLanguage(langid);
  }

  const addNewGenre = async () => {
    try {
      if (!newGenre) {
        return toast.error("Genre is required", {
          position: toast.POSITION.TOP_CENTER,
        });
      }
      const response = await fetch("http://206.189.137.63:8010/AdminNewGenre", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ genre: newGenre }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      if (data.status == 200) {
        setNewGenre("");
        toast.success(data.message, {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 600,
        });
        getfilter().then((e) => {
          setresult(e);
        });
      }
    } catch (error) {
      console.error("Error making API call:", error.message);
    }
  };

  const addNewCate = async () => {
    try {
      if (newCat == "" || newCat == undefined || newCat == null) {
        return toast.error("Category is required", {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 1000,
        });
      }
      const response = await fetch(
        "http://206.189.137.63:8010/AdminAddCategory",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ category: newCat }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      if (data.status == 200) {
        setNewCat("");
        toast.success(data.message, {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 1000,
        });
        getfilter().then((e) => {
          setresult(e);
        });
      }
    } catch (error) {
      console.error("Error making API call:", error.message);
    }
  };

  const addNewLang = async () => {
    try {
      if (newLang == "" || newLang == undefined || newLang == null) {
        return toast.error("Language is required", {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 1000,
        });
      }
      const response = await fetch(
        "http://206.189.137.63:8010/AdminAddLanguage",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ lang: newLang }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      if (data.status == 200) {
        setNewLang("");
        toast.success(data.message, {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 600,
        });
        getfilter().then((e) => {
          setresult(e);
        });
      }
    } catch (error) {
      console.error("Error making API call:", error.message);
    }
  };

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <ToastContainer />
      {languageDeleteOpen && (
        <Modal setOpenModal={setLanguageDeleteOpen} setdelete={setLangDelete} />
      )}
      {isOpen && <Modal setOpenModal={setIsOpen} setdelete={setdeletee} />}
      {GenreDeleteOpen && (
        <Modal setOpenModal={setGenreDeleteOpen} setdelete={setGenreDelete} />
      )}
      <MDBox pt={6} pb={3}>
        <Grid container spacing={6}>
          <Grid item xs={12}>
            <MDBox
              display={isMobile ? "block" : "flex"}
              justifyContent="space-around"
              width="100%"
            >
              <MDBox
                width={isMobile ? "100%" : "39%"}
                mt="-60px"
                align="center"
              >
                <MDTypography variant="h1">Genre</MDTypography>
                <Card
                  sx={{ width: isMobile ? "100%" : "75%", marginTop: "15px" }}
                  className="text-center"
                  style={{ maxHeight: "640px", overflowY: "auto" }}
                >
                  <table className="table table-borderless ">
                    <thead>
                      <tr>
                        <th scope="col">ID</th>
                        <th scope="col">Genre</th>
                        <th scope="col">Action</th>
                      </tr>
                    </thead>

                    <DragDropContext onDragEnd={dragfuntion}>
                      <Droppable droppableId="genre">
                        {(provided, snapshot) => (
                          <tbody
                            {...provided.droppableProps}
                            ref={provided.innerRef}
                          >
                            {result?.genre?.map((e, i) => {
                              return (
                                <tr>
                                  <th scope="row">{i + 1}</th>
                                  <Draggable
                                    key={e.genre}
                                    draggableId={e.genre}
                                    index={i + 1}
                                  >
                                    {(provided, snapshot) => (
                                      <>
                                        <td
                                          {...provided.draggableProps}
                                          ref={provided.innerRef}
                                          {...provided.dragHandleProps}
                                        >
                                          <div
                                            style={{
                                              backgroundColor: "#e8e4e3",
                                              width: "240px",
                                              height: "37px",
                                              borderRadius: "15px",
                                              float: "right",
                                              marginRight: "30px",
                                            }}
                                          >
                                            {e.genre}
                                          </div>
                                        </td>
                                        <td>
                                          <div style={{ display: "flex" }}>
                                            <button
                                              className="btn btn-sm btn-success"
                                              onClick={() => {
                                                setGenreDeleteOpen(true);
                                                setgenid(e.id);
                                              }}
                                            >
                                              DELETE
                                            </button>
                                          </div>
                                        </td>
                                      </>
                                    )}
                                  </Draggable>
                                </tr>
                              );
                            })}
                          </tbody>
                        )}
                      </Droppable>
                    </DragDropContext>
                  </table>
                </Card>
                <div className="mt-3 w-full">
                  <input
                    type="text"
                    placeholder=" Add new Genre"
                    value={newGenre}
                    onChange={(e) => setNewGenre(e.target.value)}
                    style={{
                      marginRight: "20px",
                      borderRadius: "7.5px",
                      fontSize: "18px",
                      border: "1px solid",
                      paddingLeft: "10px",
                    }}
                  />
                  <button
                    className="btn btn-sm btn-primary   ml-3"
                    onClick={() => addNewGenre()}
                    style={{ width: "60px", backgroundColor: "#1A73E8" }}
                  >
                    Add
                  </button>
                </div>
              </MDBox>
              <MDBox
                width={isMobile ? "100%" : "39%"}
                mt={isMobile ? "10px" : "-60px"}
                align="center"
              >
                <MDTypography variant="h1">Category</MDTypography>
                <Card
                  sx={{
                    width: isMobile ? "100%" : "75%",
                    marginTop: "15px",
                    maxH: "650px",
                  }}
                  className="text-center"
                  pt={2}
                  style={{
                    maxHeight: "640px",
                    overflowY: "auto",
                    width: isMobile ? "100%" : "75%",
                  }}
                >
                  <table
                    className="table table-borderless"
                    maxH={isMobile ? "100%" : 650}
                  >
                    <thead>
                      <tr>
                        <th scope="col">ID</th>
                        <th scope="col">Category</th>
                        <th scope="col">Action</th>
                      </tr>
                    </thead>

                    <DragDropContext onDragEnd={dragfuntion}>
                      <Droppable droppableId="category">
                        {(provided, snapshot) => (
                          <tbody
                            {...provided.droppableProps}
                            ref={provided.innerRef}
                          >
                            {result?.category?.map((e, i) => {
                              return (
                                <tr>
                                  <th scope="row">{i + 1}</th>
                                  <Draggable
                                    key={e.category}
                                    draggableId={e.category}
                                    index={i + 1}
                                  >
                                    {(provided, snapshot) => (
                                      <>
                                        <td
                                          {...provided.draggableProps}
                                          ref={provided.innerRef}
                                          {...provided.dragHandleProps}
                                        >
                                          <div
                                            style={{
                                              backgroundColor: "#e8e4e3",
                                              width: "240px",
                                              height: "37px",
                                              borderRadius: "15px",
                                              float: "right",
                                              marginRight: "30px",
                                            }}
                                          >
                                            {e.category}
                                          </div>
                                        </td>
                                        <td>
                                          <div style={{ display: "flex" }}>
                                            <button
                                              className="btn btn-sm btn-success"
                                              onClick={() => {
                                                setIsOpen(true);
                                                setcatid(e.id);
                                              }}
                                            >
                                              DELETE
                                            </button>
                                          </div>
                                        </td>
                                      </>
                                    )}
                                  </Draggable>
                                </tr>
                              );
                            })}
                          </tbody>
                        )}
                      </Droppable>
                    </DragDropContext>
                  </table>
                </Card>
                <div className="mt-3">
                  <input
                    type="text"
                    placeholder=" Add new Category"
                    value={newCat}
                    onChange={(e) => setNewCat(e.target.value)}
                    style={{
                      marginRight: "20px",
                      borderRadius: "7.5px",
                      fontSize: "18px",
                      border: "1px solid",
                      paddingLeft: "10px",
                    }}
                  />
                  <button
                    className="btn btn-sm btn-primary ml-3"
                    onClick={() => addNewCate()}
                    style={{ width: "60px", backgroundColor: "#1A73E8" }}
                  >
                    Add
                  </button>
                </div>
              </MDBox>

              <MDBox
                width={isMobile ? "100%" : "32%"}
                mt={isMobile ? "10px" : "-60px"}
                align="center"
              >
                <MDTypography variant="h1">Language</MDTypography>
                <Card
                  sx={{
                    width: isMobile ? "100%" : "75%",
                    marginTop: "15px",
                    maxH: "650px",
                  }}
                  className="text-center"
                  pt={2}
                  style={{
                    maxHeight: "640px",
                    overflowY: "auto",
                    width: isMobile ? "100%" : "75%",
                  }}
                >
                  <table className="table table-borderless" maxH={650}>
                    <thead>
                      <tr>
                        <th scope="col">ID</th>
                        <th scope="col">Language</th>
                        <th scope="col">Action</th>
                      </tr>
                    </thead>

                    <DragDropContext onDragEnd={dragfuntion}>
                      <Droppable droppableId="language">
                        {(provided, snapshot) => (
                          <tbody
                            {...provided.droppableProps}
                            ref={provided.innerRef}
                          >
                            {result?.language?.map((e, i) => {
                              return (
                                <tr>
                                  <th scope="row">{i + 1}</th>
                                  <Draggable
                                    key={e.language}
                                    draggableId={e.language}
                                    index={i + 1}
                                  >
                                    {(provided, snapshot) => (
                                      <>
                                        <td
                                          {...provided.draggableProps}
                                          ref={provided.innerRef}
                                          {...provided.dragHandleProps}
                                        >
                                          <EditText
                                            defaultValue={e.language}
                                            style={{
                                              width: "120px",
                                              textAlign: "left",
                                              margin: "0px",
                                            }}
                                            onSave={(j) => {
                                              updatelanguage(j, e.id);
                                            }}
                                          />
                                        </td>
                                        <td>
                                          <div style={{ display: "flex" }}>
                                            <button
                                              className="btn btn-sm btn-success"
                                              onClick={() => {
                                                setLanguageDeleteOpen(true);
                                                setlangid(e.id);
                                              }}
                                            >
                                              DELETE
                                            </button>
                                          </div>
                                        </td>
                                      </>
                                    )}
                                  </Draggable>
                                </tr>
                              );
                            })}
                          </tbody>
                        )}
                      </Droppable>
                    </DragDropContext>
                  </table>
                </Card>
                <div className="mt-3">
                  <input
                    type="text"
                    placeholder=" Add new Category"
                    value={newCat}
                    onChange={(e) => setNewCat(e.target.value)}
                    style={{
                      marginRight: "20px",
                      borderRadius: "7.5px",
                      fontSize: "18px",
                      border: "1px solid",
                      paddingLeft: "10px",
                    }}
                  />
                  <button
                    className="btn btn-sm btn-primary ml-3"
                    onClick={() => addNewCate()}
                    style={{ width: "60px", backgroundColor: "#1A73E8" }}
                  >
                    Add
                  </button>
                </div>
              </MDBox>

              {/* <MDBox width="32%" mt="-60px" align="center">
                <MDTypography variant="h1">Language</MDTypography>
                <MDBox width="80%" pt={2}>
                  {result != null ? (
                    <>
                      <Card
                        sx={{ width: "90%", marginTop: "5px" }}
                        className="text-center"
                        style={{ maxHeight: "640px", overflowY: "auto" }}
                      >
                        <table
                          className="table table-borderless "
                          style={{ maxH: "640px", overflowY: "auto" }}
                        >
                          <thead>
                            <tr>
                              <th scope="col">ID</th>
                              <th scope="col">Genre</th>
                              <th scope="col">Action</th>
                            </tr>
                          </thead>
                          <tbody>
                            {result.language.map((e, i) => (
                              <tr key={e.id}>
                                <th scope="row">{i + 1}</th>
                                <td>
                                  <EditText
                                    defaultValue={e.language}
                                    style={{
                                      width: "120px",
                                      textAlign: "left",
                                      margin: "0px",
                                    }}
                                    onSave={(j) => {
                                      updatelanguage(j, e.id);
                                    }}
                                  />
                                </td>
                                <td>
                                  <button
                                    className="btn btn-sm btn-success"
                                    onClick={() => {
                                      setLanguageDeleteOpen(true);
                                      setlangid(e.id);
                                    }}
                                  >
                                    DELETE
                                  </button>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </Card>
                      {/* <DataTable
                        table={{
                          columns: [
                            {
                              Header: "id",
                              accessor: "id",
                              width: "0",
                              align: "left",
                            },
                            {
                              Header: "language",
                              accessor: "language",
                              width: "0",
                              align: "left",
                            },
                            {
                              Header: "action",
                              accessor: "action",
                              align: "center",
                            },
                          ],
                          rows: result?.language?.map((e, i) => ({
                            id: <Typography>{i + 1}</Typography>,
                            language: (
                              <EditText
                                defaultValue={e.language}
                                style={{
                                  width: "120px",
                                  textAlign: "left",
                                  margin: "0px",
                                }}
                                onSave={(j) => {
                                  updatelanguage(j, e.id);
                                }}
                              />
                            ),
                            action: (
                              <button
                                className="btn btn-sm btn-success"
                                onClick={() => {
                                  setLanguageDeleteOpen(true);
                                  setlangid(e.id);
                                }}
                              >
                                DELETE
                              </button>
                            ),
                          })),
                        }}
                        isSorted={false}
                        // entriesPerPage={false}
                        // showTotalEntries={false}
                        pagination={false}
                        noEndBorder
                        maxH={640}
                        margin={0}
                      /> 
                    </>
                  ) : (
                    <Typography>No data found</Typography>
                  )}
                </MDBox>
                <div className="mt-3">
                  <input
                    type="text"
                    placeholder=" Add new Language"
                    value={newLang}
                    onChange={(e) => setNewLang(e.target.value)}
                    style={{
                      marginRight: "20px",
                      borderRadius: "7.5px",
                      fontSize: "18px",
                      border: "1px solid",
                      paddingLeft: "10px",
                    }}
                  />
                  <button
                    className="btn btn-sm btn-primary ml-3"
                    onClick={() => addNewLang()}
                    style={{ width: "60px", backgroundColor: "#1A73E8" }}
                  >
                    Add
                  </button>
                </div>
              </MDBox> */}
            </MDBox>
          </Grid>
        </Grid>
      </MDBox>
      <Footer />
    </DashboardLayout>
  );
}

export default Tables;
