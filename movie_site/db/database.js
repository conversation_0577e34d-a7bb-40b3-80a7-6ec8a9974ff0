const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class Database {
  constructor() {
    this.db = null;
    this.init();
  }

  init() {
    const dbPath = path.join(__dirname, '..', 'movie_site.db');
    this.db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('Error opening database:', err.message);
      } else {
        console.log('Connected to SQLite database');
        // Enable foreign keys
        this.db.run('PRAGMA foreign_keys = ON');
      }
    });
  }

  // Wrapper for database queries to match MySQL interface
  query(sql, params = [], callback) {
    if (typeof params === 'function') {
      callback = params;
      params = [];
    }

    // Handle different query types
    if (sql.trim().toLowerCase().startsWith('select')) {
      this.db.all(sql, params, callback);
    } else if (sql.trim().toLowerCase().startsWith('insert')) {
      this.db.run(sql, params, function(err) {
        if (callback) {
          callback(err, { insertId: this.lastID, affectedRows: this.changes });
        }
      });
    } else {
      this.db.run(sql, params, function(err) {
        if (callback) {
          callback(err, { affectedRows: this.changes });
        }
      });
    }
  }

  // Close database connection
  close() {
    if (this.db) {
      this.db.close((err) => {
        if (err) {
          console.error('Error closing database:', err.message);
        } else {
          console.log('Database connection closed');
        }
      });
    }
  }

  // Get database instance for direct access if needed
  getDb() {
    return this.db;
  }
}

// Create singleton instance
const database = new Database();

module.exports = database;
