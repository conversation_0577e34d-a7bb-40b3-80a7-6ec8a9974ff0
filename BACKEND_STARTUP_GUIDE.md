# 🚀 Backend Startup Guide

## 📋 Configuration Summary

Your backend is now configured to use:
- **Base URL**: `https://appzone99.science:3001`
- **HTTPS Port**: 3001
- **HTTP Port**: 8010 (fallback)
- **Domain**: appzone99.science

## 🔧 Backend Configuration

### Environment Variables (`.env`)
```env
PORT = 8010
HOST = localhost
DB_TYPE = sqlite
DB_PATH = ./movie_site.db
USER_SECRET = "iy98hcbh489n38984y4h498"
ADMIN_SECRET = "joge15fs4a5sd4f7abns5a4a"
ADMIN = "admin"
PASS = "admin123"
STATIC_KEY = "Habt5o0cDNWjc42y"
PUBLIC_PROFILE_PATH = 'https://appzone99.science:3001/upload/profile/'
PUBLIC_POST_PATH = 'https://appzone99.science:3001/upload/thumbnail/'
```

### SSL Certificate
The backend uses SSL certificate for `appzone99.science` domain:
- Certificate file: `appzone99_science.crt`
- Private key: `appzone99_science.key`

## 🚀 Starting the Backend

### 1. Install Dependencies
```bash
cd movie_site
npm install
```

### 2. Start the Server
```bash
npm start
# or
node app.js
```

### 3. Verify Server is Running
The server will start on:
- **HTTPS**: `https://appzone99.science:3001`
- **HTTP**: `http://localhost:8010` (fallback)

You should see:
```
port is running on 8010
```

## 📱 Flutter App Configuration

### Base URL Configuration
```dart
// lib/app/config/app_config.dart
String get baseUrl {
  switch (flavor) {
    case Flavor.dev:
      return 'https://appzone99.science:3001/';
    case Flavor.prod:
      return 'https://appzone99.science:3001/';
  }
}
```

## 🧪 Testing the Setup

### 1. Test Backend Health
```bash
curl -k https://appzone99.science:3001/testdata
```

### 2. Test Authentication Endpoints
```bash
# Get unregistered user token
curl -k -X POST https://appzone99.science:3001/unregisteredusertoken

# Test login/register
curl -k -X POST https://appzone99.science:3001/login \
  -H "Content-Type: application/json" \
  -d '{
    "fname": "Test",
    "lname": "User",
    "email": "<EMAIL>",
    "gender": "m"
  }'
```

### 3. Use Postman Collection
Import the `movie_site_postman_collection.json` file into Postman:
- Base URL is set to: `https://appzone99.science:3001`
- All endpoints are pre-configured
- Test authentication flow

## 🔒 SSL/HTTPS Considerations

### For Development
If you're testing locally and the SSL certificate doesn't match your local setup:

1. **Option 1**: Update your hosts file to point `appzone99.science` to localhost:
   ```bash
   # Add to /etc/hosts (macOS/Linux) or C:\Windows\System32\drivers\etc\hosts (Windows)
   127.0.0.1 appzone99.science
   ```

2. **Option 2**: Use HTTP fallback for local development:
   ```dart
   // Temporarily change base URL for local testing
   return 'http://localhost:8010/';
   ```

### For Production
Ensure your domain `appzone99.science` points to your server's IP address and the SSL certificate is valid.

## 🐛 Troubleshooting

### Backend Won't Start
1. **Check Port Availability**:
   ```bash
   lsof -i :3001
   lsof -i :8010
   ```

2. **Check SSL Certificate**:
   - Ensure `appzone99_science.crt` and `appzone99_science.key` exist
   - Verify certificate is not expired

3. **Check Dependencies**:
   ```bash
   npm install
   ```

### Flutter App Can't Connect
1. **Check Base URL**: Verify `app_config.dart` has correct URL
2. **Check Network**: Ensure device can reach `appzone99.science:3001`
3. **Check SSL**: For development, you might need to handle SSL certificate issues

### API Endpoints Not Working
1. **Check Server Logs**: Look for errors in the backend console
2. **Test with Postman**: Use the provided collection to test endpoints
3. **Check Database**: Ensure SQLite database is accessible

## 📊 API Endpoints Summary

### Authentication
- `POST /unregisteredusertoken` - Get static token
- `POST /login` - User login/register
- `POST /adminlogin` - Admin login
- `POST /logoutuser` - User logout
- `POST /logoutadmin` - Admin logout

### Movies & Content
- `POST /dashboard` - Get dashboard data
- `POST /showposts/:page` - Get movies with pagination
- `POST /searchpost/:page` - Search movies
- `POST /uploadpost` - Upload new movie

### User Management
- `GET /showprofile/:id` - Get user profile
- `POST /userupdate` - Update user profile
- `POST /followuser` - Follow user
- `POST /unfollowuser` - Unfollow user

## 🎯 Next Steps

1. **Start Backend**: Follow the startup steps above
2. **Test API**: Use Postman collection to verify endpoints
3. **Run Flutter App**: Test the complete authentication flow
4. **Monitor Logs**: Check both backend and Flutter app logs for any issues

The configuration is now set to use `https://appzone99.science:3001` as the base URL for both backend and Flutter app! 🎉
