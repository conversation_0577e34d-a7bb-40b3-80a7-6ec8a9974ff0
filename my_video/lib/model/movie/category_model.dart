import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'category_model.g.dart';

@JsonSerializable()
@HiveType(typeId: 1)
class CategoryModel extends HiveObject {
  @HiveField(0)
  @<PERSON>son<PERSON><PERSON>(name: 'id')
  final String id;

  @HiveField(1)
  @Json<PERSON>ey(name: 'name')
  final String name;

  @HiveField(2)
  @<PERSON>sonKey(name: 'description')
  final String? description;

  @HiveField(3)
  @<PERSON>sonKey(name: 'icon_url')
  final String? iconUrl;

  @HiveField(4)
  @<PERSON>sonKey(name: 'banner_url')
  final String? bannerUrl;

  @HiveField(5)
  @<PERSON>son<PERSON>ey(name: 'movie_count')
  final int movieCount;

  @HiveField(6)
  @JsonKey(name: 'is_active')
  final bool isActive;

  @HiveField(7)
  @JsonKey(name: 'sort_order')
  final int sortOrder;

  @HiveField(8)
  @<PERSON>son<PERSON>ey(name: 'created_at')
  final DateTime? createdAt;

  @HiveField(9)
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;

  CategoryModel({
    required this.id,
    required this.name,
    this.description,
    this.iconUrl,
    this.bannerUrl,
    this.movieCount = 0,
    this.isActive = true,
    this.sortOrder = 0,
    this.createdAt,
    this.updatedAt,
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) =>
      _$CategoryModelFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryModelToJson(this);

  @override
  String toString() {
    return 'CategoryModel(id: $id, name: $name, movieCount: $movieCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Copy with method for updating category data
  CategoryModel copyWith({
    String? id,
    String? name,
    String? description,
    String? iconUrl,
    String? bannerUrl,
    int? movieCount,
    bool? isActive,
    int? sortOrder,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      iconUrl: iconUrl ?? this.iconUrl,
      bannerUrl: bannerUrl ?? this.bannerUrl,
      movieCount: movieCount ?? this.movieCount,
      isActive: isActive ?? this.isActive,
      sortOrder: sortOrder ?? this.sortOrder,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
