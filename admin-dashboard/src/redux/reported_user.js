import { createSlice,createAsyncThunk } from "@reduxjs/toolkit";
import { render } from "@testing-library/react";
import React from "react";
import {useCookies} from "react-cookie";


const initialState = {
        loading :true,
        users :[],
        error:''
}


// generates pending request
export const fetchReportedUser = createAsyncThunk('user/fetchReportedUser',async({page,token,form})=>{
    const res = await   fetch(`${window.path}/adminshowreporteduser/${page}`,{
        method:"POST",
        headers:{
            auth:token
        },
        body:form

    })
    const data = await res.json()
    return data;
})
const ReportedUserSlice = createSlice({
    name:'user',
    initialState,
    extraReducers:(builder)=>{
        builder.addCase(fetchReportedUser.pending,(state)=>{
            state.loading = true;
        })
        builder.addCase(fetchReportedUser.fulfilled,(state,action)=>{
            state.loading = false;
            state.users = action.payload    
            state.error = ''
        })
        builder.addCase(fetchReportedUser.rejected,(state,action)=>{
            state.loading = false;
            state.users = ''
            state.error = action.error.message
        })
    }
})


export default ReportedUserSlice.reducer