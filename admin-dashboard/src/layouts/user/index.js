/* eslint-disable */
import React, { useEffect } from "react";
// @mui material components
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";
// Material Dashboard 2 React components
import profile from "../../assets/images/profile/porfile.png";
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import MDAvatar from "components/MDAvatar";
import MDButton from "components/MDButton";
import SearchIcon from "@mui/icons-material/Search";
// Material Dashboard 2 React example components
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import Modal from "examples/Cards/ProjectCards/DefaultProjectCard/Modal";
import Modal3 from "examples/Cards/ProjectCards/DefaultProjectCard/Modal4";
import Footer from "examples/Footer";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DataTable from "examples/Tables/DataTable";
import { useSelector, useDispatch } from "react-redux";
import { fetchUsers } from "../../redux/userslice";
import { useState } from "react";
import { useNavigate, Link, useSearchParams } from "react-router-dom";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import SearchBar from "material-ui-search-bar";
import moment from "moment/moment";
import { useCookies } from "react-cookie";
import Select from "react-select";
import { Button, Typography } from "@mui/material";
import Cover from "layouts/authentication/sign-up";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";

import useMediaQuery from "@material-ui/core/useMediaQuery";
import { useTheme } from "@material-ui/core/styles";
//require("dotenv").config()
function Tables() {
  const [page, setpage] = useState(1);
  const [finaldata, setfinaldata] = useState(null);
  let [searchParams, setSearchParams] = useSearchParams({ page: 1 });
  const { users } = useSelector((state) => state);
  const dispatch = useDispatch();
  const [cookie, getcookie] = useCookies();
  const token = cookie.admin;
  const [searchkey, setsearchkey] = useState(null);
  const [blockemail, setblockemail] = useState(null);
  const [sortoptionvalue, setSortoptionvalue] = useState({
    label: "Date Modefied",
    value: "date",
  });
  var form = new FormData();
  form.append("sort", sortoptionvalue.value);
  form.append("key", searchkey);
  useEffect(() => {
    dispatch(
      fetchUsers({ page: parseInt(searchParams.get("page")), token, form })
    ).then((u) => setfinaldata(u.payload.result));
  }, [
    dispatch,
    parseInt(searchParams.get("page")),
    searchkey,
    sortoptionvalue,
  ]);

  const Author = ({ image, name, email }) => {
    return (
      <MDBox display="flex" alignItems="center" lineHeight={1}>
        <MDAvatar
          src={image}
          name={name}
          size="lg"
          style={{ objectFit: "fill" }}
        />
        <MDBox ml={2} lineHeight={1}>
          <MDTypography display="block" variant="button" fontWeight="medium">
            {name}
          </MDTypography>
          <MDTypography variant="caption">{email}</MDTypography>
        </MDBox>
      </MDBox>
    );
  };

  const sortoption = [
    { label: "Date Modefied", value: "date" },
    { label: "Name", value: "name" },
  ];

  const prev = (e) => {
    if (parseInt(searchParams.get("page")) > 1) {
      setSearchParams({ page: parseInt(searchParams.get("page")) - 1 });
    }
  };
  const next = (e) => {
    if (users?.users?.lastpage != true) {
      setSearchParams({ page: parseInt(searchParams.get("page")) + 1 });
    }
  };
  const pages = Math.ceil(users?.users?.records / 10);
  const nopages = [];
  for (let i = 1; i <= pages; i++) {
    nopages.push(i);
  }
  let currentpages = [];
  if (parseInt(searchParams.get("page")) >= 10) {
    let s = (Math.ceil(parseFloat(searchParams.get("page") / 10)) - 1) * 10;
    currentpages = [...nopages.splice(s, 10)];
  } else {
    currentpages = [...nopages.splice(0, 10)];
  }
  const del = async (id) => {
    let formdata = new FormData();
    formdata.append("id", id);
    //console.log(form.get("id"))
    const res = await fetch(`${window.path}/admindeleteuser`, {
      method: "DELETE",
      headers: {
        auth: token,
      },
      body: formdata,
    });
    const delres = await res.json();
    if (delres.status === 1) {
      toast.success("deleted successfully !", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 100,
      });
      setdeletee(false);
      console.log(form);
      dispatch(fetchUsers({ page, token, form })).then((u) =>
        setfinaldata(u.payload.result)
      );
      // setTimeout(navigate("/users"),3000)
    } else {
      toast.error("invalid credential !", {
        position: toast.POSITION.TOP_CENTER,
      });
    }
  };

  const blockuser = async () => {
    try {
      const jsonbody = {
        email: blockemail,
      };
      const res = await fetch(`${window.path}/blockbyadmin`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Auth: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwiaWF0IjoxNzI0ODI3MTI4fQ.1em7IezVmGV8TZ_okAzY_Ox0op7a9JTD9fgX0UcuZ4s",
        },
        body: JSON.stringify(jsonbody),
      });
      if (res.status == 200) {
        // getBlockedUser();
        setblockemail("");
        setIsblockOpen(false);
        toast.success("User Blocked Successfully", {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 1500,
        });
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else if (res.status == 404) {
        setblockemail("");
        setIsblockOpen(false);
        return toast.error("User Not Found", {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 1500,
        });
      } else {
        setblockemail("");
        setIsblockOpen(false);
        // return toast.error("Something Went Wrong", {
        //   position: toast.POSITION.TOP_CENTER,
        //   autoClose: 1500,
        // });
      }
    } catch (err) {
      setblockemail("");
      setIsblockOpen(false);
      // return toast.error("Something Went Wrong", {
      //   position: toast.POSITION.TOP_CENTER,
      //   autoClose: 1500,
      // });
    }
  };

  const sorthandle = (e) => {
    setSortoptionvalue(e);
  };
  const searchHanlde = (e) => {
    e.preventDefault();
    if (e.target.value) {
      setSearchParams({ page: 1 });

      setsearchkey(e.target.value);
    } else {
      setsearchkey(null);
    }
    // if (e.length > 1 && e.length < 4) {
    //   setSearchParams({ page: 1 });
    // }

    // if (e.length > 2) {
    //   setsearchkey(e);
    // } else {
    //   setsearchkey(null);
    // }
  };
  const [isOpen, setIsOpen] = useState();
  const [deletee, setdeletee] = useState(false);
  const [isblockOpen, setIsblockOpen] = useState(false);
  const [block, setblock] = useState(false);
  const [userid, setuserid] = useState();
  if (deletee == true) {
    del(userid);
  }
  if (block == true) {
    blockuser();
  }
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  return (
    <DashboardLayout>
      <ToastContainer />
      <DashboardNavbar />
      <MDBox pt={-4} pb={3}>
        {isOpen && <Modal setOpenModal={setIsOpen} setdelete={setdeletee} />}
        {isblockOpen && (
          <Modal3 setOpenModal={setIsblockOpen} setdelete={setblock} />
        )}
        <Grid container spacing={0}>
          <Grid item xs={12}>
            <MDBox
              width="98%"
              display={isMobile ? "block" : "flex"}
              align=""
              mt={-1}
            >
              <MDBox width={isMobile ? "100%" : "70%"} mt={2} ml={4}>
                <MDTypography
                  variant="h6"
                  fontWeight="medium"
                  width={isMobile ? "90%" : "70%"}
                >
                  <span>
                    <Select
                      placeholder="sort"
                      onChange={sorthandle}
                      className="custom-select"
                      options={sortoption}
                      value={sortoptionvalue}
                      defaultValue={sortoptionvalue}
                      height={10}
                      width={40}
                    ></Select>
                  </span>
                </MDTypography>
              </MDBox>
              <MDBox
                style={{
                  marginTop: "20px",
                  marginBottom: "20px",
                  marginLeft: "10%",
                }}
              >
                <table style={{ height: "10px", border: "1px solid #c7c7c7" }}>
                  <thead style={{}}>
                    <tr style={{ display: "flex", width: "100%" }}>
                      <td
                        scope="col"
                        style={{
                          width: "40px",
                          textAlign: "center",
                          borderRight: "1px solid #b1b5b2",
                          cursor: "pointer",
                          color: "#646669",
                        }}
                        onClick={() => {
                          parseInt(searchParams.get("page")) - 10 >= 1
                            ? setSearchParams({
                                page:
                                  Math.floor(
                                    parseInt(searchParams.get("page")) / 10
                                  ) *
                                    10 -
                                  9,
                              })
                            : "";
                        }}
                      >
                        <KeyboardArrowLeftIcon
                          style={{ marginRight: "-10px" }}
                        />
                        <KeyboardArrowLeftIcon />
                      </td>
                      <td
                        scope="col"
                        style={{
                          width: "40px",
                          textAlign: "center",
                          cursor: "pointer",
                          color: "#646669",
                        }}
                        onClick={prev}
                      >
                        <KeyboardArrowLeftIcon />
                      </td>
                      {currentpages[0] > 1 ? (
                        <>
                          <td
                            style={{
                              width: "40px",
                              textAlign: "center",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              borderLeft: "1px solid #b1b5b2",
                              cursor: "pointer",
                              fontSize: "17px",
                              color: "#646669",
                            }}
                            onClick={() => {
                              setSearchParams({ page: 1 });
                            }}
                          >
                            1
                          </td>
                          <td
                            style={{
                              width: "45px",
                              textAlign: "center",
                              borderLeft: "1px solid #b1b5b2",
                              cursor: "pointer",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              fontSize: "17px",
                              color: "#646669",
                            }}
                          >
                            ...
                          </td>
                        </>
                      ) : (
                        ""
                      )}

                      {currentpages?.map((e) => {
                        return (
                          <td
                            style={{
                              borderLeft: "1px solid #b1b5b2",
                              width: "45px",
                              textAlign: "center",
                              cursor: "pointer",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              fontSize: "17px",
                              backgroundColor: `${
                                e == parseInt(searchParams.get("page"))
                                  ? "#3993EE"
                                  : ""
                              }`,
                            }}
                            onClick={() => {
                              setSearchParams({ page: e });
                            }}
                          >
                            {" "}
                            <span>
                              {" "}
                              <Link
                                to=""
                                style={{
                                  position: "absolute",
                                  color: `${
                                    e == parseInt(searchParams.get("page"))
                                      ? "white"
                                      : "#646669"
                                  }`,
                                }}
                                className={`p-1   position-relative  text-underline-hover`}
                              >
                                {e}
                              </Link>
                            </span>
                          </td>
                        );
                      })}

                      {currentpages[currentpages.length - 1] < pages ? (
                        <>
                          <td
                            style={{
                              width: "40px",
                              textAlign: "center",
                              borderLeft: "1px solid #b1b5b2",
                              cursor: "pointer",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              fontSize: "17px",
                              color: "#646669",
                            }}
                          >
                            ...
                          </td>
                          <td
                            style={{
                              width: "60px",
                              textAlign: "center",
                              borderLeft: "1px solid #b1b5b2",
                              cursor: "pointer",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              fontSize: "17px",
                              color: "#646669",
                            }}
                            onClick={() => {
                              setSearchParams({ page: pages });
                            }}
                          >
                            {pages}
                          </td>
                        </>
                      ) : (
                        ""
                      )}

                      <td
                        style={{
                          width: "40px",
                          textAlign: "center",
                          borderLeft: "1px solid #b1b5b2",
                          cursor: "pointer",
                          color: "#646669",
                        }}
                        onClick={next}
                      >
                        {" "}
                        <NavigateNextIcon />{" "}
                      </td>
                      <td
                        style={{
                          width: "40px",
                          textAlign: "center",
                          borderLeft: "1px solid #b1b5b2",
                          cursor: "pointer",
                          color: "#646669",
                        }}
                        onClick={() => {
                          parseInt(searchParams.get("page")) + 10 <= pages
                            ? setSearchParams({
                                page:
                                  Math.ceil(
                                    parseInt(searchParams.get("page")) / 10
                                  ) *
                                    10 +
                                  1,
                              })
                            : "";
                        }}
                      >
                        {" "}
                        <NavigateNextIcon style={{ marginRight: "-10px" }} />
                        <NavigateNextIcon />
                      </td>
                    </tr>
                  </thead>
                </table>
              </MDBox>
              <MDBox width={isMobile ? "100%" : "120%"}>
                <MDTypography
                  variant="h6"
                  width={isMobile ? "100%" : "80%"}
                  align={isMobile ? "" : "right"}
                  mb={2}
                  mt={2}
                  fontWeight="medium"
                >
                  <input
                    style={{
                      paddingLeft: "10PX",
                      height: "40px",
                      borderRadius: "10px",
                      border: "1px solid #7b809a",
                      width: isMobile ? "90%" : "60%",
                      marginLeft: isMobile ? "7%" : "",
                      marginTop: isMobile ? "10px" : "0px",
                    }}
                    width=""
                    onChange={(e) => {
                      searchHanlde(e);
                    }}
                    placeholder="search user here..."
                    // onRequestSearch={(e)=>{searchHanlde(e)}}
                  />
                  <SearchIcon
                    fontSize="medium"
                    style={{ marginLeft: "-30px" }}
                    color="black"
                  ></SearchIcon>
                  {/* <SearchBar
                    height="10px"
                    width=""
                    onChange={(e) => {
                      searchHanlde(e);
                    }}
                    placeholder="search user here..."
                    // onRequestSearch={(e)=>{searchHanlde(e)}}
                    autoFocus
                  /> */}
                </MDTypography>
              </MDBox>
            </MDBox>
            {users.loading == false ||
            users.error == "" ||
            finaldata !== null ? (
              <MDBox pt={1}>
                {finaldata == null ? (
                  <MDBox align="center" mt={10}>
                    <MDTypography
                      component="a"
                      href="#"
                      variant="button"
                      color="text"
                      fontWeight="medium"
                    >
                      No Data Found
                    </MDTypography>
                  </MDBox>
                ) : (
                  <DataTable
                    table={{
                      columns: [
                        {
                          Header: "name",
                          accessor: "author",
                          width: "25%",
                          align: "left",
                        },
                        {
                          Header: "follower",
                          accessor: "follower",
                          align: "left",
                        },
                        {
                          Header: "following",
                          accessor: "following",
                          align: "left",
                        },
                        { Header: "post", accessor: "post", align: "left" },
                        {
                          Header: "join date",
                          accessor: "employed",
                          align: "center",
                        },
                        {
                          Header: "action",
                          accessor: "action",
                          align: "center",
                        },
                      ],
                      rows:
                        finaldata === null
                          ? "no data found"
                          : finaldata?.map((e) => {
                              return {
                                follower: (
                                  <MDTypography
                                    component="a"
                                    href="#"
                                    variant="button"
                                    color="text"
                                    fontWeight="medium"
                                  >
                                    {e.follower_count}
                                  </MDTypography>
                                ),
                                following: (
                                  <MDTypography
                                    component="a"
                                    href="#"
                                    variant="button"
                                    color="text"
                                    fontWeight="medium"
                                  >
                                    {e.following_count}
                                  </MDTypography>
                                ),
                                post: (
                                  <MDTypography
                                    component="a"
                                    href="#"
                                    variant="button"
                                    color="text"
                                    fontWeight="medium"
                                  >
                                    {e.post_count}
                                  </MDTypography>
                                ),
                                author: (
                                  <Author
                                    image={
                                      e.profile == null ? profile : e.profile
                                    }
                                    name={`${e.fname + " " + e.lname}`}
                                    email={e.email}
                                  />
                                ),
                                employed: (
                                  <MDTypography
                                    component="a"
                                    href="#"
                                    variant="caption"
                                    color="text"
                                    fontWeight="medium"
                                  >
                                    {e.created_date.slice(0, 10)}
                                  </MDTypography>
                                ),
                                action: (
                                  <MDBox ml={-1}>
                                    {e.isblocked === "false" && (
                                      <button
                                        className="btn btn-sm btn-primary"
                                        onClick={() => {
                                          setIsblockOpen(true);
                                          setblockemail(e.email);
                                        }}
                                      >
                                        &nbsp;BLOCK&nbsp;
                                      </button>
                                    )}
                                    <button
                                      className="btn btn-sm btn-danger"
                                      onClick={() => {
                                        setIsOpen(true);
                                        setuserid(e.id);
                                      }}
                                      style={{ marginLeft: "10PX" }}
                                    >
                                      DELETE
                                    </button>
                                  </MDBox>
                                ),
                              };
                            }),
                    }}
                    isSorted={false}
                    entriesPerPage={false}
                    showTotalEntries={false}
                  />
                )}
              </MDBox>
            ) : (
              <MDBox align="center" mt={10}>
                <MDTypography
                  component="a"
                  href="#"
                  variant="button"
                  color="text"
                  fontWeight="medium"
                >
                  Loadin...
                </MDTypography>
              </MDBox>
            )}

            {/* </Card> */}
          </Grid>
        </Grid>
      </MDBox>
      <Footer />
    </DashboardLayout>
  );
}

export default Tables;
