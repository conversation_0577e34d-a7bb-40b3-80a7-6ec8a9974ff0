import React, { useEffect, useState } from "react";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import { useCookies } from "react-cookie";
import { Link } from "react-router-dom";
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import DataTable from "examples/Tables/DataTable";
import moment from "moment";
import { Grid } from "@mui/material";
import { useMediaQuery } from "@material-ui/core";
import { useTheme } from "@material-ui/core/styles";
import Select from "react-select";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import SearchIcon from "@mui/icons-material/Search";
import CircularProgress from "@mui/material/CircularProgress";

function SearchHistory() {
  const [cookie] = useCookies();
  const token = cookie.admin;
  const [loading, setLoading] = useState(true);
  const [languageLilst, setLanguageList] = useState([]);
  const [data, setData] = useState([]);
  const [selectedLanguage, setSelectedLanguage] = useState({
    label: "All",
    value: "",
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [noPages, setNoPages] = useState([]);
  const [currentPages, setCurrentPages] = useState([]);
  const [pages, setPages] = useState(0);
  const [searchKey, setSearchKey] = useState(null);
  const [sortOrder, setSortOrder] = useState();
  const [maxCount, setMaxCount] = useState(0);
  const [searchMaxValue, setSearchMaxValue] = useState(null);
  const [searchMinValue, setSearchMinValue] = useState(null);

  const sortingMenu = [
    { label: "high to low", value: "desc" },
    { label: "low to high", value: "asc" },
  ];

  useEffect(() => {
    getHistoryData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    currentPage,
    selectedLanguage,
    searchKey,
    sortOrder,
    searchMinValue,
    searchMaxValue,
  ]);

  useEffect(() => {
    let currentpages = [];
    if (currentPage >= 10) {
      const s = (Math.ceil(currentPage / 10) - 1) * 10;
      currentpages = [...noPages.splice(s, 10)];
    } else {
      currentpages = [...noPages.splice(0, 10)];
    }
    setCurrentPages(currentpages);
  }, [noPages, currentPage]);

  const getHistoryData = async () => {
    setLoading(true);
    let query = "";
    if (searchKey) {
      if (query?.length > 0) {
        query += `&key=${searchKey}`;
      } else {
        query += `?key=${searchKey}`;
      }
    }

    if (selectedLanguage.value !== "") {
      if (query?.length > 0) {
        query += `&language=${selectedLanguage.value}`;
      } else {
        query += `?language=${selectedLanguage.value}`;
      }
    }

    if (sortOrder && sortOrder.value !== "") {
      if (query.length > 0) {
        query += `&sort=${sortOrder.value}`;
      } else {
        query += `?sort=${sortOrder.value}`;
      }
    }

    if (searchMinValue > 0 && searchMaxValue > 0) {
      if (query.length > 0) {
        query += `&countRange=${searchMinValue},${searchMaxValue}`;
      } else {
        query += `?countRange=${searchMinValue},${searchMaxValue}`;
      }
    } else if (searchMinValue > 0) {
      if (query.length > 0) {
        query += `&countRange=${searchMinValue}`;
      } else {
        query += `?countRange=${searchMinValue}`;
      }
    }

    const url = `${window.path}/getsearchhistory/${currentPage}${query}`;
    fetch(url, { headers: { auth: token } })
      .then((response) => response.json())
      .then((res) => {
        const startIndex = (currentPage - 1) * 10;
        const result = res.data.map((r, index) => ({
          ...r,
          sr: startIndex + index + 1,
        }));
        setData(result);
        const maxValue = res.maxCount;

        setMaxCount(maxValue);

        const languagesList = [
          { label: "All", value: "" },
          ...res.languages.map((item) => ({
            label: item?.language,
            value: item?.lang_id,
          })),
        ];
        setLanguageList(languagesList);

        const totalLength = res?.total || 0;
        const totalPages = [];
        const pags = totalLength > 0 ? Math.ceil(totalLength / 10) : 0;
        setPages(pags);
        if (pags > 0) {
          for (let i = 1; i <= pags; i += 1) {
            totalPages.push(i);
          }
        }
        setNoPages(totalPages);
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleChangeLanguage = (e) => {
    setCurrentPage(1);
    setSelectedLanguage(e);
  };

  const prev = (e) => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const searchHanlde = (e) => {
    e.preventDefault();
    if (e.target.value) {
      setCurrentPage(1);

      setSearchKey(e.target.value);
    } else {
      setSearchKey(null);
    }
  };

  const next = (e) => {
    if (currentPage < pages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handleChangeCountSort = (e) => {
    setSortOrder(e);
    setCurrentPage(1);
  };

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox pt={-4} pb={3}>
        <Grid container spacing={0}>
          <Grid item xs={12}>
            <MDBox
              display={isMobile ? "block" : "flex"}
              align=""
              mt={-1}
              style={isMobile ? {} : { justifyContent: "space-between" }}
            >
              <div
                style={{
                  width: "30%",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <MDTypography
                  variant="h6"
                  fontWeight="medium"
                  width={isMobile ? "90%" : "70%"}
                >
                  <Select
                    placeholder="Select language"
                    onChange={handleChangeLanguage}
                    className="custom-select"
                    options={languageLilst}
                    value={selectedLanguage}
                    isSearchable={false}
                    width={40}
                  />
                </MDTypography>
              </div>

              <div
                style={{
                  width: "40%",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <MDTypography
                  variant="h6"
                  fontWeight="medium"
                  width={isMobile ? "90%" : "70%"}
                >
                  <Select
                    placeholder="Select count sort"
                    onChange={handleChangeCountSort}
                    className="custom-select"
                    options={sortingMenu}
                    value={sortOrder}
                    isSearchable={false}
                    isClearable
                    width={40}
                  />
                </MDTypography>
              </div>

              <div
                style={{
                  width: "50%",
                  padding: "0px 20px",
                }}
              >
                <div>
                  <div style={{ fontSize: "15px" }}>Enter count range</div>
                  <div
                    style={{ display: "flex", justifyContent: "space-between" }}
                  >
                    <input
                      style={{
                        width: "220px",
                        fontSize: "15px",
                        paddingLeft: "10PX",
                        height: "40px",
                        borderRadius: "10px",
                        border: "1px solid #7b809a",
                        marginLeft: isMobile ? "7%" : "",
                        marginTop: isMobile ? "10px" : "0px",
                      }}
                      value={searchMinValue ?? ""}
                      onChange={(e) => {
                        if (e.target.value === "") {
                          setSearchMinValue("");
                          setSearchMaxValue("");
                        } else {
                          setSearchMaxValue(parseInt(e.target.value, 10) + 100);
                          setSearchMinValue(parseInt(e.target.value, 10));
                        }
                        setCurrentPage(1);
                      }}
                      placeholder="Enter min search count"
                    />
                    <input
                      disabled={!searchMinValue}
                      style={{
                        width: "220px",
                        fontSize: "15px",
                        paddingLeft: "10PX",
                        height: "40px",
                        borderRadius: "10px",
                        border: "1px solid #7b809a",
                        marginLeft: isMobile ? "7%" : "",
                        marginTop: isMobile ? "10px" : "0px",
                      }}
                      value={searchMaxValue ?? ""}
                      onChange={(e) => {
                        if (e.target.value === "") {
                          setSearchMaxValue("");
                        } else {
                          setSearchMaxValue(parseInt(e.target.value, 10));
                        }
                        setCurrentPage(1);
                      }}
                      placeholder="Enter max search count"
                    />
                  </div>
                  <div
                    style={{
                      fontSize: "15px",
                      display: "flex",
                      justifyContent: "flex-end",
                    }}
                  >
                    Max Search Count&nbsp;:&nbsp;{maxCount}
                  </div>
                </div>
              </div>

              <div
                style={{
                  width: "44%",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <input
                  style={{
                    fontSize: "15px",
                    paddingLeft: "10PX",
                    height: "40px",
                    borderRadius: "10px",
                    border: "1px solid #7b809a",
                    width: "100%",
                    marginLeft: isMobile ? "7%" : "",
                    marginTop: isMobile ? "10px" : "0px",
                  }}
                  onChange={(e) => {
                    searchHanlde(e);
                  }}
                  placeholder="search key here..."
                />
                <SearchIcon
                  fontSize="medium"
                  style={{ marginLeft: "-30px" }}
                  color="black"
                />
              </div>
            </MDBox>
            {!loading && data.length > 0 ? (
              <MDBox pt={1}>
                <DataTable
                  table={{
                    columns: [
                      {
                        Header: "Sr. No.",
                        accessor: "sr",
                        align: "left",
                        width: "5%",
                      },
                      {
                        Header: "Search movie name",
                        accessor: "searchKeyWord",
                        align: "left",
                      },
                      {
                        Header: "count",
                        accessor: "count",
                        align: "left",
                      },
                      {
                        Header: "language",
                        accessor: "language",
                        align: "left",
                      },

                      {
                        Header: "last search on",
                        accessor: "lastSearch",
                        align: "left",
                      },
                    ],
                    rows:
                      data.length > 0
                        ? data?.map((line) => ({
                            sr: (
                              <MDTypography
                                component="a"
                                href="#"
                                variant="button"
                                color="text"
                                fontWeight="medium"
                              >
                                {line.sr}
                              </MDTypography>
                            ),
                            language: (
                              <MDTypography
                                component="a"
                                href="#"
                                variant="button"
                                color="text"
                                fontWeight="medium"
                              >
                                {line.language}
                              </MDTypography>
                            ),
                            searchKeyWord: (
                              <MDTypography
                                component="a"
                                href="#"
                                variant="button"
                                color="text"
                                fontWeight="medium"
                              >
                                {line.search_key_word}
                              </MDTypography>
                            ),
                            count: (
                              <MDTypography
                                component="a"
                                href="#"
                                variant="button"
                                color="text"
                                fontWeight="medium"
                              >
                                {line.count}
                              </MDTypography>
                            ),
                            lastSearch: (
                              <MDTypography
                                component="a"
                                href="#"
                                variant="button"
                                color="text"
                                fontWeight="medium"
                              >
                                {moment(line.updated_date)
                                  .add(330, "minutes")
                                  .format("MMMM D, YYYY h:mm:ss A")}
                              </MDTypography>
                            ),
                          }))
                        : "no data found",
                  }}
                  isSorted={false}
                  entriesPerPage={false}
                  showTotalEntries={false}
                />
              </MDBox>
            ) : (
              <div>
                {loading ? (
                  <div style={{ display: "flex", justifyContent: "center" }}>
                    <CircularProgress color="secondary" />
                  </div>
                ) : (
                  <MDBox pt={1}>
                    <MDBox align="center" mt={10}>
                      <MDTypography
                        component="a"
                        href="#"
                        variant="button"
                        color="text"
                        fontWeight="medium"
                      >
                        No Data Found
                      </MDTypography>
                    </MDBox>
                  </MDBox>
                )}
              </div>
            )}

            {!loading && data.length > 0 ? (
              <MDBox
                style={{
                  margin: "20px 0px 20px 10px",
                  position: "relative",
                  bottom: "-20px",
                  display: "flex",
                  justifyContent: "center",
                }}
              >
                <table style={{ height: "10px", border: "1px solid #c7c7c7" }}>
                  <thead>
                    <tr style={{ display: "flex", width: "100%" }}>
                      <td
                        style={{
                          width: "40px",
                          textAlign: "center",
                          borderRight: "1px solid #b1b5b2",
                        }}
                      >
                        <button
                          type="button"
                          style={{
                            cursor: "pointer",
                            color: "#646669",
                            background: "none",
                            border: "none",
                            padding: 0,
                          }}
                          aria-label="Previous 10 pages"
                          onClick={() => {
                            if (currentPage - 10 >= 1) {
                              setCurrentPage(
                                Math.floor(currentPage / 10) * 10 - 9
                              );
                            }
                          }}
                        >
                          <KeyboardArrowLeftIcon
                            style={{ marginRight: "-10px" }}
                          />
                          <KeyboardArrowLeftIcon />
                        </button>
                      </td>
                      <td
                        style={{
                          width: "40px",
                          textAlign: "center",
                          cursor: "pointer",
                        }}
                      >
                        <button
                          type="button"
                          onClick={prev}
                          style={{
                            width: "40px",
                            textAlign: "center",
                            cursor: "pointer",
                            color: "#646669",
                            border: "none",
                          }}
                        >
                          <KeyboardArrowLeftIcon />
                        </button>
                      </td>
                      {currentPages[0] > 1 ? (
                        <>
                          <td
                            style={{
                              width: "40px",
                              textAlign: "center",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              borderLeft: "1px solid #b1b5b2",
                              cursor: "pointer",
                              fontSize: "17px",
                              color: "#646669",
                            }}
                          >
                            <button
                              type="button"
                              onClick={() => setCurrentPage(1)}
                              style={{
                                width: "40px",
                                background: "none",
                                border: "none",
                              }}
                            >
                              1
                            </button>
                          </td>
                          <td
                            style={{
                              width: "45px",
                              textAlign: "center",
                              borderLeft: "1px solid #b1b5b2",
                              cursor: "pointer",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              fontSize: "17px",
                              color: "#646669",
                            }}
                          >
                            ...
                          </td>
                        </>
                      ) : (
                        ""
                      )}
                      {currentPages?.map((e) => (
                        <td
                          style={{
                            borderLeft: "1px solid #b1b5b2",
                            textAlign: "center",
                            cursor: "pointer",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            fontSize: "17px",
                            backgroundColor: `${
                              e === currentPage ? "#3993EE" : ""
                            }`,
                          }}
                        >
                          {" "}
                          <button
                            style={{
                              padding: "0px 10px",
                              background: "none",
                              border: "none",
                            }}
                            onClick={() => setCurrentPage(e)}
                            type="button"
                          >
                            <span>
                              {" "}
                              <Link
                                to=""
                                style={{
                                  position: "absolute",
                                  color: `${
                                    e === currentPage ? "white" : "#646669"
                                  }`,
                                }}
                                className="p-1   position-relative  text-underline-hover"
                              >
                                {e}
                              </Link>
                            </span>
                          </button>
                        </td>
                      ))}

                      {currentPages[currentPages.length - 1] < pages ? (
                        <>
                          <td
                            style={{
                              width: "40px",
                              textAlign: "center",
                              borderLeft: "1px solid #b1b5b2",
                              cursor: "pointer",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              fontSize: "17px",
                              color: "#646669",
                            }}
                          >
                            ...
                          </td>
                          <td
                            style={{
                              textAlign: "center",
                              borderLeft: "1px solid #b1b5b2",
                              cursor: "pointer",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              fontSize: "17px",
                              color: "#646669",
                            }}
                          >
                            <button
                              style={{
                                background: "none",
                                border: "none",
                                padding: "0px 10px",
                              }}
                              onClick={() => {
                                setCurrentPage(pages);
                              }}
                              type="button"
                            >
                              {pages}
                            </button>
                          </td>
                        </>
                      ) : (
                        ""
                      )}

                      <td
                        style={{
                          width: "40px",
                          textAlign: "center",
                          borderLeft: "1px solid #b1b5b2",
                          cursor: "pointer",
                          color: "#646669",
                        }}
                        // onClick={next}
                      >
                        {" "}
                        <button
                          style={{
                            width: "40px",
                            background: "none",
                            border: "none",
                          }}
                          type="button"
                          onClick={next}
                        >
                          <NavigateNextIcon />{" "}
                        </button>
                      </td>
                      <td
                        style={{
                          width: "40px",
                          textAlign: "center",
                          borderLeft: "1px solid #b1b5b2",
                          cursor: "pointer",
                          color: "#646669",
                        }}
                      >
                        <button
                          style={{
                            background: "none",
                            border: "none",
                            width: "40px",
                          }}
                          type="button"
                          onClick={() => {
                            currentPage + 10 <= pages &&
                              setCurrentPage(
                                Math.ceil(currentPage / 10) * 10 + 1
                              );
                          }}
                        >
                          <NavigateNextIcon style={{ marginRight: "-10px" }} />
                          <NavigateNextIcon />
                        </button>{" "}
                      </td>
                    </tr>
                  </thead>
                </table>
              </MDBox>
            ) : (
              ""
            )}
          </Grid>
        </Grid>
      </MDBox>
    </DashboardLayout>
  );
}
export default SearchHistory;
