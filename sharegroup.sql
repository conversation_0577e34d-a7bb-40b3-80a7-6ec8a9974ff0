-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.6.6deb5ubuntu0.5
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jul 12, 2025 at 06:08 AM
-- Server version: 5.7.42-0ubuntu0.18.04.1
-- PHP Version: 7.2.34-36+ubuntu18.04.1+deb.sury.org+1

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `movie_site`
--

-- --------------------------------------------------------

--
-- Table structure for table `block_user_tbl`
--

CREATE TABLE `block_user_tbl` (
  `id` int(11) NOT NULL,
  `source_id` int(11) NOT NULL,
  `dest_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `category_tbl`
--

CREATE TABLE `category_tbl` (
  `cat_id` tinyint(5) NOT NULL,
  `cat_name` varchar(30) NOT NULL,
  `sort_order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `device_tbl`
--

CREATE TABLE `device_tbl` (
  `id` int(11) NOT NULL,
  `deviceId` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `follower_tbl`
--

CREATE TABLE `follower_tbl` (
  `id` int(50) NOT NULL,
  `source_id` int(50) NOT NULL,
  `source_name` varchar(50) NOT NULL,
  `dest_id` int(50) NOT NULL,
  `dest_name` varchar(50) NOT NULL,
  `dest_profile` varchar(150) NOT NULL,
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `source_profile` varchar(150) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `genre_tbl`
--

CREATE TABLE `genre_tbl` (
  `id` tinyint(4) NOT NULL,
  `genre` varchar(50) NOT NULL,
  `sort_order` smallint(6) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `language_tbl`
--

CREATE TABLE `language_tbl` (
  `lang_id` tinyint(5) NOT NULL,
  `language` varchar(30) NOT NULL,
  `langorder` int(3) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `like_tbl`
--

CREATE TABLE `like_tbl` (
  `id` bigint(255) NOT NULL,
  `source_id` int(255) NOT NULL,
  `source_name` text NOT NULL,
  `source_profile` text NOT NULL,
  `dest_id` int(255) NOT NULL,
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `mxvideo`
--

CREATE TABLE `mxvideo` (
  `id` int(11) NOT NULL,
  `title` varchar(100) DEFAULT NULL,
  `link` varchar(500) DEFAULT NULL,
  `thumbnail` varchar(500) DEFAULT NULL,
  `epno` int(11) NOT NULL,
  `movieid` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `iscrawl` varchar(6) NOT NULL DEFAULT 'false',
  `sub_title` varchar(500) DEFAULT NULL,
  `is_trailer` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `not_support`
--

CREATE TABLE `not_support` (
  `id` int(11) NOT NULL,
  `name` varchar(500) COLLATE utf8_unicode_ci NOT NULL,
  `link` varchar(500) COLLATE utf8_unicode_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `post_report_tbl`
--

CREATE TABLE `post_report_tbl` (
  `id` int(20) NOT NULL,
  `source_id` int(20) NOT NULL,
  `dest_post_id` int(20) NOT NULL,
  `reason` varchar(500) NOT NULL,
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `post_tbl`
--

CREATE TABLE `post_tbl` (
  `id` int(50) NOT NULL,
  `source_id` int(50) NOT NULL,
  `source_name` varchar(50) NOT NULL,
  `source_profile` varchar(100) DEFAULT NULL,
  `title` varchar(100) NOT NULL,
  `link` text NOT NULL,
  `ismovie` tinyint(1) NOT NULL,
  `season` tinyint(4) DEFAULT '0',
  `caption` longtext NOT NULL,
  `views` bigint(200) NOT NULL,
  `thumbnail` varchar(100) DEFAULT NULL,
  `trailer_link` text,
  `report_count` int(15) DEFAULT NULL,
  `imdb_rating` float DEFAULT NULL,
  `google_rating` tinyint(3) DEFAULT NULL,
  `cat_id` varchar(100) NOT NULL,
  `genre_id` varchar(20) DEFAULT NULL,
  `lang_id` varchar(100) DEFAULT NULL,
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `isrestric` varchar(10) NOT NULL DEFAULT 'false',
  `iscopyright` varchar(6) NOT NULL DEFAULT 'false',
  `isprivate` varchar(6) NOT NULL DEFAULT 'false',
  `istrailrestric` varchar(6) NOT NULL DEFAULT 'false',
  `isrestricmovie` varchar(6) NOT NULL DEFAULT 'false',
  `isembeded` varchar(6) NOT NULL DEFAULT 'false',
  `ismoviecrawl` varchar(6) NOT NULL DEFAULT 'false',
  `istrailercrawl` varchar(6) NOT NULL DEFAULT 'false',
  `isUltra` varchar(6) NOT NULL DEFAULT 'false',
  `isverified` varchar(6) NOT NULL DEFAULT 'true',
  `free_ep` tinyint(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `search_history_tbl`
--

CREATE TABLE `search_history_tbl` (
  `id` int(50) NOT NULL,
  `lang_id` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `search_key_word` varchar(500) COLLATE utf8_unicode_ci DEFAULT NULL,
  `count` int(100) DEFAULT NULL,
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tracking_tbl`
--

CREATE TABLE `tracking_tbl` (
  `deviceId` int(11) NOT NULL,
  `movieid` int(11) NOT NULL,
  `userid` int(11) DEFAULT NULL,
  `id` int(11) NOT NULL,
  `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_report_tbl`
--

CREATE TABLE `user_report_tbl` (
  `id` int(11) NOT NULL,
  `source_id` int(11) NOT NULL,
  `source_name` text COLLATE utf8_unicode_ci NOT NULL,
  `source_profile` text COLLATE utf8_unicode_ci NOT NULL,
  `reason` text COLLATE utf8_unicode_ci NOT NULL,
  `dest_id` int(11) NOT NULL,
  `dest_name` text COLLATE utf8_unicode_ci NOT NULL,
  `dest_profile` text COLLATE utf8_unicode_ci NOT NULL,
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_tbl`
--

CREATE TABLE `user_tbl` (
  `id` int(100) NOT NULL,
  `fname` varchar(25) NOT NULL,
  `lname` varchar(25) NOT NULL,
  `email` varchar(70) NOT NULL,
  `gender` char(1) NOT NULL,
  `post_count` int(50) NOT NULL,
  `follower_count` int(50) NOT NULL,
  `following_count` int(50) NOT NULL,
  `profile` varchar(1000) DEFAULT NULL,
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `isblocked` varchar(5) NOT NULL DEFAULT 'false'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `block_user_tbl`
--
ALTER TABLE `block_user_tbl`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `category_tbl`
--
ALTER TABLE `category_tbl`
  ADD PRIMARY KEY (`cat_id`);

--
-- Indexes for table `device_tbl`
--
ALTER TABLE `device_tbl`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `follower_tbl`
--
ALTER TABLE `follower_tbl`
  ADD PRIMARY KEY (`id`),
  ADD KEY `following_constraint` (`source_id`),
  ADD KEY `follower_constraint` (`dest_id`);

--
-- Indexes for table `genre_tbl`
--
ALTER TABLE `genre_tbl`
  ADD PRIMARY KEY (`id`),
  ADD KEY `id` (`id`);

--
-- Indexes for table `language_tbl`
--
ALTER TABLE `language_tbl`
  ADD PRIMARY KEY (`lang_id`);

--
-- Indexes for table `like_tbl`
--
ALTER TABLE `like_tbl`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_source` (`source_id`),
  ADD KEY `post_dest` (`dest_id`);

--
-- Indexes for table `mxvideo`
--
ALTER TABLE `mxvideo`
  ADD PRIMARY KEY (`id`),
  ADD KEY `movieid` (`movieid`);

--
-- Indexes for table `not_support`
--
ALTER TABLE `not_support`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `post_report_tbl`
--
ALTER TABLE `post_report_tbl`
  ADD PRIMARY KEY (`id`),
  ADD KEY `reporter` (`source_id`),
  ADD KEY `reported_post` (`dest_post_id`);

--
-- Indexes for table `post_tbl`
--
ALTER TABLE `post_tbl`
  ADD PRIMARY KEY (`id`),
  ADD KEY `lang_tbl_constraint` (`lang_id`),
  ADD KEY `cat_tbl_constraint` (`cat_id`),
  ADD KEY `user_tbl_constraint` (`source_id`),
  ADD KEY `genre_tbl_constraint` (`genre_id`),
  ADD KEY `lang_id` (`lang_id`),
  ADD KEY `genre_id` (`genre_id`),
  ADD KEY `source_id` (`source_id`);

--
-- Indexes for table `search_history_tbl`
--
ALTER TABLE `search_history_tbl`
  ADD PRIMARY KEY (`id`),
  ADD KEY `lang_id` (`lang_id`);

--
-- Indexes for table `tracking_tbl`
--
ALTER TABLE `tracking_tbl`
  ADD PRIMARY KEY (`id`),
  ADD KEY `deviceId` (`deviceId`),
  ADD KEY `movieid` (`movieid`);

--
-- Indexes for table `user_report_tbl`
--
ALTER TABLE `user_report_tbl`
  ADD PRIMARY KEY (`id`),
  ADD KEY `dest_id` (`dest_id`),
  ADD KEY `source_id` (`source_id`);

--
-- Indexes for table `user_tbl`
--
ALTER TABLE `user_tbl`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `block_user_tbl`
--
ALTER TABLE `block_user_tbl`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=410;
--
-- AUTO_INCREMENT for table `category_tbl`
--
ALTER TABLE `category_tbl`
  MODIFY `cat_id` tinyint(5) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=74;
--
-- AUTO_INCREMENT for table `device_tbl`
--
ALTER TABLE `device_tbl`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5150399;
--
-- AUTO_INCREMENT for table `follower_tbl`
--
ALTER TABLE `follower_tbl`
  MODIFY `id` int(50) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13446;
--
-- AUTO_INCREMENT for table `genre_tbl`
--
ALTER TABLE `genre_tbl`
  MODIFY `id` tinyint(4) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=90;
--
-- AUTO_INCREMENT for table `language_tbl`
--
ALTER TABLE `language_tbl`
  MODIFY `lang_id` tinyint(5) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=68;
--
-- AUTO_INCREMENT for table `like_tbl`
--
ALTER TABLE `like_tbl`
  MODIFY `id` bigint(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=118286;
--
-- AUTO_INCREMENT for table `mxvideo`
--
ALTER TABLE `mxvideo`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5680;
--
-- AUTO_INCREMENT for table `not_support`
--
ALTER TABLE `not_support`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `post_report_tbl`
--
ALTER TABLE `post_report_tbl`
  MODIFY `id` int(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6965;
--
-- AUTO_INCREMENT for table `post_tbl`
--
ALTER TABLE `post_tbl`
  MODIFY `id` int(50) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17823;
--
-- AUTO_INCREMENT for table `search_history_tbl`
--
ALTER TABLE `search_history_tbl`
  MODIFY `id` int(50) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=829619;
--
-- AUTO_INCREMENT for table `tracking_tbl`
--
ALTER TABLE `tracking_tbl`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18282053;
--
-- AUTO_INCREMENT for table `user_report_tbl`
--
ALTER TABLE `user_report_tbl`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=276;
--
-- AUTO_INCREMENT for table `user_tbl`
--
ALTER TABLE `user_tbl`
  MODIFY `id` int(100) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=724849;
--
-- Constraints for dumped tables
--

--
-- Constraints for table `follower_tbl`
--
ALTER TABLE `follower_tbl`
  ADD CONSTRAINT `follower_constraint` FOREIGN KEY (`dest_id`) REFERENCES `user_tbl` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `following_constraint` FOREIGN KEY (`source_id`) REFERENCES `user_tbl` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `like_tbl`
--
ALTER TABLE `like_tbl`
  ADD CONSTRAINT `post_dest` FOREIGN KEY (`dest_id`) REFERENCES `post_tbl` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `user_source` FOREIGN KEY (`source_id`) REFERENCES `user_tbl` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `mxvideo`
--
ALTER TABLE `mxvideo`
  ADD CONSTRAINT `mxvideo_ibfk_1` FOREIGN KEY (`movieid`) REFERENCES `post_tbl` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `post_report_tbl`
--
ALTER TABLE `post_report_tbl`
  ADD CONSTRAINT `reported_post` FOREIGN KEY (`dest_post_id`) REFERENCES `post_tbl` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `reporter` FOREIGN KEY (`source_id`) REFERENCES `user_tbl` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `post_tbl`
--
ALTER TABLE `post_tbl`
  ADD CONSTRAINT `user` FOREIGN KEY (`source_id`) REFERENCES `user_tbl` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `tracking_tbl`
--
ALTER TABLE `tracking_tbl`
  ADD CONSTRAINT `tracking_tbl_ibfk_1` FOREIGN KEY (`deviceId`) REFERENCES `device_tbl` (`id`);

--
-- Constraints for table `user_report_tbl`
--
ALTER TABLE `user_report_tbl`
  ADD CONSTRAINT `reported` FOREIGN KEY (`dest_id`) REFERENCES `user_tbl` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `reporter_user` FOREIGN KEY (`source_id`) REFERENCES `user_tbl` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
