/* eslint-disable */
import React, { useState, useEffect } from "react";
import { Link, useParams, useNavigate } from "react-router-dom";
import CardMedia from "@mui/material/CardMedia";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import MDButton from "components/MDButton";
import Footer from "examples/Footer";
import "bootstrap/dist/css/bootstrap.min.css";
import { ToastContainer, toast } from "react-toastify";
import { useCookies } from "react-cookie";
import "react-toastify/dist/ReactToastify.css";
import { useLocation, useSearchParams } from "react-router-dom";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import UpdateModal from "examples/Cards/ProjectCards/DefaultProjectCard/updateModal.js";
import Modal from "examples/Cards/ProjectCards/DefaultProjectCard/Modal.js";
import Select from "react-select";
import "./style.css";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import { useTheme } from "@material-ui/core/styles";
const index = () => {
  const { id } = useParams();
  let [searchParams, setSearchParams] = useSearchParams();
  const location = useLocation();
  const [cookie, getcookie] = useCookies();
  const [result, setresult] = useState(null);
  const [trailerLink, settrailerLink] = useState("");
  const token = cookie.admin;
  const [thumbpreview, setthumbpreview] = useState();
  const [tempGenre, setTempGenre] = useState();
  const navigate = useNavigate();
  const [data, setdata] = useState();
  const getdata = async () => {
    const res = await fetch(`${window.path}/adminshowposts/${id}`, {
      method: "GET",
      headers: {
        auth: token,
      },
    });
    const result = await res.json();
    setdata(result.result[0]);
    setTempGenre(result.result[0].genre);
    setthumbpreview(result.result[0].thumbnail);
    settrailerLink(result.result[0].trailer);
  };

  useEffect(() => {
    getdata();
  }, []);

  const del = async () => {
    let form = new FormData();
    form.append("dest", data?.post_id);
    form.append("id", data.source_id);
    const res = await fetch(`${window.path}/admindeletepost`, {
      method: "DELETE",
      headers: {
        auth: token,
      },
      body: form,
    });
    const delres = await res.json();
    if (delres.status === 1) {
      alert("deleted successfully");
      toast("deleted succefully");
      setTimeout(navigate("/posts"), 3000);
    } else {
      toast("invalid credintial");
    }
  };

  const trailerhandle = (e) => {
    settrailerLink(e.target.value);
  };
  const handlechange = (e) => {
    setdata({ ...data, [e.target.name]: e.target.value });
  };
  const handleChanged = (event, name) => {
    setdata({ ...data, [name.name]: event });
  };

  const handleChangedGenre = (selectedOption, name) => {
    // If the selectedOption is an array, extract the labels
    const updatedValue = Array.isArray(selectedOption)
      ? selectedOption.map((option) => option.label).join(",")
      : selectedOption.label;
    setTempGenre(selectedOption.value);
    setdata({ ...data, [name.name]: updatedValue });
  };

  function generateRandomString(length) {
    let result = "";
    const characters =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }

  const handlefile = (e) => {
    let file = e.target.files[0];
    const fileSizeInMB = file.size / (1024 * 1024);

    if (fileSizeInMB > 2) {
      return toast.error("File size is more than 2 MB", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 1000,
      });
    }
    console.log("files name", file);
    const originalFileName = file.name;
    const randomString = generateRandomString(15);
    const newFileName = randomString;
    const newFile = new File([file], newFileName, { type: file.type });
    console.log("file name after update", newFile);
    setdata({ ...data, [e.target.name]: newFile, fileName: newFileName });
    setthumbpreview(URL.createObjectURL(file));
  };

  const fetchthumb = async (e) => {
    const url = e.target.value;
    const regexp =
      /^.*(?:(?:youtu\.be\/|v\/|vi\/|u\/\w\/|embed\/|shorts\/)|(?:(?:watch)?\?v(?:i)?=|\&v(?:i)?=))([^#\&\?]*).*/;
    const videoid = url?.match(regexp);

    if (videoid != null) {
      const response =
        await fetch(`https://www.googleapis.com/youtube/v3/videos?id=${videoid[1]}&key=AIzaSyCHWLzPxk8hO6jW-vAH4zn-pG54ihUcYa8&part=snippet,contentDetails,status
    `);
      const jsonData = await response.json();

      if (
        jsonData?.items[0]?.status?.embeddable === false ||
        jsonData?.items[0]?.contentDetails?.contentRating?.ytRating ===
          "ytAgeRestricted" ||
        jsonData?.items[0]?.contentDetails?.regionRestriction?.allowed?.length >
          0 ||
        jsonData?.items.length == 0
      ) {
        // let err = document.getElementsByName("post");
        // err[0].style.border = "2px solid red";
        return toast.error("Trailer link is not supported", {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 1000,
        });
      }
    }
    // let err = document.getElementsByName("trailer");
    // err[0].style.border = "none";

    if (videoid != null) {
      if (url != data.trailer) {
        console.log("videoid in fetch thumb", videoid[1]);
        setdata({
          ...data,
          thumbnail: `https://img.youtube.com/vi/${videoid[1]}/0.jpg`,
        });
        setthumbpreview(`https://img.youtube.com/vi/${videoid[1]}/0.jpg`);
      }
    } else {
      setdata({ ...data, thumbnail: "" });
      setthumbpreview(null);
    }
  };
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdateOpen, setIsUpdateOpen] = useState(false);
  const [deletee, setdeletee] = useState(false);
  const [update, setUpdate] = useState(false);
  if (deletee == true) {
    del();
  }

  const getfilter = async () => {
    const fetchfilter = await fetch(`${window.path}/adminshowfilters`, {
      method: "POST",
      headers: {
        auth: token,
      },
    });
    const resp = await fetchfilter.json();
    setresult(resp);
  };
  useEffect(() => {
    getfilter();
  }, []);
  const cat = result?.category?.map((e) => {
    return { value: e.category, label: e.category };
  });
  const gen = result?.genre?.map((e) => {
    return { value: e.genre, label: e.genre };
  });
  const lang = result?.language?.map((e) => {
    return { value: e.language, label: e.language };
  });
  const updatePost = async () => {
    var cat = data.category;
    var lang = data.language;
    var ismovie = data.ismovie;
    if (Array.isArray(data.category)) {
      cat = data.category?.map((e) => {
        return e.value;
      });
      cat = cat.toString();
    }
    if (Array.isArray(data.language)) {
      lang = data.language?.map((e) => {
        return e.value;
      });
      lang = lang.toString();
    }
    if (typeof data.ismovie == "object") {
      ismovie = data.ismovie.value;
    }
    let isrestric = "false";
    let isembeded = "false";
    let isrestricmovie = "false";
    const url = data.link;
    const regexp =
      /^.*(?:(?:youtu\.be\/|v\/|vi\/|u\/\w\/|embed\/|shorts\/)|(?:(?:watch)?\?v(?:i)?=|\&v(?:i)?=))([^#\&\?]*).*/;
    const videoid = url?.match(regexp);

    if (videoid != null) {
      const response =
        await fetch(`https://www.googleapis.com/youtube/v3/videos?id=${videoid[1]}&key=AIzaSyCHWLzPxk8hO6jW-vAH4zn-pG54ihUcYa8&part=snippet,contentDetails,status
    `);
      const jsonData = await response.json();

      if (
        jsonData?.items[0]?.status?.embeddable === false ||
        jsonData?.items[0]?.contentDetails?.contentRating?.ytRating ===
          "ytAgeRestricted" ||
        jsonData?.items[0]?.contentDetails?.regionRestriction?.allowed?.length >
          0 ||
        jsonData?.items?.length == 0
      ) {
        if (jsonData?.items?.length == 0) {
          return toast.error("Movie link is not supported", {
            position: toast.POSITION.TOP_CENTER,
            autoClose: 1000,
          });
        }
        isrestric = "true";
        if (jsonData.items[0].status.embeddable === false) {
          isembeded = "true";
        }
        if (
          jsonData?.items[0]?.contentDetails?.regionRestriction?.allowed
            ?.length > 0
        ) {
          isrestricmovie = "true";
        }
      }
    }

    const formdata = new FormData();
    formdata.append("post_id", data.post_id);
    formdata.append("title", data.title);
    formdata.append("link", data.link);
    formdata.append("ismovie", ismovie);
    formdata.append("season", data.season);
    formdata.append("caption", data.caption);
    formdata.append("views", data.views);
    formdata.append("google_rating", data.google_rating);
    formdata.append("imdb_rating", data.imdb_rating);
    formdata.append("category", cat);
    formdata.append("language", lang);
    formdata.append("genre", tempGenre);
    formdata.append("trailer_link", trailerLink);
    formdata.append("thumbnail", data.thumbnail);
    formdata.append("isrestric", isrestric);
    formdata.append("isembeded", isembeded);
    formdata.append("isrestricmovie", isrestricmovie);
    formdata.append("free_ep", data.free_ep || 0);
    const senddata = await fetch(`${window.path}/adminupdatepost`, {
      method: "POST",
      headers: {
        auth: token,
      },
      body: formdata,
    });
    setUpdate(false);
    const res = await senddata.json();
    if (res.status === 1) {
      toast.success("post updated successfully !", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 300,
      });
      getdata();
    } else {
      toast.success("Internal Server Error Please Try Again !", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 200,
      });
    }
  };
  if (update != false) {
    updatePost();
  }
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const checkdata = async () => {
    if (data.title == "" || data.title == null) {
      let err = document.getElementsByName("title");
      err[0].style.border = "2px solid red";
      return toast.error("plese fill title properly !", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 500,
      });
    }
    if (data.caption == "" || data.caption == null) {
      let err = document.getElementsByName("title");
      err[0].style.border = "2px solid red";
      return toast.error("plese fill caption properly !", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 500,
      });
    }

    if (data.link == "" || data.link == null) {
      let err = document.getElementsByName("title");
      err[0].style.border = "2px solid red";
      return toast.error("plese fill movie link properly !", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 500,
      });
    } else {
      let isrestric = "false";
      let isembeded = "false";
      let isrestricmovie = "false";
      const url = data.link;
      const regexp =
        /^.*(?:(?:youtu\.be\/|v\/|vi\/|u\/\w\/|embed\/|shorts\/)|(?:(?:watch)?\?v(?:i)?=|\&v(?:i)?=))([^#\&\?]*).*/;
      const videoid = url?.match(regexp);

      if (videoid != null) {
        const response =
          await fetch(`https://www.googleapis.com/youtube/v3/videos?id=${videoid[1]}&key=AIzaSyCHWLzPxk8hO6jW-vAH4zn-pG54ihUcYa8&part=snippet,contentDetails,status
      `);
        const jsonData = await response.json();

        if (
          jsonData?.items[0]?.status?.embeddable === false ||
          jsonData?.items[0]?.contentDetails?.contentRating?.ytRating ===
            "ytAgeRestricted" ||
          jsonData?.items[0]?.contentDetails?.regionRestriction?.allowed
            ?.length > 0 ||
          jsonData?.items?.length == 0
        ) {
          if (jsonData?.items?.length == 0) {
            return toast.error("Movie link is not supported", {
              position: toast.POSITION.TOP_CENTER,
              autoClose: 1000,
            });
          }
          isrestric = "true";
          if (jsonData.items[0].status.embeddable === false) {
            isembeded = "true";
          }
          if (
            jsonData?.items[0]?.contentDetails?.regionRestriction?.allowed
              ?.length > 0
          ) {
            isrestricmovie = "true";
          }
        }
      }
    }
    if (data.google_rating == "" || data.google_rating == null) {
      let err = document.getElementsByName("title");
      err[0].style.border = "2px solid red";
      return toast.error("plese fill google rating properly !", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 500,
      });
    }
    if (data.imdb_rating == "" || data.imdb_rating == null) {
      let err = document.getElementsByName("title");
      err[0].style.border = "2px solid red";
      return toast.error("plese fill imdb rating properly !", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 500,
      });
    }
    if (data.views == "" || data.views == null) {
      let err = document.getElementsByName("title");
      err[0].style.border = "2px solid red";
      return toast.error("plese fill views properly !", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 500,
      });
    }

    if (data.google_rating > 100) {
      console.log("grating in if > 100");
      let err = document.getElementsByName("google_rating");
      err[0].style.border = "2px solid red";
      return toast.error("plese enter google rating below 100", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 500,
      });
    }

    if (data.imdb_rating > 10) {
      console.log("grating in if > 100");
      let err = document.getElementsByName("imdb_rating");
      err[0].style.border = "2px solid red";
      return toast.error("plese enter imdb google rating below 10", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 500,
      });
    }

    const url = trailerLink;
    const regexp =
      /^.*(?:(?:youtu\.be\/|v\/|vi\/|u\/\w\/|embed\/|shorts\/)|(?:(?:watch)?\?v(?:i)?=|\&v(?:i)?=))([^#\&\?]*).*/;
    const videoid = url?.match(regexp);
    if (videoid != null) {
      const response =
        await fetch(`https://www.googleapis.com/youtube/v3/videos?id=${videoid[1]}&key=AIzaSyCHWLzPxk8hO6jW-vAH4zn-pG54ihUcYa8&part=snippet,contentDetails,status
    `);
      const jsonData = await response.json();

      if (
        jsonData?.items[0]?.status?.embeddable === false ||
        jsonData?.items[0]?.contentDetails?.contentRating?.ytRating ===
          "ytAgeRestricted" ||
        jsonData?.items[0]?.contentDetails?.regionRestriction?.allowed?.length >
          0 ||
        jsonData.items.length == 0
      ) {
        // let err = document.getElementsByName("post");
        // err[0].style.border = "2px solid red";
        return toast.error("Trailer link is not supported", {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 1000,
        });
      }
    }
    setIsUpdateOpen(true);
  };
  return (
    <DashboardLayout>
      <DashboardNavbar />

      <section>
        <ToastContainer />
        {data ? (
          <div
            style={{
              display: isMobile ? "block" : "flex",
              justifyContent: "space-around",
              alignItems: "center",
              padding: "0px 20px 0px 20px",
            }}
          >
            <div style={{ width: "30%" }}>
              {isOpen && (
                <Modal setOpenModal={setIsOpen} setdelete={setdeletee} />
              )}
              {isUpdateOpen && (
                <UpdateModal
                  setIsUpdateOpen={setIsUpdateOpen}
                  setUpdate={setUpdate}
                />
              )}
              <img
                src={thumbpreview}
                style={{
                  width: "400px",
                  height: "400px",
                  objectFit: "contain",
                  border: "1px ",
                  borderRadius: "20px",
                  padding: "10px 20px 10px 20px",
                  backgroundColor: "white",
                }}
              />
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  marginTop: "30px",
                  width: "400px",
                  padding: "10px 20px 10px 20px",
                }}
              >
                <label
                  htmlFor="thumbnail"
                  style={{
                    backgroundColor: "white",
                    paddingBottom: "10px",
                    paddingLeft: "20px",
                    textTransform: "uppercase",
                    paddingRight: "20px",
                    paddingTop: "10px",
                    borderRadius: "10px",
                    fontSize: "15px",
                  }}
                >
                  <input
                    type="file"
                    name="thumbnail"
                    id="thumbnail"
                    onChange={handlefile}
                    hidden
                  />
                  Upload
                </label>
              </div>
            </div>
            <div
              style={{
                backgroundColor: "white",
                padding: "20px",
                width: isMobile ? "100%" : "70%",
                border: "1px ",
                borderRadius: "20px",
              }}
            >
              <div className="w-full" style={{ display: "flex" }}>
                <div className="" style={{ width: isMobile ? "100%" : "20%" }}>
                  <p>User Name : </p>
                </div>
                <div className="" style={{ width: isMobile ? "100%" : "80%" }}>
                  <p
                    className="border-0"
                    name="source_name"
                    style={{ color: "black" }}
                  >
                    {data.source_name}
                  </p>
                </div>
              </div>
              <hr />
              <div className="w-full" style={{ display: "flex" }}>
                <div className="" style={{ width: isMobile ? "100%" : "20%" }}>
                  <p>Post Title : </p>
                </div>
                <div
                  className=""
                  style={{
                    width: isMobile ? "100%" : "80%",
                    overflow: isMobile ? "auto" : "",
                  }}
                >
                  <input
                    className="border-0 w-100"
                    value={data.title}
                    onChange={handlechange}
                    name="title"
                    required
                  />
                </div>
              </div>
              <hr />
              <div className="w-full" style={{ display: "flex" }}>
                <div className="" style={{ width: isMobile ? "100%" : "20%" }}>
                  <p>Caption : </p>
                </div>
                <div className="" style={{ width: isMobile ? "100%" : "80%" }}>
                  <input
                    type="text"
                    className="border-0 w-100"
                    value={data.caption}
                    onChange={handlechange}
                    name="caption"
                  />
                </div>
              </div>
              <hr />
              <div className="w-full" style={{ display: "flex" }}>
                <div className="" style={{ width: isMobile ? "90%" : "20%" }}>
                  <p>Type : </p>
                </div>
                <div className="" style={{ width: isMobile ? "100%" : "80%" }}>
                  <Select
                    options={[
                      { label: "Movie", value: 1 },
                      { label: "Webseries", value: 0 },
                      { label: "Maxvideos", value: 2 },
                    ]}
                    name="ismovie"
                    onChange={handleChanged}
                    defaultValue={{
                      label:
                        data.ismovie == 1
                          ? "Movie"
                          : data.ismovie == 2
                          ? "Maxvideos"
                          : "Webseries",
                      value: parseInt(data.ismovie),
                    }}
                  />
                </div>
              </div>
              <hr />
              {data.ismovie == 0 ? (
                <>
                  <div className="w-full" style={{ display: "flex" }}>
                    <div
                      className=""
                      style={{ width: isMobile ? "100%" : "20%" }}
                    >
                      <p>Season : </p>
                    </div>
                    <div
                      className=""
                      style={{ width: isMobile ? "100%" : "80%" }}
                    >
                      <input
                        type="text"
                        className="border-0 w-100"
                        value={data.season}
                        onChange={handlechange}
                        name="season"
                      />
                    </div>
                  </div>
                  <hr />
                </>
              ) : (
                ""
              )}
              <div className="w-full" style={{ display: "flex" }}>
                <div className="" style={{ width: isMobile ? "100%" : "20%" }}>
                  <p>Language : </p>
                </div>
                <div className="" style={{ width: isMobile ? "100%" : "80%" }}>
                  {lang ? (
                    <Select
                      options={lang}
                      defaultValue={
                        Array.isArray(data.language) == false
                          ? lang?.filter(function (e) {
                              return data?.language
                                .split(",")
                                .includes(e.label);
                            })
                          : lang?.filter(function (e) {
                              return data?.language.includes(e.label);
                            })
                      }
                      isMulti
                      onChange={handleChanged}
                      name="language"
                    />
                  ) : (
                    ""
                  )}
                </div>
              </div>
              <hr />
              <div className="w-full" style={{ display: "flex" }}>
                <div className="" style={{ width: isMobile ? "100%" : "20%" }}>
                  <p>Category : </p>
                </div>
                <div className="" style={{ width: isMobile ? "100%" : "80%" }}>
                  {cat ? (
                    <Select
                      options={cat}
                      defaultValue={
                        Array.isArray(data.category) == false
                          ? cat?.filter(function (e) {
                              return data?.category
                                .split(",")
                                .includes(e.label);
                            })
                          : cat?.filter(function (e) {
                              return data?.category.includes(e.label);
                            })
                      }
                      isMulti
                      onChange={handleChanged}
                      name="category"
                    />
                  ) : (
                    ""
                  )}
                </div>
              </div>
              <hr />
              <div className="w-full" style={{ display: "flex" }}>
                <div className="" style={{ width: isMobile ? "100%" : "20%" }}>
                  <p>Genre : </p>
                </div>
                <div className="" style={{ width: isMobile ? "100%" : "80%" }}>
                  {gen ? (
                    <Select
                      options={gen}
                      defaultValue={
                        Array.isArray(data.genre) == false
                          ? gen?.filter(function (e) {
                              return data?.genre.split(",").includes(e.label);
                            })
                          : gen?.filter(function (e) {
                              return data?.genre.includes(e.label);
                            })
                      }
                      onChange={handleChangedGenre}
                      name="genre"
                    />
                  ) : (
                    ""
                  )}
                </div>
              </div>
              <hr />
              <div className="w-full" style={{ display: "flex" }}>
                <div className="" style={{ width: isMobile ? "100%" : "20%" }}>
                  <p>Trailer Link : </p>
                </div>
                <div className="" style={{ width: isMobile ? "100%" : "80%" }}>
                  <input
                    className="border-0 w-100  "
                    style={{ display: "flex", alignItems: "center" }}
                    value={trailerLink}
                    onBlur={fetchthumb}
                    onChange={trailerhandle}
                    name="trailer"
                    onClick={(event) => {
                      const isCtrlPressed = event.ctrlKey || event.metaKey;
                      if (isCtrlPressed) {
                        window.open(trailerLink, "_blank");
                      }
                    }}
                  />
                </div>
              </div>
              <hr />
              <div className="w-full" style={{ display: "flex" }}>
                <div className="" style={{ width: isMobile ? "100%" : "20%" }}>
                  <p>Movie Link : </p>
                </div>
                <div className="" style={{ width: isMobile ? "100%" : "80%" }}>
                  <input
                    className="border-0 w-100"
                    value={data.link}
                    onChange={handlechange}
                    name="link"
                    onClick={(event) => {
                      const isCtrlPressed = event.ctrlKey || event.metaKey;
                      if (isCtrlPressed) {
                        window.open(data.link, "_blank");
                      }
                    }}
                  />
                </div>
              </div>
              <hr />
              <div className="w-full" style={{ display: "flex" }}>
                <div className="" style={{ width: isMobile ? "100%" : "20%" }}>
                  <p>Google Rating :</p>
                </div>
                <div className="" style={{ width: isMobile ? "100%" : "80%" }}>
                  <input
                    className="border-0 w-100"
                    value={data.google_rating}
                    onChange={handlechange}
                    name="google_rating"
                  />
                </div>
              </div>
              <hr />
              <div className="w-full" style={{ display: "flex" }}>
                <div className="" style={{ width: isMobile ? "100%" : "20%" }}>
                  <p>IMDB Rating : </p>
                </div>
                <div className="" style={{ width: isMobile ? "100%" : "80%" }}>
                  <input
                    className="border-0 w-100"
                    value={data.imdb_rating}
                    onChange={handlechange}
                    name="imdb_rating"
                  />
                </div>
              </div>
              <hr />
              <div className="w-full" style={{ display: "flex" }}>
                <div className="" style={{ width: "20%" }}>
                  <p>Views : </p>
                </div>
                <div className="" style={{ width: "80%" }}>
                  <input
                    className="border-0 w-100"
                    value={data.views}
                    onChange={handlechange}
                    name="views"
                    type="number"
                  />
                </div>
              </div>
              <hr />
              {data.ismovie === 2 ? (
                <>
                  <div className="w-full" style={{ display: "flex" }}>
                    <div className="" style={{ width: "20%" }}>
                      <p>Free Episode: </p>
                    </div>
                    <div className="" style={{ width: "80%" }}>
                      <input
                        className="border-0 w-100"
                        value={data.free_ep}
                        onChange={handlechange}
                        name="free_ep"
                        type="number"
                      />
                    </div>
                  </div>
                  <hr />
                </>
              ) : (
                ""
              )}
              <div className="w-full" style={{ display: "flex" }}>
                <div className="" style={{ width: "20%" }}>
                  <p>Upload date : </p>
                </div>
                <div className="" style={{ width: "80%" }}>
                  <p className="border-0"> {data.created_date.slice(0, 10)}</p>
                </div>
              </div>
              <hr style={{ marginBottom: "15px" }} />
              <div
                className="w-full"
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <div className="" style={{ marginRight: "30px" }}>
                  {data.ismovie == 2 ? (
                    <MDButton
                      onClick={() => {
                        window.open(
                          `/posts/episodes/${data.post_id}`,
                          "_blank"
                        ); // Replace with your actual route
                      }}
                      variant="contained"
                      size="medium"
                      color="primary"
                      //  style={{paddingLeft:"10px",paddingRight:"10px" ,paddingTop:"5px",paddingBottom:"5px",backgroundColor:"red",color:"white",border:"none",borderRadius:"10px"}}
                    >
                      View Episodes
                    </MDButton>
                  ) : (
                    ""
                  )}
                </div>

                <div className="" style={{ marginRight: "30px" }}>
                  <MDButton
                    onClick={() => {
                      checkdata();
                    }}
                    variant="contained"
                    size="medium"
                    color="success"
                    //  style={{paddingLeft:"10px",paddingRight:"10px" ,paddingTop:"5px",paddingBottom:"5px",backgroundColor:"red",color:"white",border:"none",borderRadius:"10px"}}
                  >
                    Update
                  </MDButton>
                </div>
                <div className="">
                  <MDButton
                    onClick={() => {
                      setIsOpen(true);
                    }}
                    variant="contained"
                    size="medium"
                    color="error"
                    //   style={{paddingLeft:"10px",paddingRight:"10px" ,paddingTop:"5px",paddingBottom:"5px",backgroundColor:"red",color:"white",border:"none",borderRadius:"10px"}}
                  >
                    Delete
                  </MDButton>
                </div>
              </div>
            </div>
          </div>
        ) : (
          ""
        )}
      </section>
      <Footer />
    </DashboardLayout>
  );
};

export default index;
