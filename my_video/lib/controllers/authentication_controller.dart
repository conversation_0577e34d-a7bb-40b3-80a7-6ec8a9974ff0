import 'package:my_video/app_imports.dart';

class AuthenticationController extends GetxController {
  final AuthenticationRepository _authRepository =
      getIt<AuthenticationRepository>();

  final Rx<ApiStatus> _apiStatus = ApiStatus.initial.obs;
  final RxBool _isLoggedIn = false.obs;
  final RxString _userToken = ''.obs;
  final Rx<Map<String, dynamic>?> _userData = Rx<Map<String, dynamic>?>(null);

  ApiStatus get apiStatus => _apiStatus.value;
  bool get isLoggedIn => _isLoggedIn.value;
  String get userToken => _userToken.value;
  Map<String, dynamic>? get userData => _userData.value;

  // Form Controllers
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();
  final TextEditingController forgotPasswordEmailController =
      TextEditingController();

  // Form Keys
  final GlobalKey<FormState> loginFormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> signupFormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> forgotPasswordFormKey = GlobalKey<FormState>();

  // UI State
  final RxBool _isPasswordVisible = false.obs;
  final RxBool _isConfirmPasswordVisible = false.obs;
  final RxBool _acceptTerms = false.obs;

  bool get isPasswordVisible => _isPasswordVisible.value;
  bool get isConfirmPasswordVisible => _isConfirmPasswordVisible.value;
  bool get acceptTerms => _acceptTerms.value;

  @override
  void onInit() {
    super.onInit();
    _checkLoginStatus();
  }

  @override
  void onClose() {
    emailController.dispose();
    passwordController.dispose();
    nameController.dispose();
    confirmPasswordController.dispose();
    forgotPasswordEmailController.dispose();
    super.onClose();
  }

  void _checkLoginStatus() {
    final token = AppSharedPreference.getUserToken();
    final userData = AppSharedPreference.getUserData();

    if (token != null && token.isNotEmpty) {
      _userToken.value = token;
      _userData.value = userData;
      _isLoggedIn.value = true;
    }
  }

  Future<void> login() async {
    if (!loginFormKey.currentState!.validate()) {
      return;
    }

    try {
      _apiStatus.value = ApiStatus.loading;

      final hasInternet = await ConnectivityHelper.hasInternetConnection();
      if (!hasInternet) {
        _apiStatus.value = ApiStatus.noInternet;
        AppHelper.showToast('no_internet_connection'.tr, isError: true);
        return;
      }

      final result = await _authRepository.login(
        email: emailController.text.trim(),
        password: passwordController.text.trim(),
      );

      if (result['success'] == true) {
        final token = result['data']['token'] as String;
        final userData = result['data']['user'] as Map<String, dynamic>;

        // Save to local storage
        await AppSharedPreference.setUserToken(token);
        await AppSharedPreference.setUserData(userData);

        // Update observables
        _userToken.value = token;
        _userData.value = userData;
        _isLoggedIn.value = true;
        _apiStatus.value = ApiStatus.success;

        AppHelper.showToast('login_successful');

        // Navigate to main navigation
        Get.context!.go(AppRoutes.mainNavigation);
      } else {
        _apiStatus.value = ApiStatus.error;
        AppHelper.showToast(result['message'] ?? 'login_failed', isError: true);
      }
    } catch (e) {
      _apiStatus.value = ApiStatus.error;
      AppHelper.logError('Login error', e);
      AppHelper.showToast('something_went_wrong', isError: true);
    }
  }

  Future<void> signup() async {
    if (!signupFormKey.currentState!.validate()) {
      return;
    }

    if (!acceptTerms) {
      AppHelper.showToast('Please accept terms and conditions', isError: true);
      return;
    }

    try {
      _apiStatus.value = ApiStatus.loading;

      final hasInternet = await ConnectivityHelper.hasInternetConnection();
      if (!hasInternet) {
        _apiStatus.value = ApiStatus.noInternet;
        AppHelper.showToast('no_internet_connection'.tr, isError: true);
        return;
      }

      final result = await _authRepository.register(
        name: nameController.text.trim(),
        email: emailController.text.trim(),
        password: passwordController.text.trim(),
      );

      if (result['success'] == true) {
        _apiStatus.value = ApiStatus.success;
        AppHelper.showToast('Account created successfully! Please login.');

        // Clear signup form
        nameController.clear();
        emailController.clear();
        passwordController.clear();
        confirmPasswordController.clear();
        _acceptTerms.value = false;

        // Navigate to login
        Get.context!.go(AppRoutes.login);
      } else {
        _apiStatus.value = ApiStatus.error;
        AppHelper.showToast(
          result['message'] ?? 'signup_failed',
          isError: true,
        );
      }
    } catch (e) {
      _apiStatus.value = ApiStatus.error;
      AppHelper.logError('Signup error', e);
      AppHelper.showToast('something_went_wrong', isError: true);
    }
  }

  Future<void> forgotPassword() async {
    if (!forgotPasswordFormKey.currentState!.validate()) {
      return;
    }

    try {
      _apiStatus.value = ApiStatus.loading;

      final hasInternet = await ConnectivityHelper.hasInternetConnection();
      if (!hasInternet) {
        _apiStatus.value = ApiStatus.noInternet;
        AppHelper.showToast('no_internet_connection'.tr, isError: true);
        return;
      }

      final result = await _authRepository.forgotPassword(
        email: forgotPasswordEmailController.text.trim(),
      );

      if (result['success'] == true) {
        _apiStatus.value = ApiStatus.success;
        AppHelper.showToast('Password reset link sent to your email');

        // Clear form
        forgotPasswordEmailController.clear();

        // Navigate back to login
        Get.context!.go(AppRoutes.login);
      } else {
        _apiStatus.value = ApiStatus.error;
        AppHelper.showToast(
          result['message'] ?? 'failed_to_send_reset_link',
          isError: true,
        );
      }
    } catch (e) {
      _apiStatus.value = ApiStatus.error;
      AppHelper.logError('Forgot password error', e);
      AppHelper.showToast('something_went_wrong', isError: true);
    }
  }

  Future<void> logout() async {
    try {
      _apiStatus.value = ApiStatus.loading;

      // Call logout API if needed
      await _authRepository.logout();

      // Clear local storage
      await AppSharedPreference.logout();

      // Reset observables
      _userToken.value = '';
      _userData.value = null;
      _isLoggedIn.value = false;
      _apiStatus.value = ApiStatus.initial;

      // Clear form controllers
      emailController.clear();
      passwordController.clear();

      AppHelper.showToast('logout_successful');

      // Navigate to login
      Get.offAllNamed(AppRoutes.login);
    } catch (e) {
      _apiStatus.value = ApiStatus.error;
      AppHelper.logError('Logout error', e);
      AppHelper.showToast('something_went_wrong', isError: true);
    }
  }

  // UI Helper Methods
  void togglePasswordVisibility() {
    _isPasswordVisible.value = !_isPasswordVisible.value;
  }

  void toggleConfirmPasswordVisibility() {
    _isConfirmPasswordVisible.value = !_isConfirmPasswordVisible.value;
  }

  void toggleAcceptTerms([bool? value]) {
    _acceptTerms.value = value ?? !_acceptTerms.value;
  }

  // Validation Methods
  String? validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Name is required';
    }
    if (value.trim().length < 2) {
      return 'Name must be at least 2 characters';
    }
    return null;
  }

  String? validateEmail(String? value) {
    return ValidationHelper.validateEmail(value);
  }

  String? validatePassword(String? value) {
    return ValidationHelper.validatePassword(value);
  }

  String? validateConfirmPassword(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Please confirm your password';
    }
    if (value != passwordController.text) {
      return 'Passwords do not match';
    }
    return null;
  }
}
