const express = require("express");
const router = express.Router();
const bodyParser = require("body-parser");
jsonparser = bodyParser.json();
const multer = require("multer");
require("dotenv").config();
const form = multer();
const auth = require("../middleware/oath");
const adminauth = require("../middleware/auth");
const strictauth = require("../middleware/strictAuth");
const crypto = require("crypto");
require("dotenv").config();
const controller = require("../controller/controller.js");
const SharpMulter = require("sharp-multer");
//connection of sql server

//uploading profile in server using multer

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, "uploads/profile");
  },

  filename: function (req, file, cb) {
    const uniqueSuffix =
      Date.now() + "-" + crypto.randomBytes(6).toString("hex");
    cb(null, file.originalname);
  },
});

const upload = multer({
  storage: storage,
  fileFilter: (req, file, cb) => {
    if (
      file.mimetype == "image/png" ||
      file.mimetype == "image/jpg" ||
      file.mimetype == "image/jpeg" ||
      file.mimetype == "image/webp" ||
      file.mimetype == "image/avif" ||
      file.mimetype == "image/avi"
    ) {
      cb(null, true);
    } else {
      cb(null, false);
      return cb("Only .png, .jpg and .jpeg format allowed!");
    }
  },
  limits: {
    fileSize: 2 * 1024 * 1024,
  },
});
//uploading post pic  in server using multer
const storage2 = multer.diskStorage({
  destination: function (req, file, cb) {
    console.log(file);
    cb(null, "uploads/thumbnail");
  },
  filename: function (req, file, cb) {
    const uniqueSuffix =
      Date.now() + "-" + crypto.randomBytes(6).toString("hex");
    cb(null, file.originalname);
  },
});

const upload_post_img = multer({
  storage: storage2,
  fileFilter: (req, file, cb) => {
    if (
      file.mimetype == "image/jpg" ||
      file.mimetype == "image/png" ||
      file.mimetype == "image/jpeg" ||
      file.mimetype == "image/webp" ||
      file.mimetype == "image/avif" ||
      file.mimetype == "image/avi"
    ) {
      cb(null, true);
    } else {
      cb(null, false);
      return cb("Only .png, .jpg  .mp4 .avi and .jpeg  format allowed!");
    }
  },
  limits: {
    fileSize: 2 * 1024 * 1024,
  },
});
router.post("/unregisteredusertoken", controller.UnRegisteredUserToken);

//user register route
router.post("/login", upload.single("profile"), auth, controller.UserRegister);
router.post("/dashboard", controller.dashboard);
router.post("/dashboard_test", controller.dashboard_test);

router.post("/notsupportedmovies/:page", controller.notSupportedMovies);
router.post("/privatemovies/:page", controller.privateMovies);
router.post("/copyrightmovies", controller.copyrightMovies);
//show profile api of user

router.get("/showprofile/:page", strictauth, controller.UserShowProfile);

router.post(
  "/userupdate",
  upload.single("profile"),
  strictauth,
  controller.UserUpdate
);

//follow api of user

router.post("/followuser", strictauth, form.array(), controller.FollowUser);

//unfollow api of user

router.post("/unfollowuser", strictauth, form.array(), controller.UnFollowUser);
router.get("/testdata", controller.test);
// remove follower
router.post(
  "/removefollower",
  strictauth,
  form.array(),
  controller.removeFollower
);

//show follower api of user
router.post(
  "/getfollower/:page",
  auth,
  form.array(),
  controller.UserGetFollower
);

// show following api of user

router.post(
  "/getfollowing/:page",
  auth,
  form.array(),
  controller.UserGetFollowing
);

//api for upoload post
router.post(
  "/uploadpost",
  strictauth,
  upload_post_img.single("thumbnail"),
  controller.UserUploadPost
);

router.post(
  "/uploadpostformxvideo",
  strictauth,
  upload_post_img.single("thumbnail"),
  controller.UserUploadPostForMxvideo
);
// api for get all filter

router.post("/showfilter", auth, controller.ShowFilter);
//api for get users post

router.get("/showuserpost/:page", auth, controller.ShowOnlyPost);

//delete user post api
router.post(
  "/deleteuserpost",
  strictauth,
  form.array(),
  controller.DeleteUserPost
);

//search movie api
router.post("/searchpost/:page", auth, controller.UserSearchPost);
router.post("/searchpost_mxvideo/:page", controller.UserSearchPost_mxvideo);

//show random post api

router.post("/showposts/:page?", form.array(), controller.UserShowPosts);
router.post(
  "/showposts_v2/:page?",
  auth,
  form.array(),
  controller.UserShowPosts_v2
);
router.post(
  "/showpoststemp/:page?",
  auth,
  form.array(),
  controller.UserShowPoststemp
);

router.post("/addDeviceid", auth, controller.getDeviceID);

router.post("/suggestionMovie", auth, controller.suggestionMovie);
router.post("/suggestionMovieclone", auth, controller.suggestionMovieclone);

// showing individual users

router.post("/showinduser/:page?", auth, controller.ShowIndUser);

//like api

router.post("/likepost", strictauth, form.array(), controller.UserLikePost);
router.post(
  "/dislikepost",
  strictauth,
  form.array(),
  controller.UserDisLikePost
);

// view post api
router.post("/postview", auth, form.array(), controller.UserViewPost);

// show post genre wise
router.post(
  "/showgenrewise/:page",
  auth,
  form.array(),
  controller.ShowGenreWisePost
);

router.post(
  "/showgenrewise_v1/:page",
  auth,
  form.array(),
  controller.NewShowGenreWisePost
);

router.post(
  "/showgenrewise_v1_new/:page",
  auth,
  form.array(),
  controller.NewShowGenreWisePost_new
);

router.post(
  "/showgenrewisetemp/:page",
  auth,
  form.array(),
  controller.ShowGenreWisePosttemp
);

router.post(
  "/showcatwise/:page",
  auth,
  form.array(),
  controller.ShowCategoryWisePost
);

router.post(
  "/showcatwise_new/:page",
  auth,
  form.array(),
  controller.ShowCategoryWisePost_new
);

router.post(
  "/showCategoryWise/:page",
  auth,
  form.array(),
  controller.ShowGenreWisePost
);

router.get("/likescount/:id", auth, controller.ShowLikesCount);

//delete acount api
router.post("/deleteuser", strictauth, controller.DeleteUser);
// reporting any person's post
router.post("/reportpost", strictauth, form.array(), controller.reportUserPost);
router.post("/reportuser", strictauth, form.array(), controller.ReportUser);
router.post("/blockuser", strictauth, form.array(), controller.BlockUser);
router.post(
  "/showblockuser",
  strictauth,
  form.array(),
  controller.ShowBlockUser
);
router.post("/unblockuser", strictauth, form.array(), controller.UnBlockUser);

//logout api of user
router.post("/logoutuser", strictauth, controller.LogOutUser);

//admin panel apis

//admin login api
router.post("/adminlogin", form.array(), controller.AdminLogin);

// add user api for admin
router.post("/adminadduser", adminauth, upload.single("profile"));

router.post(
  "/adminupdateuser",
  upload.single("profile"),
  adminauth,
  form.array(),
  controller.AdminUpdateUser
);

router.delete(
  "/admindeleteuser",
  adminauth,
  form.array(),
  controller.AdminDeleteUser
);
router.post(
  "/adminupdatecat",
  adminauth,
  form.array(),
  controller.AdminUpdateCat
);
router.post(
  "/adminupdatelang",
  adminauth,
  form.array(),
  controller.AdminUpdateLang
);

router.post(
  "/adminshowuser/:page",
  form.array(),
  adminauth,
  controller.AdminShowUser
);

router.post("/getmovielist", adminauth, controller.getmovielist);
router.get("/adminshowinduser/:id", adminauth, controller.AdminShowIndUser);

router.post(
  "/adminshowpost/:page",
  adminauth,
  form.array(),
  controller.AdminShowPost
);

router.post(
  "/adminshowunverifiedpost/:page",
  form.array(),
  controller.AdminShowUnverifiedPost
);

router.post("/deleteunverifiedpost", controller.DeleteUnverifiedPost);
router.delete(
  "/deleteselectedunverifedpost",
  controller.DeleteSelectedUnverifedPost
);

router.post(
  "/adminshowpostsearch/:page",
  adminauth,
  form.array(),
  controller.AdminShowPostSearch
);

router.post(
  "/adminaddpost",
  adminauth,
  upload_post_img.fields([
    { name: "post", maxCount: 1 },
    { name: "thumb", maxCount: 1 },
  ]),
  controller.AdminAddPost
);
router.post(
  "/adminshowreporteduser/:page",
  adminauth,
  form.array(),
  controller.AdminShowReportedUser
);
router.get("/adminshowposts/:id", adminauth, controller.AdminShowIndPost);
router.delete(
  "/admindeletepost",
  adminauth,
  form.array(),
  controller.AdminDeletePost
);
router.post(
  "/admindelcategory",
  adminauth,
  form.array(),
  controller.AdminDelCategory
);
router.post(
  "/admindellanguage",
  adminauth,
  form.array(),
  controller.AdminDelLang
);
router.post(
  "/admindelgnere",
  adminauth,
  form.array(),
  controller.AdminDelGenre
);
router.post(
  "/adminsetgenre",
  adminauth,
  form.array(),
  controller.AdminSetGenre
);

router.post(
  "/adminupdatepost",
  adminauth,
  upload_post_img.single("thumbnail"),
  controller.AdminUpdatePost
);

router.post(
  "/adminupdatepostgenre",
  adminauth,
  form.array(),
  controller.AdminUpdatePostGenre
);
// admin delete report
router.delete(
  "/admindeletereport",
  adminauth,
  form.array(),
  controller.AdminDeleteReport
);
router.post("/adminshowfilters", adminauth, controller.AdminShowFilters);
router.post(
  "/AdminAutoDeletePost/:page",
  adminauth,
  controller.AdminAutoDeletePost
);
router.post(
  "/AdminAutoDeleteTrailer/:page",
  adminauth,
  controller.AdminAutoDeleteTrailer
);

router.post("/Admincheckembeded/:page", controller.AdminCheckEmbeded);
router.post(
  "/countrywiserestricmovies/:page",
  controller.countrywiserestricmovies
);

router.post("/crawlmovies/:page", controller.crawlmovies);

router.post("/crawltrailer/:page", controller.crawltrailer);

router.post(
  "/checkTrailerRestric/:page",
  adminauth,
  controller.checkTrailerRestric
);

//admin delete multiple reports
router.delete(
  "/admindeletemultyreport",
  adminauth,
  form.array(),
  controller.AdminDeleteMultyReports
);
router.post("/checktitle", adminauth, form.array(), controller.CheckTitle);
router.post(
  "/adminshowreportcount/:page",
  adminauth,
  form.array(),
  controller.AdminShowReports
);

router.post("/logoutadmin", adminauth, controller.LogOutAdmin);
router.delete("/deletemyaccount", controller.deletemyaccount);
router.delete("/deletemyaccount1", controller.deletemyaccount1);
router.post("/AdminNewGenre", controller.AdminAddGenre);
router.post("/AdminAddCategory", controller.AdminAddCategory);
router.post("/AdminAddLanguage", controller.AdminAddLanguage);

router.post("/blockbyadmin", adminauth, controller.blockUserByAdmin);
router.post("/unblockbyadmin", adminauth, controller.UnblockUserByAdmin);

router.post("/createshorts", controller.createMxvideos);
router.post("/getmxshortsvideos", controller.getMxvideomovies);
router.post("/mxvideobymovie", controller.getshortsbymovie);
router.post("/editmxvideo", controller.editmxshorts);
router.delete("/deletemxvideo/:id", controller.deleteMxShorts);

router.post(
  "/getlistbockedbyadmin",
  adminauth,
  controller.getlistbockedbyadmin
);

router.post("/getcrawlepisodes", controller.getAllCrawlEpisodes);

// admin api for search history
router.get(
  "/getsearchhistory/:page",
  adminauth,
  controller.getAllSearchHistory
);
module.exports = router;
