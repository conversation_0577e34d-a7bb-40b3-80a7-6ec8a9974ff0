import 'package:my_video/app_imports.dart';

class MyPlaylistPage extends StatefulWidget {
  const MyPlaylistPage({super.key});

  @override
  State<MyPlaylistPage> createState() => PlaylistPageState();
}

class PlaylistPageState extends State<MyPlaylistPage> {
  PlaylistPageHelper? _playlistPageHelper;
  late PlaylistController playlistController;

  @override
  Widget build(BuildContext context) {
    _playlistPageHelper = _playlistPageHelper ?? PlaylistPageHelper(this);

    return GetBuilder(
      init: PlaylistController(),
      builder: (PlaylistController controller) {
        playlistController = controller;
        return Scaffold(
          appBar: AppBar(
            title: const AppText(
              text: 'My Playlist',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
            ),
            backgroundColor: AppColorConstants.backgroundColor,
            elevation: 0,
            actions: [
              if (_playlistPageHelper!.playlistMovies.isNotEmpty) ...[
                IconButton(
                  icon: const Icon(Icons.share),
                  onPressed: _playlistPageHelper!.sharePlaylist,
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'clear') {
                      _playlistPageHelper!.clearPlaylist();
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'clear',
                      child: Row(
                        children: [
                          Icon(
                            Icons.clear_all,
                            color: AppColorConstants.colorRed,
                          ),
                          SizedBox(width: 8),
                          AppText(
                            text: 'Clear Playlist',
                            color: AppColorConstants.colorRed,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
          body: _playlistPageHelper!.isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                  onRefresh: _playlistPageHelper!.refreshPlaylist,
                  child: _playlistPageHelper!.playlistMovies.isEmpty
                      ? _buildEmptyState()
                      : _buildPlaylistGrid(),
                ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.playlist_remove,
            size: MySize.height(80),
            color: AppColorConstants.textSecondary.withValues(alpha: 0.5),
          ),
          Space.height(16),
          AppText(
            text: 'Your playlist is empty',
            fontSize: MySize.fontSize(18),
            fontWeight: FontWeight.w600,
            color: AppColorConstants.textPrimary,
          ),
          Space.height(8),
          AppText(
            text: 'Add movies to your playlist to watch them later',
            fontSize: MySize.fontSize(14),
            color: AppColorConstants.textSecondary,
            textAlign: TextAlign.center,
          ),
          Space.height(24),
          AppButton(
            text: 'Browse Movies',
            onPressed: () {
              // Switch to home tab
              final mainController = Get.find<MainNavigationController>();
              mainController.changeTab(0);
            },
            backgroundColor: AppColorConstants.primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildPlaylistGrid() {
    return Padding(
      padding: EdgeInsets.all(MySize.width(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Playlist info
          Row(
            children: [
              Icon(
                Icons.playlist_play,
                color: AppColorConstants.primaryColor,
                size: MySize.height(20),
              ),
              Space.width(8),
              AppText(
                text: '${_playlistPageHelper!.playlistMovies.length} movies',
                fontSize: MySize.fontSize(16),
                color: AppColorConstants.textSecondary,
              ),
            ],
          ),
          Space.height(16),

          // Movies grid
          Expanded(
            child: GridView.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.7,
                crossAxisSpacing: MySize.width(12),
                mainAxisSpacing: MySize.height(16),
              ),
              itemCount: _playlistPageHelper!.playlistMovies.length,
              itemBuilder: (context, index) {
                final movie = _playlistPageHelper!.playlistMovies[index];
                return _buildPlaylistMovieCard(movie);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaylistMovieCard(MovieModel movie) {
    return GestureDetector(
      onTap: () => _playlistPageHelper!.playMovie(movie, context),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(MySize.radius(12)),
          color: AppColorConstants.cardColor,
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Movie poster
                Expanded(
                  flex: 3,
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.vertical(
                        top: Radius.circular(MySize.radius(12)),
                      ),
                      color: AppColorConstants.dividerColor,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.vertical(
                        top: Radius.circular(MySize.radius(12)),
                      ),
                      child: movie.thumbnailUrl.isNotEmpty
                          ? CachedNetworkImage(
                              imageUrl: movie.thumbnailUrl,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                color: AppColorConstants.dividerColor,
                                child: const Center(
                                  child: CircularProgressIndicator(),
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                color: AppColorConstants.dividerColor,
                                child: Icon(
                                  Icons.movie,
                                  size: MySize.height(40),
                                  color: AppColorConstants.textSecondary,
                                ),
                              ),
                            )
                          : Container(
                              color: AppColorConstants.dividerColor,
                              child: Icon(
                                Icons.movie,
                                size: MySize.height(40),
                                color: AppColorConstants.textSecondary,
                              ),
                            ),
                    ),
                  ),
                ),

                // Movie info
                Expanded(
                  flex: 1,
                  child: Padding(
                    padding: EdgeInsets.all(MySize.width(8)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AppText(
                          text: movie.title,
                          fontSize: MySize.fontSize(12),
                          fontWeight: FontWeight.w600,
                          color: AppColorConstants.textPrimary,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (movie.rating != null) ...[
                          Space.height(2),
                          Row(
                            children: [
                              Icon(
                                Icons.star,
                                size: MySize.height(12),
                                color: Colors.amber,
                              ),
                              Space.width(2),
                              AppText(
                                text: movie.formattedRating,
                                fontSize: MySize.fontSize(10),
                                color: AppColorConstants.textSecondary,
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),

            // Remove button
            Positioned(
              top: MySize.height(8),
              right: MySize.width(8),
              child: GestureDetector(
                onTap: () => _playlistPageHelper!.removeFromPlaylist(movie),
                child: Container(
                  padding: EdgeInsets.all(MySize.height(4)),
                  decoration: BoxDecoration(
                    color: AppColorConstants.colorRed,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.close,
                    size: MySize.height(16),
                    color: AppColorConstants.textPrimary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
