import 'package:my_video/app_imports.dart';

class SubscriptionPlan {
  final String id;
  final String name;
  final String description;
  final double price;
  final String duration;
  final List<String> features;
  final bool isPopular;
  final bool isCurrentPlan;

  SubscriptionPlan({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.duration,
    required this.features,
    this.isPopular = false,
    this.isCurrentPlan = false,
  });
}

class SubscriptionController extends GetxController {
  final Logger _logger = Logger();

  @override
  void onInit() {
    super.onInit();
  }

  // API Methods - Only API calls, no UI state management
  // Note: Subscription operations are currently local (Hive),
  // but this structure allows for future API integration
}
