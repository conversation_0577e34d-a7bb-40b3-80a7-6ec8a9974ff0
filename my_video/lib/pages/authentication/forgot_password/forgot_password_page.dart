import 'package:my_video/app_imports.dart';

class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => ForgotPasswordPageState();
}

class ForgotPasswordPageState extends State<ForgotPasswordPage> {
  ForgotPasswordPageHelper? _forgotPasswordPageHelper;
  late AuthenticationController authenticationController;

  @override
  void initState() {
    super.initState();
    authenticationController = Get.find<AuthenticationController>();
  }

  @override
  Widget build(BuildContext context) {
    MySize.init(context);
    _forgotPasswordPageHelper = _forgotPasswordPageHelper ?? ForgotPasswordPageHelper(this);
    
    return GetBuilder<AuthenticationController>(
      builder: (controller) {
        return AppScaffold(
          appBar: AppBar(
            title: AppText(
              text: 'Forgot Password',
              fontSize: MySize.fontSize(20),
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
            ),
            backgroundColor: AppColorConstants.backgroundColor,
            elevation: 0,
            leading: IconButton(
              icon: Icon(
                Icons.arrow_back,
                color: AppColorConstants.textPrimary,
                size: MySize.height(24),
              ),
              onPressed: () => context.go(AppRoutes.login),
            ),
          ),
          body: SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(MySize.width(16)),
              child: Form(
                key: controller.forgotPasswordFormKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Space.height(40),

                    // Icon and Title
                    Column(
                      children: [
                        Container(
                          width: MySize.width(100),
                          height: MySize.height(100),
                          decoration: BoxDecoration(
                            color: AppColorConstants.primaryColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(MySize.radius(50)),
                          ),
                          child: Icon(
                            Icons.lock_reset_outlined,
                            size: MySize.height(50),
                            color: AppColorConstants.primaryColor,
                          ),
                        ),
                        
                        Space.height(32),
                        
                        AppText(
                          text: 'Reset Password',
                          fontSize: MySize.fontSize(28),
                          fontWeight: FontWeight.bold,
                          color: AppColorConstants.textPrimary,
                          textAlign: TextAlign.center,
                        ),
                        
                        Space.height(12),
                        
                        AppText(
                          text: 'Enter your email address and we\'ll send you a link to reset your password.',
                          fontSize: MySize.fontSize(16),
                          color: AppColorConstants.textSecondary,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                    
                    Space.height(48),
                    
                    // Email Field
                    AppTextFormField(
                      controller: controller.forgotPasswordEmailController,
                      labelText: 'Email',
                      hintText: 'Enter your email address',
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.done,
                      prefixIcon: Icon(
                        Icons.email_outlined,
                        size: MySize.height(20),
                      ),
                      validator: controller.validateEmail,
                    ),
                    
                    Space.height(32),
                    
                    // Send Reset Link Button
                    Obx(() => AppButton(
                      text: 'Send Reset Link',
                      onPressed: controller.apiStatus.isLoading ? null : controller.forgotPassword,
                      isLoading: controller.apiStatus.isLoading,
                      backgroundColor: AppColorConstants.primaryColor,
                      textColor: AppColorConstants.textPrimary,
                    )),
                    
                    Space.height(24),
                    
                    // Back to Login Link
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AppText(
                          text: "Remember your password? ",
                          fontSize: MySize.fontSize(14),
                          color: AppColorConstants.textSecondary,
                        ),
                        GestureDetector(
                          onTap: () => context.go(AppRoutes.login),
                          child: AppText(
                            text: 'Sign In',
                            fontSize: MySize.fontSize(14),
                            color: AppColorConstants.primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    
                    Space.height(40),
                    
                    // Help Text
                    Container(
                      padding: EdgeInsets.all(MySize.width(16)),
                      decoration: BoxDecoration(
                        color: AppColorConstants.cardColor,
                        borderRadius: BorderRadius.circular(MySize.radius(12)),
                        border: Border.all(
                          color: AppColorConstants.dividerColor,
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.info_outline,
                                color: AppColorConstants.primaryColor,
                                size: MySize.height(20),
                              ),
                              Space.width(8),
                              AppText(
                                text: 'Need Help?',
                                fontSize: MySize.fontSize(16),
                                fontWeight: FontWeight.w600,
                                color: AppColorConstants.textPrimary,
                              ),
                            ],
                          ),
                          Space.height(8),
                          AppText(
                            text: 'If you don\'t receive the email within a few minutes, please check your spam folder or contact our support team.',
                            fontSize: MySize.fontSize(14),
                            color: AppColorConstants.textSecondary,
                          ),
                          Space.height(12),
                          GestureDetector(
                            onTap: () => context.go(AppRoutes.contactSupport),
                            child: AppText(
                              text: 'Contact Support',
                              fontSize: MySize.fontSize(14),
                              color: AppColorConstants.primaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
