import 'package:my_video/app_imports.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => HomePageState();
}

class HomePageState extends State<HomePage> {
  HomePageHelper? _homePageHelper;
  late HomeController homeController;

  @override
  Widget build(BuildContext context) {
    _homePageHelper = _homePageHelper ?? HomePageHelper(this);
    return GetBuilder(
      init: HomeController(),
      builder: (HomeController controller) {
        homeController = controller;
        return Scaffold(extendBody: true, body: _bodyView());
      },
    );
  }

  Widget _appBar() {
    return Padding(
      padding: const EdgeInsets.all(8.0).copyWith(left: 16),
      child: Row(
        children: [
          const AppText(
            text: 'My Video',
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColorConstants.textPrimary,
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(
              Icons.search,
              color: AppColorConstants.textPrimary,
            ),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(
              Icons.notifications_outlined,
              color: AppColorConstants.textPrimary,
            ),
            onPressed: () {},
          ),
        ],
      ),
    );
  }

  Widget _bodyView() {
    return _homePageHelper!.isLoading
        ? const Center(child: CircularProgressIndicator())
        : SafeArea(
            bottom: false,
            child: RefreshIndicator(
              onRefresh: _homePageHelper!.refreshData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _appBar(),
                    if (_homePageHelper!.featuredMovies.isNotEmpty) ...[
                      _buildFeaturedBanner(),
                      Space.height(24),
                    ],

                    ..._homePageHelper!.categories.map((category) {
                      final movies = _homePageHelper!.getMoviesByCategory(
                        category.name,
                      );
                      if (movies.isEmpty) return const SizedBox.shrink();

                      return _buildCategorySection(category, movies);
                    }),

                    Space.height(120), // Bottom padding for FAB
                  ],
                ),
              ),
            ),
          );
  }

  Widget _buildFeaturedBanner() {
    if (_homePageHelper!.featuredMovies.isEmpty) {
      return SizedBox(
        height: MySize.height(220),
        child: LoadingManager.buildLoadingWidget(
          message: 'Loading featured movies...',
        ),
      );
    }

    return SizedBox(
      height: MySize.height(220),
      child: PageView.builder(
        itemCount: _homePageHelper!.featuredMovies.length,
        controller: PageController(viewportFraction: 0.9),
        itemBuilder: (context, index) {
          final movie = _homePageHelper!.featuredMovies[index];
          return Container(
            margin: EdgeInsets.symmetric(horizontal: MySize.width(8)),
            child: BannerMovieCard(
              movie: movie,
              onTap: () => _homePageHelper!.playMovie(movie, context),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCategorySection(
    CategoryModel category,
    List<MovieModel> movies,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppText(
                text: category.name,
                fontSize: MySize.fontSize(18),
                fontWeight: FontWeight.bold,
                color: AppColorConstants.textPrimary,
              ),
              GestureDetector(
                onTap: () => _homePageHelper!.viewAllMovies(category),
                child: AppText(
                  text: 'See All',
                  fontSize: MySize.fontSize(14),
                  color: AppColorConstants.primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        Space.height(12),
        SizedBox(
          height: MySize.height(240),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
            itemCount: movies.length + 1, // +1 for single ad
            itemBuilder: (context, index) {
              // Determine ad position based on category index
              // Even categories (0, 2, 4...): ad at index 0
              // Odd categories (1, 3, 5...): ad at last position
              final categoryIndex = _homePageHelper!.categories.indexOf(
                category,
              );
              final isEvenCategory = categoryIndex % 2 == 0;
              final adPosition = isEvenCategory ? 0 : movies.length;

              if (index == adPosition) {
                final bannerAd = AdsManager.getBannerAdWidget(
                  index: categoryIndex % 5,
                );
                return bannerAd ?? AdsManager.buildAdPlaceholder();
              }

              // Movie items
              final movieIndex = isEvenCategory ? index - 1 : index;
              if (movieIndex < 0 || movieIndex >= movies.length) {
                return const SizedBox.shrink();
              }

              final movie = movies[movieIndex];
              return MovieCard(
                movie: movie,
                onTap: () => _homePageHelper!.playMovie(movie, context),
                showTitle: true,
                showRating: true,
                showDuration: true,
              );
            },
          ),
        ),
        Space.height(24),
      ],
    );
  }
}
