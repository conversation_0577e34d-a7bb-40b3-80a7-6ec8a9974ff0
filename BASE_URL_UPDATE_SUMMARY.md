# 🔄 Base URL Update Summary

## ✅ Changes Made

### 1. **Flutter App Configuration**
**File**: `my_video/lib/app/config/app_config.dart`
```dart
String get baseUrl {
  switch (flavor) {
    case Flavor.dev:
      return 'https://appzone99.science:3001/'; // ✅ Updated
    case Flavor.prod:
      return 'https://appzone99.science:3001/'; // ✅ Updated
  }
}
```

### 2. **Backend Environment Configuration**
**File**: `movie_site/.env`
```env
PORT = 8010
HOST = localhost
DB_TYPE = sqlite
DB_PATH = ./movie_site.db
USER_SECRET = "iy98hcbh489n38984y4h498"
ADMIN_SECRET = "joge15fs4a5sd4f7abns5a4a"
ADMIN = "admin"
PASS = "admin123"
STATIC_KEY = "Habt5o0cDNWjc42y"
PUBLIC_PROFILE_PATH = 'https://appzone99.science:3001/upload/profile/' # ✅ Updated
PUBLIC_POST_PATH = 'https://appzone99.science:3001/upload/thumbnail/'   # ✅ Updated
```

### 3. **Authentication API Endpoints**
**File**: `my_video/lib/repository/authentication/authentication_repository_impl.dart`

**Updated Endpoints**:
- ✅ Login: `auth/login` → `login`
- ✅ Register: `auth/register` → `login` (backend uses same endpoint)
- ✅ Logout: `auth/logout` → `logoutuser`
- ✅ Forgot Password: Mock response (backend doesn't have this endpoint)

**Updated Response Handling**:
- ✅ Check `status` field (1 = success, 0 = error)
- ✅ Extract `token` and `userData` from response
- ✅ Handle status code 201 for login/register

### 4. **Postman Collection**
**File**: `movie_site_postman_collection.json`
- ✅ Base URL: `https://appzone99.science:3001`
- ✅ All endpoints configured correctly

## 🚀 How to Start

### **Backend Server**
```bash
cd movie_site
npm install
npm start
```

**Expected Output**:
```
port is running on 8010
```

**Server URLs**:
- HTTPS: `https://appzone99.science:3001`
- HTTP: `http://localhost:8010` (fallback)

### **Flutter App**
```bash
cd my_video
flutter run
```

## 🧪 Testing

### **1. Test Backend Health**
```bash
curl -k https://appzone99.science:3001/testdata
```

### **2. Test Authentication**
```bash
# Get unregistered token
curl -k -X POST https://appzone99.science:3001/unregisteredusertoken

# Test login/register
curl -k -X POST https://appzone99.science:3001/login \
  -H "Content-Type: application/json" \
  -d '{
    "fname": "Test",
    "lname": "User", 
    "email": "<EMAIL>",
    "gender": "m"
  }'
```

### **3. Use Postman Collection**
- Import `movie_site_postman_collection.json`
- Base URL is set to `https://appzone99.science:3001`
- Test all authentication endpoints

## 🔒 SSL Certificate

The backend uses SSL certificate for `appzone99.science`:
- Certificate: `appzone99_science.crt`
- Private Key: `appzone99_science.key`

## 🌐 Domain Configuration

For the domain `appzone99.science` to work:

### **Production**
- Domain should point to your server's IP address
- SSL certificate should be valid

### **Local Development** (if needed)
Add to hosts file:
```bash
# /etc/hosts (macOS/Linux) or C:\Windows\System32\drivers\etc\hosts (Windows)
127.0.0.1 appzone99.science
```

## 📱 Flutter App Flow

1. **App Launch** → Splash Screen
2. **Check Login Status** → Navigate to Login or Main Navigation
3. **Authentication** → Uses `https://appzone99.science:3001` endpoints
4. **API Calls** → All use the configured base URL

## 🔧 Key Configuration Points

### **Backend (Node.js)**
- ✅ HTTPS Server: Port 3001
- ✅ HTTP Server: Port 8010 (fallback)
- ✅ SSL Certificate: `appzone99.science`
- ✅ Environment variables updated

### **Flutter App**
- ✅ Base URL: `https://appzone99.science:3001/`
- ✅ API endpoints updated to match backend
- ✅ Response parsing updated for backend format

### **API Integration**
- ✅ Authentication endpoints match backend
- ✅ Request/response format compatible
- ✅ Status codes and error handling updated

## 🎯 Next Steps

1. **Start Backend**: Follow the startup guide
2. **Test API**: Use Postman collection
3. **Run Flutter App**: Test complete authentication flow
4. **Monitor**: Check logs for any connection issues

The configuration is now fully set to use `https://appzone99.science:3001` as the base URL! 🎉

## 🐛 Troubleshooting

### **Connection Issues**
- Verify domain resolves to correct IP
- Check SSL certificate validity
- Test with curl commands above

### **Authentication Issues**
- Check backend logs for errors
- Verify API endpoints are working
- Test with Postman collection

### **Flutter App Issues**
- Check network connectivity
- Verify base URL in app config
- Check device can reach the domain
