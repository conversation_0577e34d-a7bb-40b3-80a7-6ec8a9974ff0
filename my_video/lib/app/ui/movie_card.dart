import 'package:my_video/app_imports.dart';

class MovieCard extends StatelessWidget {
  final MovieModel movie;
  final VoidCallback? onTap;
  final double? width;
  final double? height;
  final bool showTitle;
  final bool showRating;
  final bool showDuration;

  const MovieCard({
    super.key,
    required this.movie,
    this.onTap,
    this.width,
    this.height,
    this.showTitle = true,
    this.showRating = false,
    this.showDuration = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: width ?? MySize.width(140),
        margin: EdgeInsets.only(right: MySize.width(12)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(child: _buildThumbnail()),
            if (showTitle) ...[Space.height(8), _buildTitle()],
            if (showRating || showDuration) ...[
              Space.height(4),
              _buildMetadata(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildThumbnail() {
    return Container(
      height: height ?? MySize.height(200),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(MySize.radius(12)),
        color: AppColorConstants.cardColor,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(MySize.radius(12)),
        child: Stack(
          children: [
            CachedNetworkImage(
              imageUrl: movie.thumbnailUrl,
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: AppColorConstants.cardColor,
                child: Center(
                  child: Icon(
                    Icons.movie_outlined,
                    size: MySize.height(40),
                    color: AppColorConstants.textHint,
                  ),
                ),
              ),
              errorWidget: (context, url, error) => Container(
                color: AppColorConstants.cardColor,
                child: Center(
                  child: Icon(
                    Icons.broken_image_outlined,
                    size: MySize.height(40),
                    color: AppColorConstants.textHint,
                  ),
                ),
              ),
            ),
            // Play button overlay
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.center,
                    end: Alignment.center,
                    colors: [Colors.transparent, Colors.black.withOpacity(0.1)],
                  ),
                ),
                child: Center(
                  child: Container(
                    padding: EdgeInsets.all(MySize.height(8)),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.6),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.play_arrow,
                      color: AppColorConstants.textPrimary,
                      size: MySize.height(24),
                    ),
                  ),
                ),
              ),
            ),
            // Featured badge
            if (movie.isFeatured)
              Positioned(
                top: MySize.height(8),
                left: MySize.width(8),
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: MySize.width(8),
                    vertical: MySize.height(4),
                  ),
                  decoration: BoxDecoration(
                    color: AppColorConstants.primaryColor,
                    borderRadius: BorderRadius.circular(MySize.radius(4)),
                  ),
                  child: AppText(
                    text: 'Featured',
                    fontSize: MySize.fontSize(10),
                    color: AppColorConstants.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return AppText(
      text: movie.title,
      fontSize: MySize.fontSize(14),
      fontWeight: FontWeight.w600,
      color: AppColorConstants.textPrimary,
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildMetadata() {
    return Row(
      children: [
        if (showRating && movie.rating != null) ...[
          Icon(Icons.star, size: MySize.height(14), color: Colors.amber),
          Space.width(4),
          AppText(
            text: movie.formattedRating,
            fontSize: MySize.fontSize(12),
            color: AppColorConstants.textSecondary,
          ),
        ],
        if (showRating &&
            showDuration &&
            movie.rating != null &&
            movie.duration != null) ...[
          Space.width(8),
          Container(
            width: 1,
            height: MySize.height(12),
            color: AppColorConstants.dividerColor,
          ),
          Space.width(8),
        ],
        if (showDuration && movie.duration != null) ...[
          Icon(
            Icons.access_time,
            size: MySize.height(14),
            color: AppColorConstants.textSecondary,
          ),
          Space.width(4),
          AppText(
            text: movie.formattedDuration,
            fontSize: MySize.fontSize(12),
            color: AppColorConstants.textSecondary,
          ),
        ],
      ],
    );
  }
}

class BannerMovieCard extends StatelessWidget {
  final MovieModel movie;
  final VoidCallback? onTap;

  const BannerMovieCard({super.key, required this.movie, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: MySize.height(220),
        margin: EdgeInsets.symmetric(horizontal: MySize.width(16)),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(MySize.radius(16)),
          color: AppColorConstants.cardColor,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(MySize.radius(16)),
          child: Stack(
            children: [
              CachedNetworkImage(
                imageUrl: movie.thumbnailUrl,
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: AppColorConstants.cardColor,
                  child: Center(
                    child: Icon(
                      Icons.movie_outlined,
                      size: MySize.height(60),
                      color: AppColorConstants.textHint,
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: AppColorConstants.cardColor,
                  child: Center(
                    child: Icon(
                      Icons.broken_image_outlined,
                      size: MySize.height(60),
                      color: AppColorConstants.textHint,
                    ),
                  ),
                ),
              ),
              // Gradient overlay
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.7),
                      ],
                    ),
                  ),
                ),
              ),
              // Content
              Positioned(
                bottom: MySize.height(16),
                left: MySize.width(16),
                right: MySize.width(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppText(
                      text: movie.title,
                      fontSize: MySize.fontSize(20),
                      fontWeight: FontWeight.bold,
                      color: AppColorConstants.textPrimary,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (movie.description != null) ...[
                      Space.height(4),
                      AppText(
                        text: movie.description!,
                        fontSize: MySize.fontSize(14),
                        color: AppColorConstants.textSecondary,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    Space.height(12),
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: MySize.width(16),
                            vertical: MySize.height(8),
                          ),
                          decoration: BoxDecoration(
                            color: AppColorConstants.primaryColor,
                            borderRadius: BorderRadius.circular(
                              MySize.radius(20),
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.play_arrow,
                                color: AppColorConstants.textPrimary,
                                size: MySize.height(18),
                              ),
                              Space.width(4),
                              AppText(
                                text: 'Watch Now',
                                fontSize: MySize.fontSize(12),
                                fontWeight: FontWeight.w600,
                                color: AppColorConstants.textPrimary,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
