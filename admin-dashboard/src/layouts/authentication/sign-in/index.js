import React, { useState } from "react";
// react-router-dom components
import { Link, useNavigate } from "react-router-dom";
// @mui material components
import Card from "@mui/material/Card";
import Switch from "@mui/material/Switch";
import Grid from "@mui/material/Grid";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// @mui icons

// Material Dashboard 2 React components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import MDInput from "components/MDInput";
import MDButton from "components/MDButton";

// Authentication layout components
import BasicLayout from "layouts/authentication/components/BasicLayout";

// Images
import bgImage from "assets/images/bg-sign-in-basic.jpeg";
import { useCookies } from "react-cookie";

function Basic() {
  const [cookie,setcookie] =  useCookies()
  const [rememberMe, setRememberMe] = useState(false);
  const handleSetRememberMe = () => setRememberMe(!rememberMe);
  const Navigate = useNavigate()
    // const {account,setaccount} = useContext(adminlogin)
    const [data,setdata] = useState({
        username:"",
        pass:"",
    })
    const handleinput = (e)=>{
    const{name,value} = e.target
    setdata({...data,[name]:value})
    }
    
    const senddata = async()=>{
        const form = new FormData()
        form.append("username",data.username)
        form.append("password",data.pass)
        const res = await fetch(`${window.path}/adminlogin`,{
            method:"POST",
            body:form 
            })
          const resdata =   await res.json()
          console.log(resdata)
            if(resdata.status === 1){
              setcookie("admin", resdata.cb, {
                path: "/",
                maxAge:2592000
              });
               // sessionStorage.setItem("auth",resdata.cb)
               Navigate("/dashboard")
                // window.location.reload()
                
            }
            else{
              toast.error('invalid credential !', {
                position: toast.POSITION.TOP_CENTER
            });
            }
            
        
    }

  return (
    <BasicLayout image={bgImage}>
      <ToastContainer/>
      <Card>
        <MDBox
          variant="gradient"
          bgColor="info"
          borderRadius="lg"
          coloredShadow="info"
          mx={2}
          mt={-3}
          p={2}
          mb={1}
          textAlign="center"
        >
          <MDTypography variant="h4" fontWeight="medium" color="white" mt={1} p={4}>
            Sign in
          </MDTypography>
        </MDBox>
        <MDBox pt={4} pb={5} px={3}>
          <MDBox component="form" role="form">
            <MDBox mb={2}>
              <MDInput type="email" label="Email" name="username" onChange={handleinput} fullWidth />
            </MDBox>
            <MDBox mb={2}>
              <MDInput type="password" label="Password" name="pass" onChange={handleinput} fullWidth />
            </MDBox>
            <MDBox display="flex" alignItems="center" ml={-1}>
              <Switch checked={rememberMe} onChange={handleSetRememberMe} />
              <MDTypography
                variant="button"
                fontWeight="regular"
                color="text"
                onClick={handleSetRememberMe}
                sx={{ cursor: "pointer", userSelect: "none", ml: -1 }}
              >
                &nbsp;&nbsp;Remember me
              </MDTypography>
            </MDBox>
            <MDBox mt={4} mb={1}>
              <MDButton variant="gradient" color="info" onClick ={senddata} fullWidth>
                sign in
              </MDButton>
            </MDBox>
          </MDBox>
        </MDBox>
      </Card>
    </BasicLayout>
  );
}

export default Basic;
