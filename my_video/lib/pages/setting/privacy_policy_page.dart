import 'package:my_video/app_imports.dart';

class PrivacyPolicyPage extends StatelessWidget {
  const PrivacyPolicyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const AppText(
          text: 'Privacy Policy',
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        backgroundColor: AppColorConstants.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: AppColorConstants.textPrimary,
          ),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(MySize.width(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection(
              'Information We Collect',
              'We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us for support. This may include your name, email address, and payment information.',
            ),
            Space.height(20),

            _buildSection(
              'How We Use Your Information',
              'We use the information we collect to:\n• Provide and maintain our service\n• Process transactions and send confirmations\n• Send you technical notices and support messages\n• Respond to your comments and questions\n• Improve our service and develop new features',
            ),
            Space.height(20),

            _buildSection(
              'Information Sharing',
              'We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy or as required by law.',
            ),
            Space.height(20),

            _buildSection(
              'Data Storage and Security',
              'We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. Your data is stored securely using industry-standard encryption.',
            ),
            Space.height(20),

            _buildSection(
              'Cookies and Tracking',
              'We may use cookies and similar tracking technologies to enhance your experience, analyze usage patterns, and improve our service. You can control cookie settings through your device preferences.',
            ),
            Space.height(20),

            _buildSection(
              'Third-Party Services',
              'Our app may contain links to third-party websites or services. We are not responsible for the privacy practices of these third parties. We encourage you to read their privacy policies.',
            ),
            Space.height(20),

            _buildSection(
              'Children\'s Privacy',
              'Our service is not intended for children under 13. We do not knowingly collect personal information from children under 13. If you believe we have collected such information, please contact us.',
            ),
            Space.height(20),

            _buildSection(
              'Your Rights',
              'You have the right to:\n• Access your personal information\n• Correct inaccurate information\n• Delete your account and data\n• Opt out of marketing communications\n• Request data portability',
            ),
            Space.height(20),

            _buildSection(
              'Data Retention',
              'We retain your personal information for as long as necessary to provide our service and fulfill the purposes outlined in this policy, unless a longer retention period is required by law.',
            ),
            Space.height(20),

            _buildSection(
              'International Data Transfers',
              'Your information may be transferred to and processed in countries other than your own. We ensure appropriate safeguards are in place to protect your information.',
            ),
            Space.height(20),

            _buildSection(
              'Changes to This Policy',
              'We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new policy on this page and updating the "Last Updated" date.',
            ),
            Space.height(20),

            _buildSection(
              'Contact Us',
              'If you have any questions about this Privacy Policy, please contact us through the app\'s support section or email <NAME_EMAIL>.',
            ),
            Space.height(20),

            Container(
              width: double.infinity,
              padding: EdgeInsets.all(MySize.width(16)),
              decoration: BoxDecoration(
                color: AppColorConstants.cardColor,
                borderRadius: BorderRadius.circular(MySize.radius(8)),
              ),
              child: Column(
                children: [
                  AppText(
                    text:
                        'Last Updated: ${DateFormat('MMMM dd, yyyy').format(DateTime.now())}',
                    fontSize: MySize.fontSize(12),
                    color: AppColorConstants.textSecondary,
                    textAlign: TextAlign.center,
                  ),
                  Space.height(8),
                  AppText(
                    text: 'MyVideo v1.0.0',
                    fontSize: MySize.fontSize(12),
                    color: AppColorConstants.textSecondary,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            Space.height(100), // Bottom padding
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          text: title,
          fontSize: MySize.fontSize(18),
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        Space.height(8),
        AppText(
          text: content,
          fontSize: MySize.fontSize(14),
          color: AppColorConstants.textSecondary,
        ),
      ],
    );
  }
}
