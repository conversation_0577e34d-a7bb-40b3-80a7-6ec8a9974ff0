import 'package:my_video/app_imports.dart';

/// Environment-specific configuration
class EnvironmentConfig {
  // Server Configuration Options
  static const Map<String, ServerConfig> _serverConfigs = {
    'local': ServerConfig(
      baseUrl: 'https://appzone99.science:3001/',
      name: 'Local Development',
      description: 'For local development and testing',
    ),
    'local_network': ServerConfig(
      baseUrl: 'https://appzone99.science:3001/',
      name: 'Local Network',
      description: 'For testing on local network devices',
    ),
    'staging': ServerConfig(
      baseUrl: 'https://appzone99.science:3001/',
      name: 'Staging Server',
      description: 'For staging environment testing',
    ),
    'production': ServerConfig(
      baseUrl: 'https://appzone99.science:3001/',
      name: 'Production Server',
      description: 'Live production server',
    ),
    'custom': ServerConfig(
      baseUrl: 'https://appzone99.science:3001/',
      name: 'Custom Server',
      description: 'Custom server configuration',
    ),
  };

  /// Get server configuration by environment
  static ServerConfig getServerConfig(String environment) {
    return _serverConfigs[environment] ?? _serverConfigs['local']!;
  }

  /// Get all available server configurations
  static Map<String, ServerConfig> get allConfigs => _serverConfigs;

  /// Common server configurations for quick setup
  static ServerConfig get local => _serverConfigs['local']!;
  static ServerConfig get localNetwork => _serverConfigs['local_network']!;
  static ServerConfig get staging => _serverConfigs['staging']!;
  static ServerConfig get production => _serverConfigs['production']!;
  static ServerConfig get custom => _serverConfigs['custom']!;
}

/// Server configuration model
class ServerConfig {
  final String baseUrl;
  final String name;
  final String description;

  const ServerConfig({
    required this.baseUrl,
    required this.name,
    required this.description,
  });

  /// Check if this is a secure (HTTPS) connection
  bool get isSecure => baseUrl.startsWith('https://');

  /// Check if this is a local connection
  bool get isLocal =>
      baseUrl.contains('appzone99') || baseUrl.contains('127.0.0.1');

  /// Get the host from the URL
  String get host {
    final uri = Uri.parse(baseUrl);
    return uri.host;
  }

  /// Get the port from the URL
  int get port {
    final uri = Uri.parse(baseUrl);
    return uri.port;
  }

  @override
  String toString() => '$name ($baseUrl)';
}
