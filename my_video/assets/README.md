# Assets Directory

This directory contains all the static assets used in the Flutter application.

## Directory Structure

### fonts/
Contains custom font files used in the application.
- `arial/` - Arial font family files
- `din_round/` - DIN Round font family files

**Font Files Expected:**
- Arial-Light.ttf (weight: 200)
- Arial-Regular.ttf (weight: 400)
- Arial-Medium.ttf (weight: 600)
- Arial-Bold.ttf (weight: 800)
- DINRoundPro-Light.ttf (weight: 200)
- DINRoundPro.ttf (weight: 400)
- DINRoundPro-Medium.ttf (weight: 600)
- DINRoundPro-Bold.ttf (weight: 800)

### icons/
Contains icon files (PNG, SVG) used throughout the app.
- App icons
- UI icons
- Navigation icons
- Status icons

### images/
Contains image assets like:
- App logo
- Background images
- Placeholder images
- Illustrations
- Screenshots

### animation/
Contains animation files:
- Lottie animation files (.json)
- GIF files
- Other animation assets

### data/
Contains static data files:
- JSON configuration files
- Mock data files
- Static content files

## Usage

Assets are referenced in the `pubspec.yaml` file and can be accessed in the code using:

```dart
// For images
Image.asset('assets/images/logo.png')

// For icons
SvgPicture.asset('assets/icons/home.svg')

// For animations
Lottie.asset('assets/animation/loading.json')

// Using constants
Image.asset(AppAssetsConstants.imageLogo)
```

## Adding New Assets

1. Place the asset file in the appropriate directory
2. Update `pubspec.yaml` if needed (for new directories)
3. Update `AppAssetsConstants` class with the new asset path
4. Use the asset in your code

## Optimization

- Optimize images for different screen densities (1x, 2x, 3x)
- Use appropriate image formats (PNG for transparency, JPG for photos)
- Compress images to reduce app size
- Use SVG for scalable icons when possible
