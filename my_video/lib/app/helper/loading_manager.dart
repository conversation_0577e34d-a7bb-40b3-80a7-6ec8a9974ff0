import 'package:my_video/app_imports.dart';

class LoadingManager {
  static OverlayEntry? _overlayEntry;
  static bool _isShowing = false;

  static void show({String? message}) {
    if (_isShowing) return;

    _isShowing = true;
    _overlayEntry = OverlayEntry(
      builder: (context) => _LoadingOverlay(message: message),
    );

    Overlay.of(Get.context!).insert(_overlayEntry!);
  }

  static void hide() {
    if (!_isShowing) return;

    _isShowing = false;
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  static Widget buildLoadingWidget({
    String? message,
    double? size,
    Color? color,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: size ?? MySize.height(40),
            height: size ?? MySize.height(40),
            child: CircularProgressIndicator(
              strokeWidth: 3,
              valueColor: AlwaysStoppedAnimation<Color>(
                color ?? AppColorConstants.primaryColor,
              ),
            ),
          ),
          if (message != null) ...[
            Space.height(16),
            AppText(
              text: message,
              fontSize: MySize.fontSize(14),
              color: AppColorConstants.textSecondary,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  static Widget buildShimmerLoading({
    required Widget child,
    bool isLoading = true,
  }) {
    if (!isLoading) return child;

    return Shimmer.fromColors(
      baseColor: AppColorConstants.cardColor,
      highlightColor: AppColorConstants.surfaceColor,
      child: child,
    );
  }

  static Widget buildMovieCardShimmer() {
    return Container(
      width: MySize.width(140),
      margin: EdgeInsets.only(right: MySize.width(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: MySize.height(200),
            decoration: BoxDecoration(
              color: AppColorConstants.cardColor,
              borderRadius: BorderRadius.circular(MySize.radius(12)),
            ),
          ),
          Space.height(8),
          Container(
            height: MySize.height(16),
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColorConstants.cardColor,
              borderRadius: BorderRadius.circular(MySize.radius(4)),
            ),
          ),
          Space.height(4),
          Container(
            height: MySize.height(14),
            width: MySize.width(80),
            decoration: BoxDecoration(
              color: AppColorConstants.cardColor,
              borderRadius: BorderRadius.circular(MySize.radius(4)),
            ),
          ),
        ],
      ),
    );
  }

  static Widget buildBannerShimmer() {
    return Container(
      width: double.infinity,
      height: MySize.height(220),
      margin: EdgeInsets.symmetric(horizontal: MySize.width(16)),
      decoration: BoxDecoration(
        color: AppColorConstants.cardColor,
        borderRadius: BorderRadius.circular(MySize.radius(16)),
      ),
    );
  }

  static Widget buildListShimmer({int itemCount = 5}) {
    return ListView.builder(
      itemCount: itemCount,
      itemBuilder: (context, index) {
        return Container(
          margin: EdgeInsets.symmetric(
            horizontal: MySize.width(16),
            vertical: MySize.height(8),
          ),
          child: Row(
            children: [
              Container(
                width: MySize.width(60),
                height: MySize.height(60),
                decoration: BoxDecoration(
                  color: AppColorConstants.cardColor,
                  borderRadius: BorderRadius.circular(MySize.radius(8)),
                ),
              ),
              Space.width(12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: MySize.height(16),
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: AppColorConstants.cardColor,
                        borderRadius: BorderRadius.circular(MySize.radius(4)),
                      ),
                    ),
                    Space.height(8),
                    Container(
                      height: MySize.height(14),
                      width: MySize.width(120),
                      decoration: BoxDecoration(
                        color: AppColorConstants.cardColor,
                        borderRadius: BorderRadius.circular(MySize.radius(4)),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class _LoadingOverlay extends StatelessWidget {
  final String? message;

  const _LoadingOverlay({this.message});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black.withValues(alpha: 0.5),
      child: Center(
        child: Container(
          padding: EdgeInsets.all(MySize.width(24)),
          decoration: BoxDecoration(
            color: AppColorConstants.surfaceColor,
            borderRadius: BorderRadius.circular(MySize.radius(16)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: MySize.height(40),
                height: MySize.height(40),
                child: const CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppColorConstants.primaryColor,
                  ),
                ),
              ),
              if (message != null) ...[
                Space.height(16),
                AppText(
                  text: message!,
                  fontSize: MySize.fontSize(16),
                  color: AppColorConstants.textPrimary,
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
