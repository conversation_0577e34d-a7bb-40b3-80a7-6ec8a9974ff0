/* eslint-disable */
import React, { useEffect } from "react";
// @mui material components
import BookmarkAddedIcon from "@mui/icons-material/BookmarkAdded";
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";

import useMediaQuery from "@material-ui/core/useMediaQuery";
import { useTheme } from "@material-ui/core/styles";
// Material Dashboard 2 React components
import profile from "../../assets/images/profile/porfile.png";
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import MDAvatar from "components/MDAvatar";
import MDButton from "components/MDButton";
import MDInput from "components/MDInput";
// Material Dashboard 2 React example components
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import Footer from "examples/Footer";
import DataTable from "examples/Tables/DataTable";
import { useSelector, useDispatch } from "react-redux";
import { fetchUsers } from "../../redux/userslice";
import { useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import SearchBar from "material-ui-search-bar";
//import SearchBar from 'search-bar-react'
import Uploadpost from "./uploadpost";
import { TextField } from "@material-ui/core";
import "./uploadpost/postform.css";
import { useCookies } from "react-cookie";
import InputBase from "@mui/material/InputBase";
import Paper from "@mui/material/Paper";
import IconButton from "@mui/material/IconButton";
import SearchIcon from "@mui/icons-material/Search";

//require("dotenv").config()
function addpost() {
  const [page, setpage] = useState(1);
  const { users } = useSelector((state) => state);
  const [finaldata, setfinaldata] = useState(null);
  const [filterdata, setfilterdata] = useState(null);
  const [data, setdata] = useState(null);
  const [userid, setuserid] = useState("");
  const [cookie, setcookie] = useCookies();
  const token = cookie.admin;
  const [searchkey, setsearchkey] = useState(null);
  const [dummykey, setdummykey] = useState(null);
  const form = new FormData();
  form.append("key", searchkey);
  const dispatch = useDispatch();

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  useEffect(() => {
    dispatch(fetchUsers({ page, token, form })).then((e) =>
      setfinaldata(e.payload.result)
    );
  }, [dispatch, page, searchkey]);

  useEffect(() => {
    if (cookie.userId) {
      setuserid(cookie.userId);
    } else {
      setuserid(null);
    }
    if (cookie.userName) {
      setdummykey(cookie.userName);
    } else {
      setdummykey(null);
    }
  }, [cookie.userId, cookie.userName]);
  //  useEffect(()=>{
  //   setfinaldata(users?.users?.result)
  //   console.log(finaldata)
  //  },[])
  const Author = ({ image, name, email }) => {
    return (
      <MDBox display="flex" alignItems="center" lineHeight={1}>
        <MDAvatar src={image} name={name} size="lg" />
        <MDBox ml={2} lineHeight={1}>
          <MDTypography display="block" variant="button" fontWeight="medium">
            {name}
          </MDTypography>
          <MDTypography variant="caption">{email}</MDTypography>
        </MDBox>
      </MDBox>
    );
  };

  const add = (id, fname, lname) => {
    setsearchkey("");
    setdummykey(fname + " " + lname);
    setcookie("userId", id, {
      path: "/",
      maxAge: 2592000,
    });
    setcookie("userName", fname + " " + lname, {
      path: "/",
      maxAge: 2592000,
    });
  };
  const searchHanlde = (e) => {
    setdummykey(e.target.value);
    if (e.target.value.length > 2) {
      setsearchkey(e.target.value);
    } else {
      setsearchkey(null);
    }
  };
  // if(finaldata==null){
  //   return <h1>loading..</h1>
  // }
  return (
    <DashboardLayout>
      <ToastContainer />
      <DashboardNavbar />
      <MDBox pt={6} pb={3} sx={{ maxWidth: 1500 }}>
        <Grid container spacing={6}>
          <Grid item xs={12}>
            <Card sx={{ minHeight: isMobile ? "100%" : 800 }}>
              <MDBox
                mx={2}
                mt={-3}
                py={3}
                px={2}
                variant="gradient"
                bgColor="error"
                borderRadius="lg"
                coloredShadow="dark"
              >
                <MDTypography variant="h6" color="white">
                  Add Post
                </MDTypography>
              </MDBox>
              <MDBox
                width="100%"
                display="flex"
                flexDirection="space-around"
                align=""
                mt={2}
                lineHeight={1.25}
              >
                <MDBox width="100%" align="center" ml={5} lineHeight={1.25}>
                  <MDTypography
                    variant="h6"
                    width={isMobile ? "90%" : "500px"}
                    align="right"
                    flexDirection="right"
                    mb={2}
                    fontWeight="medium"
                    marginRight={isMobile ? "0" : "400px"}
                  >
                    <Paper
                      component=""
                      sx={{
                        p: "2px 4px",
                        display: isMobile ? "block" : "flex",
                        alignItems: "center",
                        width: isMobile ? "100%" : 400,
                      }}
                    >
                      <InputBase
                        sx={{ ml: 1, flex: 1 }}
                        placeholder="Search User Here..."
                        inputProps={{ "aria-label": "search user here.." }}
                        onChange={searchHanlde}
                        value={dummykey}
                      />
                      <IconButton
                        type="button"
                        sx={{ p: "10px" }}
                        aria-label="search"
                      >
                        <SearchIcon />
                      </IconButton>
                    </Paper>

                    {finaldata?.length > 0 && searchkey?.length > 2 && (
                      <MDBox width="80%" pt={2}>
                        {
                          <DataTable
                            table={{
                              columns: [
                                {
                                  Header: "name",
                                  accessor: "author",
                                  width: "0",
                                  align: "left",
                                },
                                {
                                  Header: "action",
                                  accessor: "action",
                                  align: "center",
                                },
                              ],
                              rows: finaldata?.map((e) => {
                                return {
                                  author: (
                                    <Author
                                      image={
                                        e.profile == "" ? profile : e.profile
                                      }
                                      name={`${e.fname + " " + e.lname}`}
                                      email={e.email}
                                    />
                                  ),

                                  action: (
                                    <MDBox
                                      ml={-1}
                                      onClick={() => {
                                        add(e.id, e.fname, e.lname);
                                      }}
                                    >
                                      {userid == e.id ? (
                                        <button className="btn btn-sm btn-transparent px-4">
                                          <BookmarkAddedIcon fontSize="large" />
                                        </button>
                                      ) : (
                                        <button
                                          className="btn btn-sm btn-success px-4"
                                          onClick={() =>
                                            add(e.id, e.fname, e.lname)
                                          }
                                        >
                                          add
                                        </button>
                                      )}
                                    </MDBox>
                                  ),
                                };
                              }),
                            }}
                            isSorted={false}
                            entriesPerPage={false}
                            showTotalEntries={false}
                            noEndBorder
                            maxH={250}
                          />
                        }
                      </MDBox>
                    )}
                  </MDTypography>
                </MDBox>
              </MDBox>
              <MDBox display="flex" width="100%">
                {userid != null ? (
                  <MDBox pt={5} width="100%" align="center">
                    <MDBox pt={3} width="80%">
                      <Uploadpost id={userid} />
                    </MDBox>
                  </MDBox>
                ) : (
                  <>
                    {searchkey?.length < 3 || userid == null ? (
                      <MDBox
                        width="100%"
                        height="100%"
                        align="center"
                        justifyContent="column"
                      >
                        <MDTypography variant="h2" mt={20} color="black">
                          select user to upload post
                        </MDTypography>
                      </MDBox>
                    ) : (
                      ""
                    )}
                  </>
                )}
              </MDBox>
            </Card>
          </Grid>
        </Grid>
      </MDBox>
      <Footer />
    </DashboardLayout>
  );
}

export default addpost;
