// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'movie_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MovieModelAdapter extends TypeAdapter<MovieModel> {
  @override
  final int typeId = 0;

  @override
  MovieModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MovieModel(
      id: fields[0] as String,
      title: fields[1] as String,
      description: fields[2] as String?,
      youtubeUrl: fields[3] as String,
      thumbnailUrl: fields[4] as String,
      category: fields[5] as String,
      duration: fields[6] as String?,
      rating: fields[7] as double?,
      releaseYear: fields[8] as int?,
      genre: (fields[9] as List?)?.cast<String>(),
      isFeatured: fields[10] as bool,
      viewCount: fields[11] as int?,
      createdAt: fields[12] as DateTime?,
      updatedAt: fields[13] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, MovieModel obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.youtubeUrl)
      ..writeByte(4)
      ..write(obj.thumbnailUrl)
      ..writeByte(5)
      ..write(obj.category)
      ..writeByte(6)
      ..write(obj.duration)
      ..writeByte(7)
      ..write(obj.rating)
      ..writeByte(8)
      ..write(obj.releaseYear)
      ..writeByte(9)
      ..write(obj.genre)
      ..writeByte(10)
      ..write(obj.isFeatured)
      ..writeByte(11)
      ..write(obj.viewCount)
      ..writeByte(12)
      ..write(obj.createdAt)
      ..writeByte(13)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MovieModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MovieModel _$MovieModelFromJson(Map<String, dynamic> json) => MovieModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      youtubeUrl: json['youtube_url'] as String,
      thumbnailUrl: json['thumbnail_url'] as String,
      category: json['category'] as String,
      duration: json['duration'] as String?,
      rating: (json['rating'] as num?)?.toDouble(),
      releaseYear: (json['release_year'] as num?)?.toInt(),
      genre:
          (json['genre'] as List<dynamic>?)?.map((e) => e as String).toList(),
      isFeatured: json['is_featured'] as bool? ?? false,
      viewCount: (json['view_count'] as num?)?.toInt(),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$MovieModelToJson(MovieModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'youtube_url': instance.youtubeUrl,
      'thumbnail_url': instance.thumbnailUrl,
      'category': instance.category,
      'duration': instance.duration,
      'rating': instance.rating,
      'release_year': instance.releaseYear,
      'genre': instance.genre,
      'is_featured': instance.isFeatured,
      'view_count': instance.viewCount,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };
