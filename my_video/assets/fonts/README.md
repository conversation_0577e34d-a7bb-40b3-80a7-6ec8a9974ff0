# Fonts Directory

Place your custom font files here.

## Expected Font Files

### Arial Font Family
Place in `arial/` subdirectory:
- Arial-Light.ttf (weight: 200)
- Arial-Regular.ttf (weight: 400)
- Arial-Medium.ttf (weight: 600)
- Arial-Bold.ttf (weight: 800)

### DIN Round Font Family
Place in `din_round/` subdirectory:
- DINRoundPro-Light.ttf (weight: 200)
- DINRoundPro.ttf (weight: 400)
- DINRoundPro-Medium.ttf (weight: 600)
- DINRoundPro-Bold.ttf (weight: 800)

## Usage

Fonts are already configured in `pubspec.yaml` and can be used with:

```dart
Text(
  'Hello World',
  style: TextStyle(
    fontFamily: AppAssetsConstants.defaultFont, // Arial
    fontWeight: FontWeight.w400,
  ),
)

// Or using the secondary font
Text(
  'Hello World',
  style: TextStyle(
    fontFamily: AppAssetsConstants.secondaryFont, // DinRound
    fontWeight: FontWeight.w600,
  ),
)
```

## Adding New Fonts

1. Create a new subdirectory for the font family
2. Add font files to the subdirectory
3. Update `pubspec.yaml` fonts section
4. Update `AppAssetsConstants` with the new font family name
