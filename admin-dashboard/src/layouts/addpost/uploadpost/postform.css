.custominput{
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  z-index: -1;
}
.custominputlable {
margin-left:150px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  color: #fff;
  font-size: 14px;
  padding: 10px 12px;
  background-color: #888684;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25);
}
.filename p {
  font-size: 17px;
  width: 200px;
  display: flex;
  text-align: left;
  position: absolute;
  bottom: 97px;
  right: 400px;
 
  

}
.filename2 p {
  font-size: 13px;
  width: 200px;
  display: flex;
  text-align: left;
  position: absolute;
  bottom: 50px;
  right: 358px;
}
.button {
  position: absolute;
  bottom: -10px;
}
.error{
  
  border: 1px solid red;
}
.errorv {
  position: absolute;
  font-size: 14px;
  top: 265px;
  right: 270px;
  color: red;
  display: none;
}
.errorfile{
  font-size: 17px;
  width: 200px;
  color: red;
  display: flex;
  text-align: left;
  position: relative;
  bottom: 97px;
  /* right: 60px; */
  display: none;
}
.thumbnail-image {
  
  border:20px solid white;
  
}
.thumbnail-div {
  width: 100px;
  position: absolute;
  left: 400px;
  top:0px;
 
 
}

.episode_loader{
  border: 3px solid #f3f3f3;
  border-radius: 50%;
  border-top: 3px solid #648666;
  width: 20px;
  height: 20px;
  -webkit-animation: spin 2s linear infinite; /* Safari */
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}