# 🧪 Authentication Flow Testing Guide

## 🚀 Quick Start

Your Flutter app now has a complete authentication flow! Here's how to test it:

### 1. **Start Your Backend Server**
```bash
cd movie_site
npm start
# Server should run on http://localhost:8010
```

### 2. **Run Your Flutter App**
```bash
cd my_video
flutter run
```

## 📱 Expected App Flow

### **First Launch (No User Logged In)**
1. **Splash Screen** → Shows for 2 seconds with loading animation
2. **Login Page** → User sees login form (email + password)

### **Registration Flow**
1. From Login page, tap **"Sign Up"**
2. Fill out the registration form:
   - ✅ **First Name** (required)
   - ✅ **Last Name** (optional)
   - ✅ **Gender** (Male/Female radio buttons)
   - ✅ **Email** (required)
   - ✅ **Password** (required)
   - ✅ **Confirm Password** (required)
   - ✅ **Accept Terms** (required checkbox)
3. Tap **"Sign Up"**
4. **Success**: Automatically logged in → Navigate to Main Navigation
5. **Error**: Shows error message from backend

### **Login Flow**
1. From Login page, enter:
   - ✅ **Email**
   - ✅ **Password**
2. Tap **"Sign In"**
3. **Success**: Navigate to Main Navigation
4. **Error**: Shows error message

### **Logout Flow**
1. Navigate to **Profile** page (from main navigation)
2. Tap the **red logout icon** in the app bar
3. **Success**: Navigate back to Login page
4. Token and user data cleared from storage

### **Subsequent App Launches**
- **If logged in**: Splash → Main Navigation
- **If not logged in**: Splash → Login Page

## 🔧 Backend API Integration

### **Registration/Login Endpoint**
- **URL**: `POST http://localhost:8010/login`
- **Request Body**:
```json
{
  "fname": "John",
  "lname": "Doe",
  "email": "<EMAIL>",
  "gender": "m"
}
```
- **Success Response** (Status 201):
```json
{
  "status": 1,
  "message": "user registered successfully",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "userData": {
    "id": 1,
    "fname": "John",
    "lname": "Doe",
    "email": "<EMAIL>",
    "gender": "male",
    "profile": "http://localhost:8010/upload/profile/default.jpg",
    "post_count": 0,
    "follower_count": 0,
    "following_count": 0,
    "created_date": "2024-01-01T00:00:00.000Z"
  }
}
```

### **Logout Endpoint**
- **URL**: `POST http://localhost:8010/logoutuser`
- **Headers**: `Authorization: Bearer <token>`
- **Success Response**:
```json
{
  "status": 1,
  "message": "user logout successfully"
}
```

## 🧪 Test Cases

### ✅ **Registration Tests**
- [ ] Register with valid data → Should auto-login and navigate to main app
- [ ] Register with existing email → Should show error message
- [ ] Register without accepting terms → Should show validation error
- [ ] Register with invalid email format → Should show validation error
- [ ] Register with short password → Should show validation error
- [ ] Register with mismatched passwords → Should show validation error

### ✅ **Login Tests**
- [ ] Login with valid credentials → Should navigate to main app
- [ ] Login with invalid email → Should show error message
- [ ] Login with invalid password → Should show error message
- [ ] Login with empty fields → Should show validation errors

### ✅ **Logout Tests**
- [ ] Logout from profile page → Should navigate to login page
- [ ] After logout, app restart → Should show login page (not main app)

### ✅ **Navigation Tests**
- [ ] Fresh app install → Should show login page
- [ ] After successful login → Should show main navigation
- [ ] App restart while logged in → Should show main navigation
- [ ] App restart after logout → Should show login page

### ✅ **Network Tests**
- [ ] Registration without internet → Should show "No internet" message
- [ ] Login without internet → Should show "No internet" message
- [ ] Backend server down → Should show appropriate error message

## 🐛 Troubleshooting

### **App Always Shows Main Navigation**
- ✅ **Fixed**: Updated `splash_page.dart` to check login status properly

### **Registration Doesn't Auto-Login**
- ✅ **Fixed**: Updated signup flow to save token and navigate to main app

### **Backend Connection Issues**
1. **Check Backend Server**: Ensure `movie_site` server is running on port 8010
2. **Check Base URL**: Verify `app_config.dart` has correct URL
3. **Check Network**: Test API endpoints with Postman collection

### **Token Not Persisting**
1. **Check SharedPreferences**: Verify token is saved after login
2. **Check Token Retrieval**: Verify `isLoggedIn()` method works correctly

### **Navigation Issues**
1. **Check Routes**: Verify all routes are defined in `route_helper.dart`
2. **Check Context**: Ensure `Get.context!.go()` is used correctly

## 📊 Debug Information

### **Check Login Status**
Add this debug code to see current login status:
```dart
// In any widget
final isLoggedIn = AppSharedPreference.isLoggedIn();
final token = AppSharedPreference.getUserToken();
print('Is Logged In: $isLoggedIn');
print('Token: $token');
```

### **Check User Data**
```dart
final userData = AppSharedPreference.getUserData();
print('User Data: $userData');
```

### **Clear All Data (For Testing)**
```dart
await AppSharedPreference.logout();
```

## 🎯 Next Steps

### **Immediate Testing**
1. Run the app and test the complete flow
2. Try registering a new user
3. Try logging in with existing credentials
4. Test logout functionality
5. Test app restart behavior

### **Enhancements** (Optional)
1. **Profile Image Upload**: Implement image picker for registration
2. **Remember Me**: Add remember me checkbox
3. **Biometric Auth**: Add fingerprint/face ID
4. **Social Login**: Implement Google OAuth (backend already supports it)
5. **Password Reset**: Implement when backend adds this feature

### **Production Readiness**
1. **Error Handling**: Add more specific error messages
2. **Loading States**: Improve loading indicators
3. **Validation**: Add more robust form validation
4. **Security**: Implement token refresh mechanism
5. **Analytics**: Add authentication event tracking

## 📝 Files Modified

### **Core Authentication Files**
- ✅ `lib/pages/splash/splash_page.dart` - Fixed login check
- ✅ `lib/controllers/authentication_controller.dart` - Updated signup flow
- ✅ `lib/repository/authentication/authentication_repository_impl.dart` - Backend integration
- ✅ `lib/pages/authentication/signup/signup_page.dart` - Added new fields
- ✅ `lib/pages/profile/profile_page.dart` - Added logout button

### **Configuration Files**
- ✅ `lib/app/config/app_config.dart` - Updated base URL
- ✅ `movie_site_postman_collection.json` - Updated for testing

The authentication flow is now complete and ready for testing! 🎉
