const jwt = require("jsonwebtoken")
require('dotenv').config()



const auth = (req,res,next)=>{
            
    try{
        if( req.headers.auth){

            let token = req.headers.auth
            if(token==process.env.STATIC_KEY){
                req.session.user = ""
                next()
            }else{

                if(token){
                    //console.log(req.session.user);
                    const user = jwt.verify(token,process.env.USER_SECRET); 
                    
                    
                    if(user){
                    req.session.user = user  
                    req.session.user.expires = new Date(
                        Date.now() + 3 * 
                        24 * 3600 * 1000 // session expires in 3 days
                        )
                        
                        next();
                    }
                    else{
                        res.status(409).json({status:0,message:"invalid session token"})
                    }
                    
                }
            }
                
            }else{
                res.json({status:0,message:"please provide a token"})     
            }
            }
            catch(e){
            console.log("authentication user error"+e);
            res.json({status:0,message:"invalid token "})
        }
}


module.exports = auth;
