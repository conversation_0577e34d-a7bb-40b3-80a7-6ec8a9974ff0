{"info": {"name": "Movie Site API Collection", "description": "Complete API collection for the Movie Streaming Node.js application", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "variable": [{"key": "baseUrl", "value": "https://appzone99.science:3001", "description": "Base URL for the API"}, {"key": "token", "value": "", "description": "JWT token for authentication"}, {"key": "adminToken", "value": "", "description": "Admin JWT token"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "item": [{"name": "Authentication", "item": [{"name": "Get Unregistered User Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/unregisteredusertoken", "host": ["{{baseUrl}}"], "path": ["unregisteredusertoken"]}}, "response": []}, {"name": "User Login/Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "fname", "value": "<PERSON>", "type": "text"}, {"key": "lname", "value": "<PERSON><PERSON>", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "gender", "value": "m", "type": "text"}, {"key": "profile", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/login", "host": ["{{baseUrl}}"], "path": ["login"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"<EMAIL>\",\n  \"password\": \"admin123\"\n}"}, "url": {"raw": "{{baseUrl}}/adminlogin", "host": ["{{baseUrl}}"], "path": ["adminlogin"]}}, "response": []}, {"name": "Logout User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/logoutuser", "host": ["{{baseUrl}}"], "path": ["logoutuser"]}}, "response": []}, {"name": "Logout Admin", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/logoutadmin", "host": ["{{baseUrl}}"], "path": ["logoutadmin"]}}, "response": []}]}, {"name": "Dashboard & General", "item": [{"name": "Dashboard", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/dashboard", "host": ["{{baseUrl}}"], "path": ["dashboard"]}}, "response": []}, {"name": "Dashboard Test", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/dashboard_test", "host": ["{{baseUrl}}"], "path": ["dashboard_test"]}}, "response": []}, {"name": "Test Data", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/testdata", "host": ["{{baseUrl}}"], "path": ["testdata"]}}, "response": []}]}, {"name": "Movies & Posts", "item": [{"name": "Show Posts", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"genre_id\": \"\",\n  \"cat_id\": \"\",\n  \"lang_id\": \"\"\n}"}, "url": {"raw": "{{baseUrl}}/showposts/1", "host": ["{{baseUrl}}"], "path": ["showposts", "1"]}}, "response": []}, {"name": "Show Posts V2", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"genre_id\": \"\",\n  \"cat_id\": \"\",\n  \"lang_id\": \"\"\n}"}, "url": {"raw": "{{baseUrl}}/showposts_v2/1", "host": ["{{baseUrl}}"], "path": ["showposts_v2", "1"]}}, "response": []}, {"name": "Search Posts", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"search\": \"movie title\"\n}"}, "url": {"raw": "{{baseUrl}}/searchpost/1", "host": ["{{baseUrl}}"], "path": ["searchpost", "1"]}}, "response": []}, {"name": "Search MX Video Posts", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"search\": \"movie title\"\n}"}, "url": {"raw": "{{baseUrl}}/searchpost_mxvideo/1", "host": ["{{baseUrl}}"], "path": ["searchpost_mxvideo", "1"]}}, "response": []}, {"name": "Show Genre Wise Posts", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"genre_id\": \"1\"\n}"}, "url": {"raw": "{{baseUrl}}/showgenrewise/1", "host": ["{{baseUrl}}"], "path": ["showgenrewise", "1"]}}, "response": []}, {"name": "Show Category Wise Posts", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"cat_id\": \"1\"\n}"}, "url": {"raw": "{{baseUrl}}/showcatwise/1", "host": ["{{baseUrl}}"], "path": ["showcatwise", "1"]}}, "response": []}, {"name": "Show Filters", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/showfilter", "host": ["{{baseUrl}}"], "path": ["showfilter"]}}, "response": []}, {"name": "Upload Post", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Movie Title", "type": "text"}, {"key": "caption", "value": "Movie description", "type": "text"}, {"key": "link", "value": "https://youtube.com/watch?v=example", "type": "text"}, {"key": "trailer_link", "value": "https://youtube.com/watch?v=trailer", "type": "text"}, {"key": "genre_id", "value": "1", "type": "text"}, {"key": "cat_id", "value": "1,2", "type": "text"}, {"key": "lang_id", "value": "1,2", "type": "text"}, {"key": "ismovie", "value": "true", "type": "text"}, {"key": "season", "value": "1", "type": "text"}, {"key": "thumbnail", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/uploadpost", "host": ["{{baseUrl}}"], "path": ["uploadpost"]}}, "response": []}]}, {"name": "User Management", "item": [{"name": "Show User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/showprofile/1", "host": ["{{baseUrl}}"], "path": ["showprofile", "1"]}}, "response": []}, {"name": "Update User Profile", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "fname", "value": "Updated Name", "type": "text"}, {"key": "lname", "value": "Updated Last Name", "type": "text"}, {"key": "gender", "value": "m", "type": "text"}, {"key": "profile", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/userupdate", "host": ["{{baseUrl}}"], "path": ["userupdate"]}}, "response": []}, {"name": "Follow User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"following_id\": \"2\"\n}"}, "url": {"raw": "{{baseUrl}}/followuser", "host": ["{{baseUrl}}"], "path": ["followuser"]}}, "response": []}, {"name": "Unfollow User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"following_id\": \"2\"\n}"}, "url": {"raw": "{{baseUrl}}/unfollowuser", "host": ["{{baseUrl}}"], "path": ["unfollowuser"]}}, "response": []}, {"name": "Get Followers", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"1\"\n}"}, "url": {"raw": "{{baseUrl}}/getfollower/1", "host": ["{{baseUrl}}"], "path": ["getfollower", "1"]}}, "response": []}, {"name": "Get Following", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"1\"\n}"}, "url": {"raw": "{{baseUrl}}/getfollowing/1", "host": ["{{baseUrl}}"], "path": ["getfollowing", "1"]}}, "response": []}, {"name": "Delete User Account", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/deleteuser", "host": ["{{baseUrl}}"], "path": ["deleteuser"]}}, "response": []}]}, {"name": "Post Interactions", "item": [{"name": "Like Post", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"post_id\": \"1\"\n}"}, "url": {"raw": "{{baseUrl}}/likepost", "host": ["{{baseUrl}}"], "path": ["likepost"]}}, "response": []}, {"name": "Dislike Post", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"post_id\": \"1\"\n}"}, "url": {"raw": "{{baseUrl}}/dislikepost", "host": ["{{baseUrl}}"], "path": ["dislikepost"]}}, "response": []}, {"name": "View Post", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"post_id\": \"1\"\n}"}, "url": {"raw": "{{baseUrl}}/postview", "host": ["{{baseUrl}}"], "path": ["postview"]}}, "response": []}, {"name": "Get Likes Count", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/likescount/1", "host": ["{{baseUrl}}"], "path": ["likescount", "1"]}}, "response": []}, {"name": "Report Post", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"post_id\": \"1\",\n  \"reason\": \"Inappropriate content\"\n}"}, "url": {"raw": "{{baseUrl}}/reportpost", "host": ["{{baseUrl}}"], "path": ["reportpost"]}}, "response": []}, {"name": "Delete User Post", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"post_id\": \"1\"\n}"}, "url": {"raw": "{{baseUrl}}/deleteuserpost", "host": ["{{baseUrl}}"], "path": ["deleteuserpost"]}}, "response": []}]}, {"name": "Admin - User Management", "item": [{"name": "Admin Show Users", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"search\": \"\"\n}"}, "url": {"raw": "{{baseUrl}}/adminshowuser/1", "host": ["{{baseUrl}}"], "path": ["adminshowuser", "1"]}}, "response": []}, {"name": "Admin Show Individual User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/adminshowinduser/1", "host": ["{{baseUrl}}"], "path": ["adminshowinduser", "1"]}}, "response": []}, {"name": "Admin Update User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "body": {"mode": "formdata", "formdata": [{"key": "user_id", "value": "1", "type": "text"}, {"key": "fname", "value": "Updated Name", "type": "text"}, {"key": "lname", "value": "Updated Last Name", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "profile", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/adminupdateuser", "host": ["{{baseUrl}}"], "path": ["adminupdateuser"]}}, "response": []}, {"name": "Admin Delete User", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"1\"\n}"}, "url": {"raw": "{{baseUrl}}/admindeleteuser", "host": ["{{baseUrl}}"], "path": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "response": []}, {"name": "Block User by Admin", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"1\"\n}"}, "url": {"raw": "{{baseUrl}}/blockbyadmin", "host": ["{{baseUrl}}"], "path": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "response": []}, {"name": "Unblock User by <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"1\"\n}"}, "url": {"raw": "{{baseUrl}}/unblockbyadmin", "host": ["{{baseUrl}}"], "path": ["unblockbyadmin"]}}, "response": []}]}, {"name": "Admin - Post Management", "item": [{"name": "Admin Show Posts", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"search\": \"\"\n}"}, "url": {"raw": "{{baseUrl}}/adminshowpost/1", "host": ["{{baseUrl}}"], "path": ["adminshowpost", "1"]}}, "response": []}, {"name": "Admin Show Individual Post", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/adminshowposts/1", "host": ["{{baseUrl}}"], "path": ["adminshowposts", "1"]}}, "response": []}, {"name": "Admin Add Post", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Admin Movie Title", "type": "text"}, {"key": "caption", "value": "Movie description", "type": "text"}, {"key": "link", "value": "https://youtube.com/watch?v=example", "type": "text"}, {"key": "trailer_link", "value": "https://youtube.com/watch?v=trailer", "type": "text"}, {"key": "genre_id", "value": "1", "type": "text"}, {"key": "cat_id", "value": "1,2", "type": "text"}, {"key": "lang_id", "value": "1,2", "type": "text"}, {"key": "post", "type": "file", "src": []}, {"key": "thumb", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/adminaddpost", "host": ["{{baseUrl}}"], "path": ["adminaddpost"]}}, "response": []}]}, {"name": "MX Video Management", "item": [{"name": "Create MX Video Shorts", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Episode Title\",\n  \"link\": \"https://dailymotion.com/video/example\",\n  \"thumbnail\": \"thumbnail.jpg\",\n  \"epno\": \"1\",\n  \"movieid\": \"1\"\n}"}, "url": {"raw": "{{baseUrl}}/createshorts", "host": ["{{baseUrl}}"], "path": ["createshorts"]}}, "response": []}, {"name": "Get MX Shorts Videos", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"page\": \"1\"\n}"}, "url": {"raw": "{{baseUrl}}/getmxshortsvideos", "host": ["{{baseUrl}}"], "path": ["getmxshortsvideos"]}}, "response": []}, {"name": "Get Shorts by Movie", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"movieid\": \"1\"\n}"}, "url": {"raw": "{{baseUrl}}/mxvideobymovie", "host": ["{{baseUrl}}"], "path": ["mxvideobymovie"]}}, "response": []}, {"name": "Edit MX Video", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"1\",\n  \"title\": \"Updated Episode Title\",\n  \"link\": \"https://dailymotion.com/video/updated\",\n  \"epno\": \"2\"\n}"}, "url": {"raw": "{{baseUrl}}/editmxvideo", "host": ["{{baseUrl}}"], "path": ["editmxvideo"]}}, "response": []}, {"name": "Delete MX Video", "request": {"method": "DELETE", "url": {"raw": "{{baseUrl}}/deletemxvideo/1", "host": ["{{baseUrl}}"], "path": ["deletemxvideo", "1"]}}, "response": []}]}, {"name": "Movie Analysis & Crawling", "item": [{"name": "Not Supported Movies", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/notsupportedmovies/1", "host": ["{{baseUrl}}"], "path": ["notsupportedmovies", "1"]}}, "response": []}, {"name": "Private Movies", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/privatemovies/1", "host": ["{{baseUrl}}"], "path": ["privatemovies", "1"]}}, "response": []}, {"name": "Copyright Movies", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/copyrightmovies", "host": ["{{baseUrl}}"], "path": ["copyrightmovies"]}}, "response": []}, {"name": "Admin Check Embedded", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/Admincheckembeded/1", "host": ["{{baseUrl}}"], "path": ["Admincheckembeded", "1"]}}, "response": []}, {"name": "Crawl Movies", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/crawlmovies/1", "host": ["{{baseUrl}}"], "path": ["crawlmovies", "1"]}}, "response": []}, {"name": "Get Crawl Episodes", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/getcrawlepisodes", "host": ["{{baseUrl}}"], "path": ["getcrawlepisodes"]}}, "response": []}]}, {"name": "Utility & Miscellaneous", "item": [{"name": "Add Device ID", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"device_id\": \"unique-device-id-123\"\n}"}, "url": {"raw": "{{baseUrl}}/addDeviceid", "host": ["{{baseUrl}}"], "path": ["addDeviceid"]}}, "response": []}, {"name": "Suggestion Movie", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"genre_id\": \"1\"\n}"}, "url": {"raw": "{{baseUrl}}/suggestionMovie", "host": ["{{baseUrl}}"], "path": ["suggestionMovie"]}}, "response": []}, {"name": "Get Movie List", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/getmovielist", "host": ["{{baseUrl}}"], "path": ["getmovielist"]}}, "response": []}, {"name": "Get Search History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/getsearchhistory/1", "host": ["{{baseUrl}}"], "path": ["getsearchhistory", "1"]}}, "response": []}]}]}