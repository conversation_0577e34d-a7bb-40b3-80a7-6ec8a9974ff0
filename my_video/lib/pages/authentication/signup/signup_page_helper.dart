import 'package:my_video/app_imports.dart';

class SignupPageHelper {
  final SignupPageState _state;
  final Logger _logger = Logger();

  // Loading state
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // Form controllers
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();

  // Form key
  final GlobalKey<FormState> signupFormKey = GlobalKey<FormState>();

  // UI State
  bool _isPasswordVisible = false;
  bool get isPasswordVisible => _isPasswordVisible;

  bool _isConfirmPasswordVisible = false;
  bool get isConfirmPasswordVisible => _isConfirmPasswordVisible;

  bool _acceptTerms = false;
  bool get acceptTerms => _acceptTerms;

  String _selectedGender = 'm';
  String get selectedGender => _selectedGender;

  File? _profileImage;
  File? get profileImage => _profileImage;

  // API Status
  ApiStatus apiStatus = ApiStatus.initial;

  SignupPageHelper(this._state) {
    _initializeData();
  }

  void _initializeData() {
    _logger.i('Signup page initialized');
  }

  Future<void> signup() async {
    if (!signupFormKey.currentState!.validate()) {
      return;
    }

    if (!acceptTerms) {
      AppHelper.showToast('Please accept terms and conditions', isError: true);
      return;
    }

    try {
      _setLoading(true);
      apiStatus = ApiStatus.loading;
      _state.authController.update();

      final hasInternet = await ConnectivityHelper.hasInternetConnection();
      if (!hasInternet) {
        apiStatus = ApiStatus.noInternet;
        AppHelper.showToast('no_internet_connection'.tr, isError: true);
        return;
      }

      final result = await _state.authController.signupFromAPI(
        name: firstNameController.text.trim(),
        email: emailController.text.trim(),
        password: passwordController.text.trim(),
        lastName: lastNameController.text.trim(),
        gender: selectedGender,
        profileImage: profileImage,
      );

      if (result['success'] == true) {
        final token = result['data']['token'] as String;
        final userData = result['data']['user'] as Map<String, dynamic>;

        // Save to local storage
        await AppSharedPreference.setUserToken(token);
        await AppSharedPreference.setUserData(userData);

        apiStatus = ApiStatus.success;
        AppHelper.showToast('Account created successfully! Welcome!');

        // Clear signup form
        _clearForm();

        // Navigate to main navigation (user is now logged in)
        gotoMainNavigationPage();
      } else {
        apiStatus = ApiStatus.error;
        AppHelper.showToast(
          result['message'] ?? 'signup_failed',
          isError: true,
        );
      }
    } catch (e) {
      apiStatus = ApiStatus.error;
      AppHelper.logError('Signup error', e);
      AppHelper.showToast('something_went_wrong', isError: true);
    } finally {
      _setLoading(false);
      _state.authController.update();
    }
  }

  void showTermsOfService() {
    try {
      gotoTermsOfServicePage();
    } catch (e) {
      _logger.e('Error opening Terms of Service: $e');
    }
  }

  void showPrivacyPolicy() {
    try {
      gotoPrivacyPolicyPage();
    } catch (e) {
      _logger.e('Error opening Privacy Policy: $e');
    }
  }

  void navigateToLogin() {
    try {
      gotoLoginPage();
    } catch (e) {
      _logger.e('Error navigating to login: $e');
    }
  }

  void togglePasswordVisibility() {
    _isPasswordVisible = !_isPasswordVisible;
    _state.authController.update();
  }

  void toggleConfirmPasswordVisibility() {
    _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
    _state.authController.update();
  }

  void toggleAcceptTerms([bool? value]) {
    _acceptTerms = value ?? !_acceptTerms;
    _state.authController.update();
  }

  void setGender(String gender) {
    _selectedGender = gender;
    _state.authController.update();
  }

  void setProfileImage(File? image) {
    _profileImage = image;
    _state.authController.update();
  }

  // Validation Methods
  String? validateFirstName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'First name is required';
    }
    if (value.trim().length < 2) {
      return 'First name must be at least 2 characters';
    }
    return null;
  }

  String? validateLastName(String? value) {
    // Last name is optional in backend
    if (value != null && value.trim().isNotEmpty && value.trim().length < 2) {
      return 'Last name must be at least 2 characters';
    }
    return null;
  }

  String? validateEmail(String? value) {
    return ValidationHelper.validateEmail(value);
  }

  String? validatePassword(String? value) {
    return ValidationHelper.validatePassword(value);
  }

  String? validateConfirmPassword(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Please confirm your password';
    }
    if (value != passwordController.text) {
      return 'Passwords do not match';
    }
    return null;
  }

  void _clearForm() {
    firstNameController.clear();
    lastNameController.clear();
    emailController.clear();
    passwordController.clear();
    confirmPasswordController.clear();
    _acceptTerms = false;
    _selectedGender = 'm';
    _profileImage = null;
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    _state.authController.update();
  }

  void dispose() {
    firstNameController.dispose();
    lastNameController.dispose();
    emailController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    _logger.i('Signup page helper disposed');
  }
}
