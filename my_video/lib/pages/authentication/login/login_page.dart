import 'package:my_video/app/ui/typography.dart';
import 'package:my_video/app_imports.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    MySize.init(context);
    return GetBuilder<AuthenticationController>(
      init: AuthenticationController(),
      builder: (controller) {
        return AppScaffold(
          body: SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(MySize.width(16)),
              child: Form(
                key: controller.loginFormKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const Spacer(),

                    Column(
                      children: [
                        Container(
                          width: MySize.width(80),
                          height: MySize.height(80),
                          decoration: BoxDecoration(
                            color: AppColorConstants.primaryColor,
                            borderRadius: BorderRadius.circular(
                              MySize.radius(20),
                            ),
                          ),
                          child: Icon(
                            Icons.movie_outlined,
                            size: MySize.height(40),
                            color: AppColorConstants.textPrimary,
                          ),
                        ),

                        Space.height(24),

                        AppText(
                          text: 'Welcome Back',
                          fontSize: MySize.fontSize(28),
                          fontWeight: FontWeight.bold,
                          color: AppColorConstants.textPrimary,
                          textAlign: TextAlign.center,
                        ),

                        Space.height(8),

                        AppText(
                          text: 'Sign in to continue watching',
                          fontSize: MySize.fontSize(16),
                          color: AppColorConstants.textSecondary,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),

                    const SizedBox(height: AppSizeConstants.marginExtraLarge),

                    // Email Field
                    AppTextFormField(
                      controller: controller.emailController,
                      labelText: 'email',
                      hintText: 'Enter your email',
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.next,
                      prefixIcon: const Icon(Icons.email_outlined),
                      validator: controller.validateEmail,
                    ),

                    const SizedBox(height: AppSizeConstants.marginMedium),

                    // Password Field
                    Obx(
                      () => AppTextFormField(
                        controller: controller.passwordController,
                        labelText: 'password',
                        hintText: 'Enter your password',
                        obscureText: true,
                        textInputAction: TextInputAction.done,
                        prefixIcon: const Icon(Icons.lock_outlined),
                        validator: controller.validatePassword,
                        onChanged: (value) {
                          // Handle password change if needed
                        },
                      ),
                    ),

                    const SizedBox(height: AppSizeConstants.marginSmall),

                    // Forgot Password
                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton(
                        onPressed: () => context.go(AppRoutes.forgotPassword),
                        child: const AppText(
                          text: 'Forgot Password?',
                          color: AppColorConstants.primaryColor,
                        ),
                      ),
                    ),

                    const SizedBox(height: AppSizeConstants.marginLarge),

                    // Login Button
                    Obx(
                      () => AppButton.primary(
                        text: 'sign_in',
                        onPressed: controller.apiStatus.isLoading
                            ? null
                            : controller.login,
                        isLoading: controller.apiStatus.isLoading,
                      ),
                    ),

                    const SizedBox(height: AppSizeConstants.marginMedium),

                    // Sign Up Link
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const AppText(
                          text: "Don't have an account? ",
                          color: AppColorConstants.colorGrey,
                        ),
                        TextButton(
                          onPressed: () => context.go(AppRoutes.signup),
                          child: const AppText(
                            text: 'Sign Up',
                            color: AppColorConstants.primaryColor,
                          ),
                        ),
                      ],
                    ),

                    const Spacer(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
