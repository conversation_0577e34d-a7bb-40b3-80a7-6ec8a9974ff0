# 🚀 Server Deployment Guide

## 📋 Quick Setup Checklist

### **1. Update Server URL in App**

In `my_video/lib/app/config/environment_config.dart`, update the production URL:

```dart
'production': ServerConfig(
  baseUrl: 'https://your-actual-server.com:8010/',  // ← Update this
  name: 'Production Server',
  description: 'Live production server',
),
```

### **2. Configure Environment in main.dart**

```dart
AppConfig.create(
  appName: 'MyVideo - Movie Streaming', 
  flavor: Flavor.prod,
  environment: 'production', // ← Make sure this matches your setup
);
```

## 🌐 Server Configuration Options

### **Available Environments:**

1. **`'local'`** - `http://localhost:8010/`
   - For local development
   - Use when running backend on your machine

2. **`'local_network'`** - `http://************:8010/`
   - For testing on local network
   - Use when testing on physical devices

3. **`'staging'`** - `https://staging.your-domain.com:8010/`
   - For staging environment
   - Update URL to match your staging server

4. **`'production'`** - `https://api.your-domain.com:8010/`
   - For live production server
   - **Update this URL to your actual server**

5. **`'custom'`** - Custom configuration
   - For any other server setup

## 🔧 Server Setup Steps

### **Step 1: Deploy Your Backend**

1. Upload your `movie_site` backend to your server
2. Install dependencies: `npm install`
3. Configure environment variables in `.env`
4. Start the server: `npm start` or `node app.js`

### **Step 2: Update App Configuration**

Choose one of these methods:

#### **Method A: Update Environment Config (Recommended)**
```dart
// In environment_config.dart
'production': ServerConfig(
  baseUrl: 'https://your-server.com:8010/',  // Your actual server URL
  name: 'Production Server',
  description: 'Live production server',
),
```

#### **Method B: Quick Update in main.dart**
```dart
AppConfig.create(
  appName: 'MyVideo - Movie Streaming', 
  flavor: Flavor.prod,
  environment: 'production', // or 'custom' for custom URL
);
```

### **Step 3: Test API Connection**

1. Build and run the app
2. Try logging in or signing up
3. Check logs for API connection status
4. Verify data is loading from your server

## 🔍 Common Server URL Formats

### **With Domain Name:**
```
https://api.yourdomain.com:8010/
https://yourdomain.com:8010/
http://yourdomain.com:8010/
```

### **With IP Address:**
```
https://123.456.789.012:8010/
http://123.456.789.012:8010/
```

### **With Different Ports:**
```
https://yourdomain.com/          (port 443)
http://yourdomain.com/           (port 80)
https://yourdomain.com:3000/     (custom port)
```

## 🛠️ Troubleshooting

### **Connection Issues:**
1. Check if server is running: `curl https://your-server.com:8010/`
2. Verify CORS is enabled in backend
3. Check firewall settings on server
4. Ensure SSL certificate is valid (for HTTPS)

### **API Issues:**
1. Check backend logs for errors
2. Verify API endpoints are working
3. Test with Postman or similar tool
4. Check authentication tokens

### **App Issues:**
1. Clear app data and restart
2. Check app logs for network errors
3. Verify internet connection
4. Test on different devices/networks

## 📱 Testing on Different Environments

### **Local Development:**
```dart
environment: 'local'  // http://localhost:8010/
```

### **Network Testing:**
```dart
environment: 'local_network'  // http://************:8010/
```

### **Production:**
```dart
environment: 'production'  // https://your-server.com:8010/
```

## 🔐 Security Considerations

1. **Use HTTPS** in production
2. **Configure CORS** properly in backend
3. **Secure API endpoints** with proper authentication
4. **Use environment variables** for sensitive data
5. **Enable SSL certificates** for domain-based URLs

## 📞 Need Help?

If you encounter issues:
1. Check server logs
2. Test API endpoints manually
3. Verify network connectivity
4. Check app logs for detailed error messages
