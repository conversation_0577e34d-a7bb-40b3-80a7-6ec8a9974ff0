class ApiResponseModel<T> {
  final bool success;
  final String message;
  final T? data;
  final Map<String, dynamic>? errors;
  final int? statusCode;

  ApiResponseModel({
    required this.success,
    required this.message,
    this.data,
    this.errors,
    this.statusCode,
  });

  factory ApiResponseModel.fromJson(
    Map<String, dynamic> json, {
    T Function(dynamic)? fromJsonT,
  }) {
    return ApiResponseModel<T>(
      success: json['success'] == true || json['success'] == 1,
      message: json['message']?.toString() ?? '',
      data: json['data'] != null && fromJsonT != null 
          ? fromJsonT(json['data']) 
          : json['data'] as T?,
      errors: json['errors'] as Map<String, dynamic>?,
      statusCode: json['status_code'] as int?,
    );
  }

  Map<String, dynamic> toJson({
    Map<String, dynamic> Function(T)? toJsonT,
  }) {
    return {
      'success': success,
      'message': message,
      'data': data != null && toJsonT != null ? toJsonT(data as T) : data,
      'errors': errors,
      'status_code': statusCode,
    };
  }

  ApiResponseModel<T> copyWith({
    bool? success,
    String? message,
    T? data,
    Map<String, dynamic>? errors,
    int? statusCode,
  }) {
    return ApiResponseModel<T>(
      success: success ?? this.success,
      message: message ?? this.message,
      data: data ?? this.data,
      errors: errors ?? this.errors,
      statusCode: statusCode ?? this.statusCode,
    );
  }

  @override
  String toString() {
    return 'ApiResponseModel(success: $success, message: $message, data: $data)';
  }
}

class PaginatedResponseModel<T> {
  final List<T> data;
  final int currentPage;
  final int lastPage;
  final int perPage;
  final int total;
  final bool hasMorePages;

  PaginatedResponseModel({
    required this.data,
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.total,
    required this.hasMorePages,
  });

  factory PaginatedResponseModel.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    final dataList = json['data'] as List? ?? [];
    
    return PaginatedResponseModel<T>(
      data: dataList.map((item) => fromJsonT(item as Map<String, dynamic>)).toList(),
      currentPage: json['current_page'] as int? ?? 1,
      lastPage: json['last_page'] as int? ?? 1,
      perPage: json['per_page'] as int? ?? 10,
      total: json['total'] as int? ?? 0,
      hasMorePages: (json['current_page'] as int? ?? 1) < (json['last_page'] as int? ?? 1),
    );
  }

  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJsonT) {
    return {
      'data': data.map((item) => toJsonT(item)).toList(),
      'current_page': currentPage,
      'last_page': lastPage,
      'per_page': perPage,
      'total': total,
      'has_more_pages': hasMorePages,
    };
  }

  @override
  String toString() {
    return 'PaginatedResponseModel(currentPage: $currentPage, total: $total, hasMorePages: $hasMorePages)';
  }
}
