import 'package:my_video/app_imports.dart';

// Navigation helper functions following the demo routes pattern

void gotoBack() => router.pop();

void gotoSplashPage() => router.go(AppRoutes.splash);

void gotoLoginPage() => router.go(AppRoutes.login);

void gotoSignupPage() => router.push(AppRoutes.signup);

void gotoForgotPasswordPage() => router.push(AppRoutes.forgotPassword);

void gotoMainNavigationPage() => router.go(AppRoutes.mainNavigation);

void gotoNoInternetPage() => router.push(AppRoutes.noInternet);

void gotoMoviePlayerPage(MovieModel movie) => router.push(
      AppRoutes.moviePlayer,
      extra: movie,
    );

void gotoProfilePage() => router.push(AppRoutes.profile);

void gotoTermsOfServicePage() => router.push(AppRoutes.termsOfService);

void gotoPrivacyPolicyPage() => router.push(AppRoutes.privacyPolicy);

void gotoContactSupportPage() => router.push(AppRoutes.contactSupport);
