<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link
      rel="stylesheet"
      href="http://maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css"
    />
    <script
      src="https://kit.fontawesome.com/2f78fdc272.js"
      crossorigin="anonymous"
    ></script>
    <title>StatusSaver Login</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f2f2f2;
      }

      h1 {
        text-align: center;
        margin-top: 20px;
      }

      a.google-login {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        margin: 20px auto;
        margin-top: 25px;
        background-color: #4f86ec;
        color: #fff;
        width: 200px;
        height: 40px;
        text-decoration: none;
        border-radius: 5px;
        transition: background-color 0.3s ease;
      }

      a.google-login img {
        margin-top: 1.5px;
        margin-right: 10px; /* Adjust this value as needed for spacing between image and text */
      }

      a:hover {
        background-color: #0056b3;
      }

      .maindiv {
        box-shadow: 5px 5px 10px rgba(5, 5, 5, 0.5);
        width: 450px;
        margin-left: 200px;
        margin-right: 200px;
        margin-top: 100px;
        height: 180px;
      }

      .mainsec {
        display: flex;
      }

      .header1 {
        margin-left: 250px;
        margin-top: 50px;
      }

      .header2 {
        margin-left: 50px;
        margin-top: 40px;
      }

      .header3 {
        margin-right: 200px;
        margin-top: 50px;
      }
      @media (max-width: 768px) {
        /* Adjust styles for screens smaller than 768px */

        .mainsec {
          display: block;
          width: 100%;
        }

        .appname {
          text-align: center;
          margin-left: 20px;
        }
        .header1 {
          margin-left: 20px;
          margin-top: 20px;
        }

        .header3 {
          width: 320px;
          margin-left: 20px;
          margin-top: 30px;
        }
        .maindiv {
          border: 1px solid black;
          margin-left: 10%;
          margin-right: 10%;
          width: 80%;
          margin-top: 20px;
        }

        .header2 {
          margin-left: 20px;
          margin-top: 20px;
          margin-right: 20px;
        }

        h1 {
          font-size: 24px;
        }

        a {
          padding: 8px 16px;
        }
      }

      @media (min-width: 768px) and (max-width: 1485px) {
        /* Adjust styles for screens smaller than 768px */

        .mainsec {
          display: flex;
        }

        .header1 {
          margin-left: 80px;
          margin-top: 50px;
        }

        .header3 {
          margin-right: 80px;
        }
        .maindiv {
          border: 1px solid black;
          margin-left: 5%;
          margin-right: 5%;
          width: 30%;
          margin-left: 10%;
          margin-top: 20px;
        }

        .header2 {
          margin-left: 20px;
          margin-top: 20px;
        }

        h1 {
          font-size: 24px;
        }

        a {
          padding: 8px 16px;
        }
      }

      @media (min-width: 1500px) {
        /* Adjust styles for screens smaller than 768px */

        .mainsec {
          display: flex;
        }

        .header1 {
          margin-left: 15%;
          margin-top: 50px;
        }

        .header3 {
          margin-right: 15%;
        }
        .maindiv {
          border: 1px solid black;
          margin-left: 5%;
          margin-right: 5%;
          width: 30%;
          margin-top: 50px;
        }

        .header2 {
          margin-left: 20px;
          margin-top: 20px;
        }

        h1 {
          font-size: 24px;
        }

        a {
          padding: 8px 16px;
        }
      }
    </style>
  </head>
  <body>
    <div class="mainsec">
      <h4 class="header1">
        <div>
          <img
            src="https://appzone99.science:3001/upload/thumbnail/statusic.webp"
            height="50px"
            width="50px"
          />
          <span class="appname" style="color: #008069">Status Saver</span>
          <h4 class="header3">
            Developer :
            <span style="color: #0cbfb3">Shree Ganesha Labs</span>
          </h4>
        </div>
      </h4>
      <div class="maindiv">
        <h3 class="header2">Login to Delete Your Account & Associated Data</h3>

        <a
          href="https://appzone99.science:3001/status/auth/google"
          class="google-login"
        >
          <img
            src="https://ajirayako.co.tz/wp-content/uploads/2022/07/google-g-2015-logo-png-transparent.png"
            alt="Google Logo"
            width="25"
            height="25"
          />
          <span>Login with Google</span>
        </a>
      </div>
    </div>
  </body>
</html>
