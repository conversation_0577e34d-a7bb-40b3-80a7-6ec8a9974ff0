import 'package:my_video/app_imports.dart';

class HomeController extends GetxController {
  final MovieRepository _movieRepository = GetIt.instance<MovieRepository>();
  final Logger _logger = Logger();

  Future<FeaturedMoviesResponse> getFeaturedMoviesFromAPI() async {
    try {
      return await _movieRepository.getFeaturedMovies();
    } catch (e) {
      _logger.e('Error getting featured movies from API: $e');
      rethrow;
    }
  }

  Future<CategoriesResponse> getCategoriesFromAPI() async {
    try {
      return await _movieRepository.getAllCategories();
    } catch (e) {
      _logger.e('Error getting categories from API: $e');
      rethrow;
    }
  }

  // Future<MoviesByCategoryResponse> getMoviesByCategoryFromAPI(
  //   String categoryName, {
  //   int page = 1,
  //   int perPage = 20,
  // }) async {
  //   try {
  //     return await _movieRepository.getMoviesByCategory(
  //       categoryName,
  //       page: page,
  //       perPage: perPage,
  //     );
  //   } catch (e) {
  //     _logger.e('Error getting movies for category $categoryName from API: $e');
  //     rethrow;
  //   }
  // }
}
