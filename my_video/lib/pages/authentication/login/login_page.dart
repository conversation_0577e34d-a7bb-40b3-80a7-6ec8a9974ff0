import 'package:my_video/app_imports.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => LoginPageState();
}

class LoginPageState extends State<LoginPage> {
  LoginPageHelper? _loginPageHelper;
  late AuthenticationController authController;

  @override
  void dispose() {
    _loginPageHelper?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    MySize.init(context);
    _loginPageHelper = _loginPageHelper ?? LoginPageHelper(this);
    return GetBuilder(
      init: AuthenticationController(),
      builder: (AuthenticationController controller) {
        authController = controller;
        return AppScaffold(
          backgroundColor: AppColorConstants.colorBlack,
          body: SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(MySize.width(16)),
              child: Form(
                key: _loginPageHelper!.login<PERSON><PERSON><PERSON><PERSON>,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Column(
                      children: [
                        Container(
                          width: MySize.width(80),
                          height: MySize.height(80),
                          decoration: BoxDecoration(
                            color: AppColorConstants.primaryColor,
                            borderRadius: BorderRadius.circular(
                              MySize.radius(20),
                            ),
                          ),
                          child: Icon(
                            Icons.movie_outlined,
                            size: MySize.height(40),
                            color: AppColorConstants.textPrimary,
                          ),
                        ),

                        Space.height(24),

                        AppText(
                          text: 'Welcome Back',
                          fontSize: MySize.fontSize(28),
                          fontWeight: FontWeight.bold,
                          color: AppColorConstants.textPrimary,
                          textAlign: TextAlign.center,
                        ),

                        Space.height(8),

                        AppText(
                          text: 'Sign in to continue watching',
                          fontSize: MySize.fontSize(16),
                          color: AppColorConstants.textSecondary,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),

                    const SizedBox(height: AppSizeConstants.marginExtraLarge),

                    AppTextFormField(
                      controller: _loginPageHelper!.emailController,
                      labelText: 'email',
                      hintText: 'Enter your email',
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.next,
                      prefixIcon: const Icon(Icons.email_outlined),
                      validator: _loginPageHelper!.validateEmail,
                    ),

                    const SizedBox(height: AppSizeConstants.marginMedium),

                    AppTextFormField(
                      controller: _loginPageHelper!.passwordController,
                      labelText: 'password',
                      hintText: 'Enter your password',
                      obscureText: !_loginPageHelper!.isPasswordVisible,
                      textInputAction: TextInputAction.done,
                      prefixIcon: const Icon(Icons.lock_outlined),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _loginPageHelper!.isPasswordVisible
                              ? Icons.visibility_off
                              : Icons.visibility,
                        ),
                        onPressed: _loginPageHelper!.togglePasswordVisibility,
                      ),
                      validator: _loginPageHelper!.validatePassword,
                      onChanged: (value) {
                        // Handle password change if needed
                      },
                    ),

                    const SizedBox(height: AppSizeConstants.marginSmall),

                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton(
                        onPressed: _loginPageHelper!.navigateToForgotPassword,
                        child: AppText(
                          text: 'Forgot Password?',
                          color: AppColorConstants.primaryColor,
                          textStyle: TextStyle(fontSize: MySize.size14),
                        ),
                      ),
                    ),

                    const SizedBox(height: AppSizeConstants.marginLarge),

                    _loginPageHelper!.isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : AppButton.primary(
                            text: 'sign_in',
                            onPressed: _loginPageHelper!.login,
                            isLoading: _loginPageHelper!.isLoading,
                          ),

                    const SizedBox(height: AppSizeConstants.marginMedium),

                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const AppText(
                          text: "Don't have an account? ",
                          color: AppColorConstants.colorGrey,
                        ),
                        TextButton(
                          onPressed: _loginPageHelper!.navigateToSignUp,
                          child: const AppText(
                            text: 'Sign Up',
                            color: AppColorConstants.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
