enum UserType {
  admin,
  user,
  guest,
}

extension UserTypeExtension on UserType {
  bool get isAdmin => this == UserType.admin;
  bool get isUser => this == UserType.user;
  bool get isGuest => this == UserType.guest;

  String get displayName {
    switch (this) {
      case UserType.admin:
        return 'Admin';
      case UserType.user:
        return 'User';
      case UserType.guest:
        return 'Guest';
    }
  }
}
