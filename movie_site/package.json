{"name": "express", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon app_simple.js"}, "author": "", "license": "ISC", "dependencies": {"@google-cloud/local-auth": "^3.0.1", "axios": "^1.6.2", "base64url": "^3.0.1", "bcrypt": "^5.0.1", "body-parser": "^1.20.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "dotenv": "^16.0.2", "download": "^8.0.0", "ejs": "^3.1.9", "express": "^4.18.1", "express-session": "^1.17.3", "form-data": "^4.0.0", "fs": "^0.0.1-security", "geoip-lite": "^1.4.10", "googleapis": "^140.0.0", "json-body-parser": "^1.0.2", "jsonwebtoken": "^8.5.1", "multer": "^1.4.5-lts.1", "sqlite3": "^5.1.6", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "nodemon": "^2.0.20", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "pm2": "^5.3.0", "puppeteer": "^22.6.3", "querystring": "^0.2.1", "redis": "^4.7.0", "router": "^1.3.7", "sharp": "^0.31.3", "sharp-multer": "^0.2.5"}}