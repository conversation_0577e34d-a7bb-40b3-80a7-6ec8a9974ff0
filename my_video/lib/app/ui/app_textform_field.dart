import 'package:my_video/app_imports.dart';

class AppTextFormField extends StatelessWidget {
  final String? labelText;
  final String? hintText;
  final String? helperText;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function()? onTap;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final bool obscureText;
  final bool readOnly;
  final bool enabled;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final EdgeInsetsGeometry? contentPadding;
  final InputBorder? border;
  final Color? fillColor;
  final bool filled;
  final TextStyle? textStyle;
  final TextStyle? labelStyle;
  final TextStyle? hintStyle;
  final FocusNode? focusNode;
  final TextCapitalization textCapitalization;

  const AppTextFormField({
    super.key,
    this.labelText,
    this.hintText,
    this.helperText,
    this.controller,
    this.validator,
    this.onChanged,
    this.onTap,
    this.keyboardType,
    this.textInputAction,
    this.obscureText = false,
    this.readOnly = false,
    this.enabled = true,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.prefixIcon,
    this.suffixIcon,
    this.contentPadding,
    this.border,
    this.fillColor,
    this.filled = true,
    this.textStyle,
    this.labelStyle,
    this.hintStyle,
    this.focusNode,
    this.textCapitalization = TextCapitalization.none,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      validator: validator,
      onChanged: onChanged,
      onTap: onTap,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      obscureText: obscureText,
      readOnly: readOnly,
      enabled: enabled,
      maxLines: maxLines,
      minLines: minLines,
      maxLength: maxLength,
      focusNode: focusNode,
      textCapitalization: textCapitalization,
      style:
          textStyle ??
          const TextStyle(
            fontSize: AppSizeConstants.fontSizeMedium,
            fontFamily: AppAssetsConstants.defaultFont,
            color: AppColorConstants.colorWhite,
          ),
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        helperText: helperText,
        prefixIcon: prefixIcon,
        suffixIcon: suffixIcon,
        contentPadding:
            contentPadding ??
            const EdgeInsets.symmetric(
              horizontal: AppSizeConstants.paddingMedium,
              vertical: AppSizeConstants.paddingMedium,
            ),
        border:
            border ??
            OutlineInputBorder(
              borderRadius: BorderRadius.circular(
                AppSizeConstants.radiusMedium,
              ),
              borderSide: const BorderSide(
                color: AppColorConstants.colorGrey,
                width: 1,
              ),
            ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizeConstants.radiusMedium),
          borderSide: const BorderSide(
            color: AppColorConstants.colorGrey,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizeConstants.radiusMedium),
          borderSide: const BorderSide(
            color: AppColorConstants.colorPrimary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizeConstants.radiusMedium),
          borderSide: const BorderSide(
            color: AppColorConstants.colorRed,
            width: 1,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizeConstants.radiusMedium),
          borderSide: const BorderSide(
            color: AppColorConstants.colorRed,
            width: 2,
          ),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizeConstants.radiusMedium),
          borderSide: const BorderSide(
            color: AppColorConstants.colorLightGrey,
            width: 1,
          ),
        ),
        filled: true,
        fillColor: fillColor ?? AppColorConstants.backgroundColor,
        labelStyle:
            labelStyle ??
            const TextStyle(
              fontSize: AppSizeConstants.fontSizeMedium,
              fontFamily: AppAssetsConstants.defaultFont,
              color: AppColorConstants.colorGrey,
            ),
        hintStyle:
            hintStyle ??
            const TextStyle(
              fontSize: AppSizeConstants.fontSizeMedium,
              fontFamily: AppAssetsConstants.defaultFont,
              color: AppColorConstants.colorGrey,
            ),
        helperStyle: const TextStyle(
          fontSize: AppSizeConstants.fontSizeSmall,
          fontFamily: AppAssetsConstants.defaultFont,
          color: AppColorConstants.colorGrey,
        ),
        errorStyle: const TextStyle(
          fontSize: AppSizeConstants.fontSizeSmall,
          fontFamily: AppAssetsConstants.defaultFont,
          color: AppColorConstants.colorRed,
        ),
      ),
    );
  }
}
