import Grid from "@mui/material/Grid";
import React, { useState, useEffect } from "react";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
// Material Dashboard 2 React components
import MDBox from "components/MDBox";

// Material Dashboard 2 React example components
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import Footer from "examples/Footer";
import ReportsBarChart from "examples/Charts/BarCharts/ReportsBarChart";
import ReportsLineChart from "examples/Charts/LineCharts/ReportsLineChart";
import ComplexStatisticsCard from "examples/Cards/StatisticsCards/ComplexStatisticsCard";

import useMediaQuery from "@material-ui/core/useMediaQuery";
import { useTheme } from "@material-ui/core/styles";
// Data
import reportsBarChartData from "layouts/dashboard/data/reportsBarChartData";
import reportsLineChartData from "layouts/dashboard/data/reportsLineChartData";
// API Configuration
import { API_ENDPOINTS, DEFAULT_HEADERS } from "config/api";

// Dashboard components

function Dashboard() {
  const { tasks } = reportsLineChartData;
  const [data, setData] = useState([]);
  const [userData, setUserdata] = useState({});
  const [postDatas, setPostData] = useState({});
  const [monthdata, setMonthdata] = useState({});
  const [countgenre, setcountgenre] = useState({});
  const [countlang, setcountlang] = useState({});
  const [loading, setLoading] = useState(false);
  const [selectedOption, setSelectedOption] = useState("All");
  const posts = {
    sales: {
      labels: [
        "Nov",
        "Dec",
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
      ],
      datasets: {
        label: "Users",
        data: [7, 3, 5, 2, 9, 172, 1926, 566, 597, 1928, 3121, 1907],
      },
    },
  };

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  useEffect(() => {
    setLoading(true);
    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    // Create a data object with the current date
    const year = currentDate.getFullYear();
    const month = (currentDate.getMonth() + 1).toString().padStart(2, "0");
    const day = currentDate.getDate().toString().padStart(2, "0");
    const formattedDate = `${year}-${month}-${day} 00:00:00`;

    let ismovie;
    if (selectedOption === "All") {
      ismovie = "All";
    } else if (selectedOption === "Movies") {
      ismovie = 1;
    } else if (selectedOption === "Web Series") {
      ismovie = 0;
    } else if (selectedOption === "Mxvideos") {
      ismovie = 2;
    }

    const postData = {
      date: formattedDate,
      ismovie,
    };
    // Make a POST request using fetch
    fetch(API_ENDPOINTS.DASHBOARD, {
      method: "POST",
      headers: DEFAULT_HEADERS,
      body: JSON.stringify(postData),
    })
      .then((response) => response.json())
      .then((res) => {
        // Reverse the res.months array
        const reversedMonths = res.months.slice().reverse();

        setUserdata({
          sales: {
            labels: reversedMonths, // Use the reversed array here
            datasets: {
              label: "Users",
              data: res.monthlyuser.slice().reverse(),
            },
          },
        });

        setcountgenre({
          sales: {
            labels: res.totalGenre,
            datasets: {
              label: "Users",
              data: res.countGenreWisePost,
            },
          },
        });

        setcountlang({
          sales: {
            labels: res.totallang,
            datasets: {
              label: "Users",
              data: res.countLangWisePost,
            },
          },
        });

        setPostData({
          sales: {
            labels: reversedMonths, // Use the reversed array here
            datasets: {
              label: "Posts",
              data: res.monthlypost.slice().reverse(),
            },
          },
        });
        setMonthdata({
          sales: {
            labels: res.dailyuserdates, // Use the reversed array here
            datasets: {
              label: "Daily Log in Users ",
              data: res.dailyusercounts,
            },
          },
        });
        setData(res);
        setLoading(false);
      })
      .catch((error) => console.error("Error:", error));
  }, [selectedOption]);

  const handleChange = (event) => {
    setSelectedOption(event.target.value);
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox py={3}>
        <Grid container spacing={3}>
          <Grid item xs={isMobile ? 12 : 2} md={4} lg={2} mt={-2}>
            <MDBox mb={1.5}>
              <ComplexStatisticsCard
                color="dark"
                icon="weekend"
                title="Total Posts"
                count={data?.totalpost?.toLocaleString()}
                percentage={{
                  color:
                    data.postIncreaseLastYearPercentage >= 0
                      ? "success"
                      : "error",
                  amount: `${data.postIncreaseLastYearPercentage}%`,
                  label: "than last year",
                }}
              />
            </MDBox>
          </Grid>
          <Grid item xs={isMobile ? 12 : 2} md={4} lg={2} mt={-2}>
            <MDBox mb={1.5}>
              <ComplexStatisticsCard
                icon="leaderboard"
                title="Today's Posts"
                count={data?.todaypost?.toLocaleString()}
                percentage={{
                  color: data.postIncreasePercentage >= 0 ? "success" : "error",
                  amount: data.postIncreasePercentage
                    ? `${data.postIncreasePercentage}%`
                    : "0%",
                  label: "than yesterday",
                }}
              />
            </MDBox>
          </Grid>
          <Grid item xs={isMobile ? 12 : 2} md={4} lg={2} mt={-2}>
            <MDBox mb={1.5}>
              <ComplexStatisticsCard
                color="success"
                icon="store"
                title="Total Login Users"
                count={data?.totaluser?.toLocaleString()}
                percentage={{
                  color:
                    data.userIncreaseLastYearPercentage >= 0
                      ? "success"
                      : "error",
                  amount: `${data.userIncreaseLastYearPercentage}%`,
                  label: "than last year",
                }}
              />
            </MDBox>
          </Grid>
          <Grid item xs={isMobile ? 12 : 2} md={4} lg={2} mt={-2}>
            <MDBox mb={2.5}>
              <ComplexStatisticsCard
                color="primary"
                icon="person_add"
                title="Weekly Login User"
                count={data?.todayuser?.toLocaleString()}
                percentage={{
                  color: data.userIncreasePercentage >= 0 ? "success" : "error",
                  amount: `${data.userIncreasePercentage}%`,
                  label: "than last week",
                }}
              />
            </MDBox>
          </Grid>
          <Grid item xs={isMobile ? 12 : 2} md={4} lg={2} mt={-2}>
            <MDBox mb={1.5}>
              <ComplexStatisticsCard
                color="warning"
                icon="moviecreationicon"
                title="Total Movies"
                count={data?.totalMovie?.toLocaleString()}
                percentage={{
                  color:
                    data.movieIncreaseLastYearPercentage >= 0
                      ? "success"
                      : "error",
                  amount: `${data.movieIncreaseLastYearPercentage}%`,
                  label: "than last year",
                }}
              />
            </MDBox>
          </Grid>
          <Grid item xs={isMobile ? 12 : 2} md={4} lg={2} mt={-2}>
            <MDBox mb={1.5}>
              <ComplexStatisticsCard
                color="primary"
                icon="slideshowicon"
                title="Total Web Series"
                count={data?.totalWeb?.toLocaleString()}
                percentage={{
                  color:
                    data.webIncreaseLastYearPercentage >= 0
                      ? "success"
                      : "error",
                  amount: `${data.webIncreaseLastYearPercentage}%`,
                  label: "than last year",
                }}
              />
            </MDBox>
          </Grid>
        </Grid>
        <MDBox mt={3}>
          <Grid container spacing={3}>
            {/* <Grid item xs={12} md={6} lg={4}>
              <MDBox mb={3}>
                <ReportsBarChart
                  color="info"
                  title="views"
                  description="Last Campaign Performance"
                  date="campaign sent 2 days ago"
                  chart={reportsBarChartData}
                />
              </MDBox>
            </Grid> */}
            <Grid item xs={12} md={6} lg={6} mt={-1}>
              <MDBox mb={3}>
                {loading == false && userData ? (
                  <ReportsLineChart
                    color="info"
                    title="Monthly Login Users"
                    // description={
                    //   <>
                    //     (<strong>+15%</strong>) increase in todays post.
                    //   </>
                    // }
                    // date="updated 4 min ago"
                    chart={userData.sales}
                  />
                ) : (
                  ""
                )}
              </MDBox>
            </Grid>
            <Grid item xs={12} md={6} lg={6} mt={-1}>
              <MDBox mb={3}>
                {loading == false && postDatas ? (
                  <ReportsLineChart
                    color="success"
                    title="Monthly Upload Posts"
                    // description="Last Campaign Performance"
                    // date="just updated"
                    chart={postDatas.sales}
                  />
                ) : (
                  ""
                )}
              </MDBox>
            </Grid>
          </Grid>
        </MDBox>

        <MDBox>
          <Grid>
            <MDBox mb={3} mt={3}>
              {loading == false && monthdata ? (
                <ReportsBarChart
                  color="secondary"
                  title="Daily Login Users"
                  // description="Last Campaign Performance"
                  date="campaign sent 2 days ago"
                  chart={monthdata.sales}
                />
              ) : (
                ""
              )}
            </MDBox>
          </Grid>
        </MDBox>

        {loading == false && monthdata ? (
          <Grid item xs={isMobile ? 12 : 2} md={3} lg={2} mt={5}>
            <FormControl style={{ width: "150px" }}>
              <InputLabel id="demo-simple-primary-label">select</InputLabel>
              <Select
                labelId="demo-simple-primary-label"
                id="demo-simple-select"
                value={selectedOption}
                label="select"
                onChange={handleChange}
                style={{ height: "40px", border: "#1A73EB" }}
              >
                <MenuItem value="All">All</MenuItem>
                <MenuItem value="Movies">Movies</MenuItem>
                <MenuItem value="Web Series">Web Series</MenuItem>
                <MenuItem value="Mxvideos">Mxvideos</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        ) : (
          ""
        )}

        <MDBox mt={5}>
          <Grid>
            <MDBox mb={3} mt={3}>
              {loading == false && monthdata ? (
                <ReportsBarChart
                  color="info"
                  title="Language Wise Post"
                  // description="Last Campaign Performance"
                  date="campaign sent 2 days ago"
                  chart={countlang.sales}
                />
              ) : (
                ""
              )}
            </MDBox>
          </Grid>
        </MDBox>

        <MDBox mt={5}>
          <Grid>
            <MDBox mb={3} mt={3}>
              {loading == false && countgenre ? (
                <ReportsBarChart
                  color="secondary"
                  title="Genre Wise Post"
                  date="campaign sent 2 days ago"
                  chart={countgenre.sales}
                />
              ) : (
                ""
              )}
            </MDBox>
          </Grid>
        </MDBox>
      </MDBox>
      <Footer />
    </DashboardLayout>
  );
}

export default Dashboard;
