import 'package:my_video/app_imports.dart';

class ErrorHandler {
  static final Logger _logger = Logger();

  static void handleError(dynamic error, {String? context}) {
    _logger.e('Error in $context: $error');

    String userMessage = _getUserFriendlyMessage(error);

    Get.snackbar(
      'Error',
      userMessage,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.colorRed,
      colorText: AppColorConstants.textPrimary,
      duration: const Duration(seconds: 4),
      margin: EdgeInsets.all(MySize.width(16)),
      borderRadius: MySize.radius(8),
      icon: Icon(
        Icons.error_outline,
        color: AppColorConstants.textPrimary,
        size: MySize.height(24),
      ),
    );
  }

  static String _getUserFriendlyMessage(dynamic error) {
    // Handle HTTP client errors
    if (error is ClientException) {
      return 'No internet connection. Please check your network.';
    }

    if (error is HttpException) {
      return 'Network error. Please try again.';
    }

    if (error.toString().contains('SocketException')) {
      return 'No internet connection. Please check your network.';
    }

    if (error.toString().contains('FormatException')) {
      return 'Invalid data format received.';
    }

    if (error.toString().contains('TimeoutException')) {
      return 'Request timeout. Please try again.';
    }

    return 'Something went wrong. Please try again.';
  }

  static void showNetworkError({VoidCallback? onRetry}) {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColorConstants.surfaceColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(MySize.radius(16)),
        ),
        title: Row(
          children: [
            Icon(
              Icons.wifi_off,
              color: AppColorConstants.colorRed,
              size: MySize.height(24),
            ),
            Space.width(8),
            const AppText(
              text: 'No Internet',
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
            ),
          ],
        ),
        content: const AppText(
          text: 'Please check your internet connection and try again.',
          fontSize: 14,
          color: AppColorConstants.textSecondary,
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const AppText(
              text: 'Cancel',
              color: AppColorConstants.textSecondary,
            ),
          ),
          if (onRetry != null)
            ElevatedButton(
              onPressed: () {
                Get.back();
                onRetry();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColorConstants.primaryColor,
              ),
              child: const AppText(
                text: 'Retry',
                color: AppColorConstants.textPrimary,
              ),
            ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  static void showMaintenanceDialog() {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColorConstants.surfaceColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(MySize.radius(16)),
        ),
        title: Row(
          children: [
            Icon(
              Icons.build,
              color: AppColorConstants.colorOrange,
              size: MySize.height(24),
            ),
            Space.width(8),
            const AppText(
              text: 'Under Maintenance',
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
            ),
          ],
        ),
        content: const AppText(
          text:
              'The app is currently under maintenance. Please try again later.',
          fontSize: 14,
          color: AppColorConstants.textSecondary,
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Get.back(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColorConstants.primaryColor,
            ),
            child: const AppText(
              text: 'OK',
              color: AppColorConstants.textPrimary,
            ),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  static Widget buildErrorWidget({
    required String message,
    VoidCallback? onRetry,
    IconData? icon,
  }) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(MySize.width(24)),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon ?? Icons.error_outline,
              size: MySize.height(80),
              color: AppColorConstants.colorRed,
            ),
            Space.height(16),
            AppText(
              text: 'Oops! Something went wrong',
              fontSize: MySize.fontSize(18),
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
              textAlign: TextAlign.center,
            ),
            Space.height(8),
            AppText(
              text: message,
              fontSize: MySize.fontSize(14),
              color: AppColorConstants.textSecondary,
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              Space.height(24),
              AppButton(
                text: 'Try Again',
                onPressed: onRetry,
                backgroundColor: AppColorConstants.primaryColor,
                icon: Icon(
                  Icons.refresh,
                  color: AppColorConstants.textPrimary,
                  size: MySize.height(20),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  static Widget buildNoDataWidget({
    required String title,
    required String message,
    VoidCallback? onAction,
    String? actionText,
    IconData? icon,
  }) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(MySize.width(24)),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon ?? Icons.inbox_outlined,
              size: MySize.height(80),
              color: AppColorConstants.textHint,
            ),
            Space.height(16),
            AppText(
              text: title,
              fontSize: MySize.fontSize(18),
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
              textAlign: TextAlign.center,
            ),
            Space.height(8),
            AppText(
              text: message,
              fontSize: MySize.fontSize(14),
              color: AppColorConstants.textSecondary,
              textAlign: TextAlign.center,
            ),
            if (onAction != null && actionText != null) ...[
              Space.height(24),
              AppButton(
                text: actionText,
                onPressed: onAction,
                backgroundColor: AppColorConstants.primaryColor,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
