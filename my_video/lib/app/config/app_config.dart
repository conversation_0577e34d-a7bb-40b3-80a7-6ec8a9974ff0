enum Flavor { dev, prod }

class AppConfig {
  static AppConfig? _instance;
  static AppConfig get instance => _instance!;

  final String appName;
  final Flavor flavor;

  AppConfig._internal({required this.appName, required this.flavor});

  static void create({required String appName, required Flavor flavor}) {
    _instance = AppConfig._internal(appName: appName, flavor: flavor);
  }

  bool get isDev => flavor == Flavor.dev;
  bool get isProd => flavor == Flavor.prod;

  String get baseUrl {
    switch (flavor) {
      case Flavor.dev:
        return 'http://localhost:8010/'; // For iOS simulator (use ********:8010 for Android emulator)
      case Flavor.prod:
        return 'http://************:8010/'; // For physical devices on same network
    }
  }

  // Backend expects these specific endpoints
  String get loginEndpoint => 'login';
  String get logoutEndpoint => 'logoutuser';
  String get unregisteredTokenEndpoint => 'unregisteredusertoken';
}
