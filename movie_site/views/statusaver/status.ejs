<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Delete Account</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f2f2f2;
      }

      .mainsec {
        display: flex;
        width: 100%;
      }

      h1 {
        text-align: center;
        margin-top: 20px;
      }

      .header1 {
        margin-left: 250px;
        margin-top: 50px;
      }

      .header3 {
        margin-left: 50px;
        margin-top: 50px;
      }

      .maindiv {
        box-shadow: 5px 5px 10px rgba(5, 5, 5, 0.5);
        width: 450px;
        margin-left: 200px;
        margin-right: 200px;
        margin-top: 100px;
        height: 410px;
      }

      .boxheader {
        text-align: center;
      }

      .notebox {
        border: 1px solid #008069;
        margin-left: 5%;
        margin-right: 5%;
      }
      .notebox p {
        margin-left: 3%;
        margin-right: 3%;
        text-align: left;
      }

      .confirmbox {
        text-align: left;
      }

      p {
        text-align: center;
        font-size: 18px;
        margin-top: 20px;
      }

      #deleteButton {
        display: block;
        margin: 20px auto;
        padding: 10px 20px;
        background-color: #008069;
        color: #fff;
        border: none;
        border-radius: 5px;
        cursor: pointer;
      }

      #deleteButton:hover {
        background-color: #cc0000;
      }

      .checkbox {
        height: 20px;
        width: 20px;
        background-color: red;
      }
      @media (max-width: 768px) {
        .mainsec {
          display: block;
        }

        .appname {
          text-align: center;
          margin-left: 20px;
        }

        .header1 {
          margin-left: 20px;
          margin-top: 20px;
        }

        .header3 {
          width: 320px;
          margin-left: 20px;
          margin-top: 30px;
        }

        .maindiv {
          border: 1px solid black;
          margin-left: 7.5%;
          margin-right: 7.5%;
          width: 85%;
          margin-top: 20px;
          height: 430px;
        }

        p {
          font-size: 16px;
        }

        #deleteButton {
          padding: 8px 16px;
        }
      }

      @media (min-width: 768px) and (max-width: 1485px) {
        /* Adjust styles for screens smaller than 768px */

        .mainsec {
          display: flex;
        }

        .header1 {
          margin-left: 80px;
          margin-top: 50px;
        }

        .header3 {
          margin-right: 80px;
        }
        .maindiv {
          border: 1px solid black;
          margin-left: 5%;
          margin-right: 5%;
          width: 30%;
          margin-left: 10%;
          margin-top: 20px;
          height: 100%;
        }

        .header2 {
          margin-left: 20px;
          margin-top: 20px;
        }

        h1 {
          font-size: 24px;
        }

        a {
          padding: 8px 16px;
        }
      }

      @media (min-width: 1500px) {
        /* Adjust styles for screens smaller than 768px */

        .mainsec {
          display: flex;
          display: 100%;
        }

        .header1 {
          margin-left: 10%;
          margin-top: 50px;
        }

        .header3 {
          margin-right: 10%;
        }
        .maindiv {
          border: 1px solid black;
          margin-left: 7.5%;
          margin-right: 7.5%;
          width: 30%;
          margin-top: 50px;
          height: 420px;
        }

        .header2 {
          margin-left: 20px;
          margin-top: 20px;
        }

        h1 {
          font-size: 24px;
        }

        a {
          padding: 8px 16px;
        }
      }
    </style>
  </head>
  <body>
    <div class="mainsec">
      <h4 class="header1">
        <div>
          <img
            src="https://appzone99.science:3001/upload/thumbnail/statusic.webp"
            height="50px"
            width="50px"
          />
          <span class="appname" style="color: #008069">Status Saver</span>
          <h4 class="header3">
            Developer :
            <span style="color: #0cbfb3">Shree Ganesha Labs</span>
          </h4>
        </div>
      </h4>
      <div class="maindiv">
        <p>Welcome, <span id="userEmail"><%= userEmail %></span>!</p>
        <h3 class="boxheader">
          Delete Your <span style="color: #008069">Status Saver</span> Account
        </h3>

        <div class="notebox">
          <p>
            <span style="color: #008069">Note :</span>
            Deleting your account and associated data can not be undone.
          </p>
          <p>
            By continuing, you agree to allow us to delete your account and
            associated all your data like posts, profile photo, likes,
            followers, followings etc.
          </p>

          <p class="confirmbox">
            <input type="checkbox" id="confirmCheckbox" class="checkbox" />
            &nbsp;Yes, I want to permanently delete this StatusSaver Account and
            all its data.
          </p>
        </div>
        <button id="deleteButton" data-user-email="<%= userEmail %>">
          Delete Account
        </button>
      </div>
    </div>

    <script>
      // function deleteAccount(userEmail) {
      //   // Create a URLSearchParams object and append the parameters
      //   const params = new URLSearchParams();
      //   params.append("email", email);
      //   // params.append("user_id", otherParam1);
      //   params.append("keyword", "ipUR5Q7PAbWeZt4AIFH9elB8Bko=");

      //   fetch("http://socialapp.top:3001/deleteUserData", {
      //     method: "POST",
      //     headers: {
      //       "Content-Type": "application/x-www-form-urlencoded",
      //     },
      //     body: params.toString(),
      //   })
      //     .then((response) => {
      //       if (response.ok) {
      //         alert("Account deleted successfully.");
      //       } else {
      //         alert("Account does not exist");
      //       }
      //     })
      //     .catch((error) => {
      //       console.error("Error:", error);
      //     });
      // }

      // function deleteAccount(userEmail) {
      //   fetch(`https://appzone99.science:3001/deletestatus/${userEmail}`, {
      //     method: "GET",
      //     headers: {
      //       "Content-Type": "application/json",
      //     },
      //   })
      //     .then((response) => {
      //       if (response.ok) {
      //         alert(response[0].message);
      //       } else {
      //         alert("Account does not exist");
      //       }
      //     })
      //     .catch((error) => {
      //       console.error("Error:", error);
      //     });
      // }

      function deleteAccount(userEmail) {
        fetch(`https://appzone99.science:3001/deletestatus/${userEmail}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        })
          .then((response) => {
            if (response.ok) {
              return response.json(); // Parse the response as JSON
            } else {
              throw new Error("Account does not exist");
            }
          })
          .then((data) => {
            alert(data.message);
            window.location.href = "https://appzone99.science:3001/statussaver";
          })
          .catch((error) => {
            console.error("Error:", error.message);
            alert("Something Went wrong");
          });
      }

      document
        .getElementById("deleteButton")
        .addEventListener("click", function () {
          const userEmail = this.getAttribute("data-user-email");

          // Check if the checkbox is checked
          const confirmCheckbox = document.getElementById("confirmCheckbox");
          if (confirmCheckbox.checked) {
            const confirmDelete = confirm(
              "Are you sure you want to delete your account?"
            );
            if (confirmDelete) {
              deleteAccount(userEmail);
            }
          } else {
            alert("Please confirm checkbox to delete your account");
          }
        });
    </script>
  </body>
</html>
