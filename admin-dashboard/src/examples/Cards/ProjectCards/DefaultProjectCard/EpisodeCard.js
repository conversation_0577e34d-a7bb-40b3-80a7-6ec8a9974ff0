/* eslint-disable */
// react-router-dom components
import { Link } from "react-router-dom";

// prop-types is a library for typechecking of props
import PropTypes from "prop-types";
import PlayCircleOutline from "@mui/icons-material/PlayCircleOutline";
// @mui material components
import Card from "@mui/material/Card";
import CardMedia from "@mui/material/CardMedia";
import Tooltip from "@mui/material/Tooltip";
import DeleteIcon from "@mui/icons-material/Delete";
// Material Dashboard 2 React components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import MDButton from "components/MDButton";
import MDAvatar from "components/MDAvatar";
import { useNavigate } from "react-router-dom";
import "./yoyo.css";
import CreatableSelect from "react-select/creatable";
import { useEffect, useState } from "react";
import { useCookies } from "react-cookie";
import { border, height } from "@mui/system";
import { FormatIndentDecreaseTwoTone } from "@mui/icons-material";
// import ConfirmBox from "react-dialog-confirm"; // Removed - using native confirm
import Modal from "./Modal.js";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import useMediaQuery from "@material-ui/core/useMediaQuery";
import { useTheme } from "@material-ui/core/styles";

let colourStyles = {
  control: (base) => ({
    ...base,
    fontSize: "16px",
    background: "white",
    border: "none",
    padding: "-10px",
    marginBottom: "-15px",
    //border:"1px solid red"
  }),
  menuList: (styles) => ({
    ...styles,
    background: "white",
    height: "100px",
  }),
  option: (styles, { isFocused, isSelected }) => ({
    ...styles,
    background: isFocused
      ? "hsla(291, 0%, 0%, 0.1)"
      : isSelected
      ? "hsla(291, 0%, 0%, 0.1)"
      : undefined,
    position: "relative",
  }),
  menu: (base) => ({
    ...base,
    height: "100px",
    zIndex: 999,
    overFlow: "hidden",
  }),
  placeholder: () => ({
    fontSize: "13px",
    marginBottom: "-100px",
    position: "relative",
    bottom: "62px",
    left: "0px",
    fontWeight: "400",
  }),
};

const customStyles = {
  content: {
    top: "50%",
    left: "50%",
    right: "auto",
    bottom: "auto",
    marginRight: "-50%",
    transform: "translate(-50%, -50%)",
  },
};

function EpisodeCard({
  post,
  isverified,
  trailer,
  movie,
  userid,
  label,
  title,
  description,
  epno,
  action,
  genre,
  poster,
  post_id,
  movieid,
  page,
  isdeleted,
}) {
  const select = false;
  const Navigate = useNavigate();
  const [cookie, setcookie] = useCookies();
  const [result, setresult] = useState(null);
  const token = cookie.admin;
  const getfilter = async () => {
    const fetchfilter = await fetch(`${window.path}/adminshowfilters`, {
      method: "POST",
      headers: {
        auth: token,
      },
    });
    const resp = await fetchfilter.json();
    setresult(resp);
  };
  useEffect(() => {
    getfilter();
  }, []);

  const gen = result?.genre?.map((e) => {
    return { value: e.genre, label: e.genre };
  });
  var [defaultgenre, setdefaultgenre] = useState({
    label: genre,
    value: genre,
  });
  useEffect(() => {
    setdefaultgenre({ label: genre, value: genre });
  }, [genre]);
  const setgenre = async (gen) => {
    const form = new FormData();
    form.append("genre", gen.value);
    form.append("post_id", post_id);
    const updategen = await fetch(`${window.path}/adminupdatepostgenre`, {
      method: "post",
      headers: {
        auth: token,
      },
      body: form,
    });
    const response = await updategen.json();
  };
  const onchangegenre = (e) => {
    setdefaultgenre(e);
    setgenre(e);
  };
  const [deletee, setdeletee] = useState(false);
  const del = async () => {
    let form = new FormData();
    form.append("dest", post_id);
    form.append("id", userid);
    //console.log(form.get("id"))
    const res = await fetch(`${window.path}/admindeletepost`, {
      method: "DELETE",
      headers: {
        auth: token,
      },
      body: form,
    });
    const delres = await res.json();
    if (delres.status === 1) {
      // toast.success("deleted succefully",{
      //   position: toast.POSITION.TOP_CENTER,
      //   onClose: () => {
      //   window.location.reload()
      //   },

      //   autoClose: 100,

      // });
      window.location.reload();
    } else {
      toast("invalid credintial");
    }
  };
  if (deletee == true) {
    del();
  }
  const [isOpen, setIsOpen] = useState(false);

  const handleClose = () => {
    setIsOpen(!isOpen);
  };

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <>
      {isOpen && <Modal setOpenModal={setIsOpen} setdelete={setdeletee} />}

      <div className="row">
        <ToastContainer />
        <div className="col-md-4 col-sm-12 col-lg-4  ">
          <div className="card card__dark card__dark--magenta bg-white">
            {select === true ? (
              <div className="d-flex justify-content-center p-2 ">
                <input className="" type="checkbox" value="" onChange="" />
              </div>
            ) : (
              ""
            )}

            <div
              className="media media--16-9"
              style={{
                borderTopLeftRadius: "15px",
                borderTopRightRadius: "15px",
              }}
              align="center"
              onClick={() => {
                trailer != null ? window.open(trailer, "blank") : window.open(poster, "blank");
              }}
            >
              {trailer != null ? (
                <>
                  {/* {isverified === "false" && (
                    <button
                      style={{
                        position: "absolute",
                        top: "10px",
                        right: "10px",
                        backgroundColor: "red",
                        color: "white",
                        border: "none",
                        borderRadius: "5px",
                        fontSize: "15px",
                        height: "35px",
                        width: "80px",
                        zIndex: 3,
                        opacity: 0.9,
                      }}
                    >
                      Unverified
                    </button>
                  )} */}

                  <button className="play-button">
                    <PlayCircleOutline />
                  </button>
                  <video width="300" alt="video not found" poster={poster}>
                    <source src={trailer} type="video/mp4" alt="video not found"></source>
                  </video>
                </>
              ) : (
                <img
                  src={poster}
                  alt=""
                  className="postimage"
                  style={{
                    objectFit: "contain",
                    width: "100%",
                    height: "180px", // Set a fixed height
                    maxWidth: "500px",
                    backgroundColor: "#f0f0f0", // Optional: background color for letterboxing
                  }}
                />
              )}
            </div>
            <div className="primary-title " style={{ marginTop: "-15px" }}>
              {/* <div className="primary-text">
                <b>{isverified}</b>
              </div> */}
              <div className="primary-text">
                <b>{title.length > 54 ? title.slice(0, 28) + "..." : title}</b>
              </div>
              <div className="primary-text mt-2">
                User :{userid.length > 54 ? userid.slice(0, 28) + "..." : userid}
              </div>
              <div className="primary-text mt-2">
                Episode No : &nbsp;
                <span style={{ color: "red" }}>
                  {" "}
                  <b> {epno.length > 54 ? epno.slice(0, 28) + "..." : epno}</b>
                </span>
              </div>
            </div>

            <div className="actions border-top d-flex">
              <button
                style={{
                  background: "rgb(25, 135, 84)",
                  border: "none",
                  width: "70px",
                  fontSize: "16px",
                  color: "white",
                  borderRadius: "5px",
                  height: "36px",
                  marginLeft: "10px",
                }}
                type="button"
                onClick={() => {
                  window.open(`/posts/episodes/${movieid}`, "_blank"); // Replace with your actual route
                }}
              >
                Details
              </button>

              <button
                style={{
                  background: " rgb(255, 152, 0)",
                  border: "none",
                  width: "130px",
                  fontSize: "16px",
                  color: "white",
                  borderRadius: "5px",
                  height: "36px",
                  marginLeft: "10px",
                }}
                type="button"
                onClick={() => {
                  window.open(movie, "_blank");
                }}
              >
                View Episode
              </button>
              <div style={{ width: "30px", height: "30px", marginLeft: "20px" }}>
                <button
                  className=""
                  style={{
                    width: "35px",
                    height: "35px",
                    marginLeft: "-10px",
                    backgroundColor: "red",
                    color: "white",
                    borderRadius: "50%",
                    border: "1px red",
                    fontSize: "25px",
                    marginBottom: "-10px",
                    marginTop: "0px",
                  }}
                  type="button"
                  onClick={handleClose}
                >
                  <DeleteIcon style={{ marginBottom: "8px", marginTop: "0px" }} />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// Setting default values for the props of DefaultProjectCard
EpisodeCard.defaultProps = {
  authors: [],
};

// Typechecking props for the DefaultProjectCard
EpisodeCard.propTypes = {
  image: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  epno: PropTypes.string.isRequired,
  movieid: PropTypes.string.isRequired,
  action: PropTypes.shape({
    type: PropTypes.oneOf(["external", "internal"]),
    route: PropTypes.string.isRequired,
    color: PropTypes.oneOf([
      "primary",
      "secondary",
      "info",
      "success",
      "warning",
      "error",
      "light",
      "dark",
      "white",
    ]).isRequired,
    label: PropTypes.string.isRequired,
  }).isRequired,
  authors: PropTypes.arrayOf(PropTypes.object),
};

export default EpisodeCard;
