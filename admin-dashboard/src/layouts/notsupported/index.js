/* eslint-disable */
import React, { useEffect, useState, useRef } from "react";
import Grid from "@mui/material/Grid";
import { useTheme } from "@material-ui/core/styles";
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import Footer from "examples/Footer";
import DefaultProjectCard from "examples/Cards/ProjectCards/DefaultProjectCard";
import { ToastContainer } from "react-toastify";
import Header from "./components/Header";
import { Link, useLocation, useSearchParams } from "react-router-dom";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";

import Button from "@mui/material/Button";

function notsupported() {
  const [isShow, setIsShow] = useState(false);
  const [finaldata, setfinaldata] = useState([]);
  const [Loading, setloading] = useState(true);
  const [searchkey, setsearchkey] = useState(null);
  const [category, setCategory] = useState("notsupported");
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [total, settotal] = useState(0);

  const searchHanlde = (e) => {
    e.preventDefault();
    setPage(1);
    if (e.target.value) {
      setsearchkey(e.target.value);
    } else {
      setsearchkey("");
    }
  };

  const notsupported = async () => {
    try {
      setloading(true);
      const jsonBody = {
        key: searchkey,
      };
      const response = await fetch(
        `http://206.189.137.63:8010/notsupportedmovies/${page}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(jsonBody),
        }
      );

      if (!response.ok) {
        setloading(false);
        settotal(0);
        setfinaldata([]);
        throw new Error("Network response was not ok");
      }

      const jsonData = await response.json();

      if (jsonData.result === 0) {
        setHasMore(false);
      } else {
        settotal(jsonData.total);
        setfinaldata(jsonData.data);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setloading(false);
    }
  };

  useEffect(() => {
    if (category === "country") {
      countrywiserestric();
    } else if (category === "private") {
      privateMovies();
    } else {
      notsupported();
    }
  }, [page, searchkey]);

  const privateMovies = async () => {
    try {
      setloading(true);
      const jsonBody = {
        key: searchkey,
      };
      const response = await fetch(
        `http://206.189.137.63:8010/privatemovies/${page}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(jsonBody),
        }
      );
      if (!response.ok) {
        setloading(false);
        settotal(0);
        setfinaldata([]);
        throw new Error("Network response was not ok");
      }
      const jsonData = await response.json();
      if (jsonData.result === 0) {
        setloading(false);
        settotal(jsonData.total);
        setfinaldata([]);
      } else {
        setloading(false);
        settotal(jsonData.total);
        setfinaldata(jsonData.data);
      }
    } catch (error) {
      setloading(false);
      settotal(0);
      setfinaldata([]);
      console.error("Error fetching data:", error);
    }
  };

  const copyrightMovies = async () => {
    try {
      setloading(true);
      const response = await fetch(
        "http://206.189.137.63:8010/copyrightmovies",
        {
          method: "GET",
        }
      );
      if (!response.ok) {
        setloading(false);
        setfinaldata([]);
        settotal(0);
        throw new Error("Network response was not ok");
      }
      const jsonData = await response.json();
      if (jsonData.result === 0) {
        setloading(false);
        settotal(jsonData.total);
        setfinaldata([]);
      } else {
        setloading(false);
        settotal(jsonData.total);
        setfinaldata(jsonData.data);
      }
    } catch (error) {
      setloading(false);
      settotal(0);
      setfinaldata([]);
      console.error("Error fetching data:", error);
    }
  };

  const AdminautoCheck = async () => {
    try {
      setloading(true);
      const response = await fetch(
        "http://206.189.137.63:8010/Admincheckembeded/1",
        {
          method: "POST",
          headers: {
            " Content-Type": "application/json",
            Auth: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwiaWF0IjoxNzE2NzgyNDk2fQ.jOZUk0DLDcmVrsxczNcF-BlQmC-UI9s2B5i5sKv7wTY",
          },
        }
      );
      if (!response.ok) {
        setloading(false);
        setfinaldata([]);
        throw new Error("Network response was not ok");
      }
      const jsonData = await response.json();
      if (jsonData.result === 0) {
        setloading(false);
        setfinaldata([]);
      } else {
        setloading(false);
        setfinaldata(jsonData.data);
      }
    } catch (error) {
      setloading(false);
      setfinaldata([]);
      console.error("Error fetching data:", error);
    }
  };

  const prev = () => {
    if (page > 1) {
      setPage(page - 1);
    }
  };

  const next = () => {
    //  if (posts?.posts?.lastpage != true) {
    setPage(page + 1);
    //  }
  };

  const pages = Math.ceil(total / 8);
  const nopages = [];
  for (let i = 1; i <= pages; i++) {
    nopages.push(i);
  }
  let currentpages = [];
  if (parseInt(page) > 10) {
    let s = (Math.ceil(parseFloat(page / 10)) - 1) * 10;
    currentpages = [...nopages.splice(s, 10)];
  } else {
    currentpages = [...nopages.splice(0, 10)];
  }

  const countrywiserestric = async () => {
    try {
      setloading(true);
      const jsonBody = {
        key: searchkey,
      };
      const response = await fetch(
        `http://206.189.137.63:8010/countrywiserestricmovies/${page}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Auth: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwiaWF0IjoxNzE2NzgyNDk2fQ.jOZUk0DLDcmVrsxczNcF-BlQmC-UI9s2B5i5sKv7wTY",
          },
          body: JSON.stringify(jsonBody),
        }
      );
      if (!response.ok) {
        setloading(false);
        setfinaldata([]);
        throw new Error("Network response was not ok");
      }
      const jsonData = await response.json();
      if (jsonData.result === 0) {
        setloading(false);
        settotal(jsonData.total);
        setfinaldata([]);
      } else {
        setloading(false);
        settotal(jsonData.total);
        setfinaldata(jsonData.data);
      }
    } catch (error) {
      setloading(false);
      setfinaldata([]);
      console.error("Error fetching data:", error);
    }
  };
  useEffect(() => {
    notsupported();
  }, []);

  const canclelogout = () => {
    setIsShow(false);
  };

  const logout = () => {
    setIsShow(false);
    AdminautoCheck();
  };

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox mb={0} />
      {isShow ? (
        <div>
          <div className="modalBackground">
            <div className="modalContainer">
              <div className="titleCloseBtn">
                <button onClick={() => canclelogout()}>X</button>
              </div>
              <div className="title">
                <h5>Are You Sure You Want to Show Data?</h5>
              </div>
              <div className="footer">
                <button id="cancelBtn" onClick={() => canclelogout()}>
                  No
                </button>
                <button onClick={() => logout()}>Yes</button>
              </div>
            </div>
          </div>
          <ToastContainer />
        </div>
      ) : (
        <Header>
          <MDBox
            pt={0}
            px={2}
            lineHeight={1.5}
            display={isMobile ? "block" : "flex"}
          >
            <MDTypography variant="h6" fontWeight="medium" mt={1}>
              Not Supported Posts - {total}
            </MDTypography>
            <Button
              variant="contained"
              style={{
                backgroundColor: "#1976d2",
                color: "#ffffff",
                textTransform: "capitalize",
                marginLeft: "10%",
                fontSize: "15px",
              }}
              onClick={() => {
                setCategory("notsupported");
                setPage(1);
                notsupported();
              }}
            >
              Not supported
            </Button>
            <Button
              variant="contained"
              style={{
                backgroundColor: "#4caf50",
                color: "#ffffff",
                textTransform: "capitalize",
                marginLeft: "10px",
                fontSize: "15px",
              }}
              onClick={() => {
                setCategory("private");
                setPage(1);
                privateMovies();
              }}
            >
              Channel
            </Button>
            <Button
              variant="contained"
              style={{
                backgroundColor: "#ff9800",
                color: "#ffffff",
                textTransform: "capitalize",
                marginLeft: "10px",
                fontSize: "15px",
              }}
              onClick={() => {
                copyrightMovies();
              }}
            >
              Copyright
            </Button>
            <Button
              variant="contained"
              style={{
                backgroundColor: "#f44336",
                color: "#ffffff",
                textTransform: "capitalize",
                marginLeft: "10px",
                fontSize: "15px",
              }}
              onClick={() => {
                setCategory("country");
                setPage(1);
                countrywiserestric();
              }}
            >
              Country
            </Button>
            <input
              style={{
                paddingLeft: "10px",
                marginLeft: "20PX",
                height: "40px",
                borderRadius: "10px",
                border: "1px solid black",
                width: isMobile ? "100%" : "250px",
              }}
              width=""
              onChange={(e) => {
                searchHanlde(e);
              }}
              placeholder="search post here..."
            />
            <Button
              variant="contained"
              style={{
                backgroundColor: "#1976d2",
                color: "#ffffff",
                textTransform: "capitalize",
                marginLeft: "12%",
                fontSize: "15px",
              }}
              onClick={() => {
                setIsShow(true);
              }}
            >
              Check not supported
            </Button>
          </MDBox>
          <MDBox
            width="100%"
            display={isMobile ? "block" : "flex"}
            flexDirection={isMobile ? "column" : "row"}
            align=""
            lineHeight={1.25}
          ></MDBox>
          <MDBox
            style={{
              overflow: "auto",
              alignItems: isMobile ? "" : "center",
              justifyContent: isMobile ? "" : "center",
              width: "100%",
              display: "flex",
              flexDirection: "column",
              marginTop: "20px",
            }}
          >
            <table
              style={{
                height: "15px",
                border: "1px solid #c7c7c7",
                overflowX: "auto",
              }}
            >
              <thead style={{ display: "block" }}>
                <tr style={{ display: "flex", width: "100%" }}>
                  <td
                    scope="col"
                    style={{
                      width: "40px",
                      textAlign: "center",
                      borderRight: "1px solid #b1b5b2",
                      cursor: "pointer",
                      color: "#646669",
                    }}
                    onClick={() => {
                      parseInt(page - 10) >= 1
                        ? setPage(Math.floor(parseInt(page) / 10) * 10 - 9)
                        : "";
                    }}
                  >
                    <KeyboardArrowLeftIcon style={{ marginRight: "-10px" }} />
                    <KeyboardArrowLeftIcon />
                  </td>
                  <td
                    scope="col"
                    style={{
                      width: "40px",
                      textAlign: "center",
                      cursor: "pointer",
                      color: "#646669",
                    }}
                    onClick={prev}
                  >
                    <KeyboardArrowLeftIcon />
                  </td>
                  {currentpages[0] > 1 ? (
                    <>
                      <td
                        style={{
                          width: "40px",
                          textAlign: "center",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          borderLeft: "1px solid #b1b5b2",
                          cursor: "pointer",
                          fontSize: "17px",
                          color: "#646669",
                        }}
                        onClick={() => {
                          setPage(1);
                        }}
                      >
                        1
                      </td>
                      <td
                        style={{
                          width: "40px",
                          textAlign: "center",
                          borderLeft: "1px solid #b1b5b2",
                          cursor: "pointer",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          fontSize: "17px",
                          color: "#646669",
                        }}
                      >
                        ...
                      </td>
                    </>
                  ) : (
                    ""
                  )}

                  {currentpages?.map((e) => {
                    return (
                      <td
                        style={{
                          borderLeft: "1px solid #b1b5b2",
                          width: "40px",
                          textAlign: "center",
                          cursor: "pointer",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          fontSize: "17px",
                          backgroundColor: `${
                            e == parseInt(page) ? "#3993EE" : ""
                          }`,
                        }}
                        onClick={() => {
                          setPage(e);
                        }}
                      >
                        {" "}
                        <span>
                          {" "}
                          <Link
                            to=""
                            style={{
                              position: "absolute",
                              color: `${
                                e == parseInt(page) ? "white" : "#646669"
                              }`,
                            }}
                            className={`p-1   position-relative  text-underline-hover`}
                          >
                            {e}
                          </Link>
                        </span>
                      </td>
                    );
                  })}

                  {currentpages[currentpages.length - 1] < pages ? (
                    <>
                      <td
                        style={{
                          width: "40px",
                          textAlign: "center",
                          borderLeft: "1px solid #b1b5b2",
                          cursor: "pointer",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          fontSize: "17px",
                          color: "#646669",
                        }}
                      >
                        ...
                      </td>
                      <td
                        style={{
                          width: "40px",
                          textAlign: "center",
                          borderLeft: "1px solid #b1b5b2",
                          cursor: "pointer",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          fontSize: "17px",
                          color: "#646669",
                        }}
                        onClick={() => {
                          setPage(pages);
                        }}
                      >
                        {pages}
                      </td>
                    </>
                  ) : (
                    ""
                  )}

                  <td
                    style={{
                      width: "40px",
                      textAlign: "center",
                      borderLeft: "1px solid #b1b5b2",
                      cursor: "pointer",
                      color: "#646669",
                    }}
                    onClick={next}
                  >
                    {" "}
                    <NavigateNextIcon />{" "}
                  </td>
                  <td
                    style={{
                      width: "40px",
                      textAlign: "center",
                      borderLeft: "1px solid #b1b5b2",
                      cursor: "pointer",
                      color: "#646669",
                    }}
                    onClick={() => {
                      parseInt(page) + 10 <= pages
                        ? setPage(Math.ceil(parseInt(page) / 10) * 10 + 1)
                        : "";
                    }}
                  >
                    {" "}
                    <NavigateNextIcon style={{ marginRight: "-10px" }} />
                    <NavigateNextIcon />
                  </td>
                </tr>
              </thead>
            </table>
          </MDBox>
          <MDBox p={2} mt={1}>
            <Grid container spacing={3}>
              {Loading ? (
                <MDBox align="center" width="100%" mt={10}>
                  <MDTypography
                    component="a"
                    href="#"
                    variant="button"
                    color="text"
                    fontWeight="medium"
                  >
                    Loading...
                  </MDTypography>
                </MDBox>
              ) : finaldata.length == 0 ? (
                <MDBox align="center" width="100%" mt={10}>
                  <MDTypography
                    component="a"
                    href="#"
                    variant="button"
                    color="text"
                    fontWeight="medium"
                  >
                    No Data Found{" "}
                  </MDTypography>
                </MDBox>
              ) : (
                finaldata?.map((e) => {
                  return (
                    <Grid
                      item
                      xs={12}
                      md={6}
                      xl={3}
                      mt={-2}
                      key={e.post_id}
                      overflow={isMobile ? "auto" : ""}
                    >
                      <DefaultProjectCard
                        post_id={e.post_id}
                        post={e.post != null ? e.post : e.thumbnail}
                        label={e.user_name}
                        title={e.title}
                        description={e.caption}
                        trailer={e.trailer}
                        poster={e.thumbnail}
                        movie={e.link}
                        genre={e.genre}
                        userid={e.user_id}
                        action={{
                          type: "internal",
                          route: `/posts/indpost/${e.post_id}`,
                          color: "info",
                          label: "view detail",
                        }}
                      />
                    </Grid>
                  );
                })
              )}
            </Grid>
          </MDBox>
        </Header>
      )}
      <Footer />
    </DashboardLayout>
  );
}

export default notsupported;
