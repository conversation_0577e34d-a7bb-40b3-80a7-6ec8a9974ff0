{"name": "material-dashboard-2-react", "version": "2.1.0", "private": true, "author": "Creative Tim", "license": "See license in https://www.creative-tim.com/license", "description": "React version of Material Dashboard 2 by <PERSON> Tim", "bugs": {"url": "https://github.com/creativetimofficial/material-dashboard-react/issues"}, "repository": {"type": "git", "url": "git+https://github.com/creativetimofficial/material-dashboard-react.git"}, "engines": {"node": ">=14", "npm": ">=6"}, "dependencies": {"@coreui/chartjs": "^3.0.0", "@coreui/coreui": "^4.2.1", "@coreui/icons": "^2.1.0", "@coreui/icons-react": "^2.1.0", "@coreui/react": "^4.3.1", "@coreui/react-chartjs": "^2.1.0", "@coreui/utils": "^1.3.1", "@emotion/cache": "11.7.1", "@emotion/react": "11.7.1", "@emotion/styled": "11.6.0", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@mui/core": "^5.0.0-alpha.54", "@mui/icons-material": "5.4.1", "@mui/material": "5.4.1", "@mui/styled-engine": "5.4.1", "@progress/kendo-react-dropdowns": "^5.9.0", "@reduxjs/toolkit": "^1.8.6", "@testing-library/jest-dom": "5.16.2", "@testing-library/react": "12.1.2", "@testing-library/user-event": "13.5.0", "axios": "^1.1.2", "bootstrap": "^5.3.2", "caniuse-lite": "^1.0.30001566", "chart.js": "3.4.1", "chartjs-plugin-datalabels": "^2.2.0", "chroma-js": "2.4.2", "dotenv": "^16.0.3", "fs": "^0.0.1-security", "loadsh": "^0.0.4", "material-ui-player": "^1.0.10", "material-ui-search-bar": "^1.0.0", "mdb-react-ui-kit": "^5.0.0", "moment": "^2.29.4", "pm2": "^5.3.0", "prop-types": "15.8.1", "react": "17.0.2", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "3.0.4", "react-cookie": "^4.1.1", "react-dom": "17.0.2", "react-dotenv": "^0.1.3", "react-drag-and-drop": "^3.0.0", "react-dropdown-select": "^4.9.0", "react-edit-text": "^5.0.2", "react-editext": "^5.1.0", "react-file-input": "^0.2.5", "react-github-btn": "1.2.1", "react-modal": "^3.16.1", "react-redux": "^8.0.4", "react-router-dom": "6.2.1", "react-scripts": "^5.0.1", "react-select": "^5.6.1", "react-table": "7.7.0", "react-toastify": "^9.1.1", "reactjs-popup": "^2.0.5", "redux": "^4.2.0", "redux-devtools-extension": "^2.13.9", "redux-thunk": "^2.4.1", "search-bar-react": "^1.1.0", "stylis": "4.0.13", "stylis-plugin-rtl": "2.1.1", "universal-cookie": "^4.0.4", "web-vitals": "2.1.4", "yup": "0.32.11"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "install:clean": "rm -rf node_modules/ && rm -rf package-lock.json && npm install && npm start"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"assert": "^2.0.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.0", "dotenv-webpack": "^8.0.1", "eslint": "^8.0.0", "eslint-config-airbnb": "19.0.4", "eslint-config-prettier": "8.3.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-jsx-a11y": "6.5.1", "eslint-plugin-prettier": "4.0.0", "eslint-plugin-react": "7.28.0", "eslint-plugin-react-hooks": "4.3.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "prettier": "2.5.1", "process": "^0.11.10", "react-app-rewired": "^2.2.1", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "url": "^0.11.0", "webpack": "^5.74.0", "webpack-cli": "^4.10.0"}}