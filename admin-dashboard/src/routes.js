/* eslint-disable */
/** 
  All of the routes for the admin dashboard are added here,
  You can add a new route, customize the routes and delete the routes here.

  Once you add a new route on this file it will be visible automatically on
  the Sidenav.

  For adding a new route you can follow the existing routes in the routes array.
  1. The `type` key with the `collapse` value is used for a route.
  2. The `type` key with the `title` value is used for a title inside the Sidenav. 
  3. The `type` key with the `divider` value is used for a divider between Sidenav items.
  4. The `name` key is used for the name of the route on the Sidenav.
  5. The `key` key is used for the key of the route (It will help you with the key prop inside a loop).
  6. The `icon` key is used for the icon of the route on the Sidenav, you have to add a node.
  7. The `collapse` key is used for making a collapsible item on the Sidenav that has other routes
  inside (nested routes), you need to pass the nested routes inside an array as a value for the `collapse` key.
  8. The `route` key is used to store the route location which is used for the react router.
  9. The `href` key is used to store the external links location.
  10. The `title` key is only for the item with the type of `title` and its used for the title text on the Sidenav.
  10. The `component` key is used to store the component of its route.
*/

// Material Dashboard 2 React layouts
import Dashboard from "layouts/dashboard";
import User from "layouts/user";
import AddPost from "layouts/addpost";
import Reportedpost from "layouts/reports";
import Reporteduser from "layouts/reported_user";
import Post from "layouts/Posts";
import Indpost from "./layouts/individualpost";
import Induser from "./layouts/individualuser";
import Crawler from "./layouts/crawler";
import NotSupport from "./layouts/notsupported";
import TeraBox from "./layouts/terabox";
import BlockedUser from "./layouts/blockuser";
import UnverifiedPost from "./layouts/unverifiedpost";
// @mui icons
import Icon from "@mui/material/Icon";
import Login from "./layouts/authentication/sign-in";
import Logout from "./layouts/authentication/logout/index";
import Gen from "./layouts/edit_genre";
import Episodes from "./layouts/editEpisodes";
import MxCrawl from "layouts/mxcrawl";
import SearchHistory from "layouts/searchHistory";
// import {GiHalfBodyCrawling} from "react"
const auth = sessionStorage.getItem("auth");
const routes = [
  {
    type: "collapse",
    name: "Dashboard",
    key: "dashboard",
    icon: <Icon fontSize="small">dashboard</Icon>,
    route: "/dashboard",
    component: <Dashboard />,
  },
  {
    key: "inividual post",
    icon: <Icon fontSize="small">post</Icon>,
    route: "posts/indpost/:id",
    component: <Indpost />,
  },
  {
    key: "inividual episdodes",
    icon: <Icon fontSize="small">post</Icon>,
    route: "posts/episodes/:id",
    component: <Episodes />,
  },
  {
    key: "individual user",
    icon: <Icon fontSize="small">user</Icon>,
    route: "/users/induser/:id",
    component: <Induser />,
  },

  {
    type: "collapse",
    name: "Users",
    key: "users",
    icon: <Icon fontSize="small">person</Icon>,
    route: "/users",
    component: <User />,
  },
  {
    type: "collapse",
    name: "Reported Post",
    key: "reportedPost",
    icon: <Icon fontSize="small">receipt_long</Icon>,
    route: "/reportedPost",
    component: <Reportedpost />,
  },
  {
    type: "collapse",
    name: "Reported user",
    key: "reportedUser",
    icon: <Icon fontSize="small">receipt_long</Icon>,
    route: "/reportedUser",
    component: <Reporteduser />,
  },
  {
    type: "collapse",
    name: "Blocked User",
    key: "blockedusers",
    icon: <Icon fontSize="small">person</Icon>,
    route: "/blockedusers",
    component: <BlockedUser />,
  },
  {
    type: "collapse",
    name: "Posts",
    key: "posts",
    icon: <Icon fontSize="small">table_view</Icon>,
    route: "/posts",
    component: <Post />,
  },
  {
    type: "collapse",
    name: "Crawl",
    key: "crawls",
    icon: <Icon fontSize="small">table_view</Icon>,
    route: "/crawls",
    component: <Crawler />,
  },
  {
    type: "collapse",
    name: "MX crawl",
    key: "mxcrawl",
    icon: <Icon fontSize="small">table_view</Icon>,
    route: "/mxcrawl",
    component: <MxCrawl />,
  },
  {
    type: "collapse",
    name: "Unverified Post",
    key: "unverifiedpost",
    icon: <Icon fontSize="small">table_view</Icon>,
    route: "/unverifiedpost",
    component: <UnverifiedPost />,
  },
  {
    type: "collapse",
    name: "Not Supported",
    key: "notsupported",
    icon: <Icon fontSize="small">table_view</Icon>,
    route: "/notsupported",
    component: <NotSupport />,
  },
  {
    type: "collapse",
    name: "Terabox",
    key: "terabox",
    icon: <Icon fontSize="small">table_view</Icon>,
    route: "/terabox",
    component: <TeraBox />,
  },
  {
    type: "collapse",
    name: "Add Post",
    key: "addpost",
    icon: <Icon fontSize="small">control_point</Icon>,
    route: "/addpost",
    component: <AddPost />,
  },
  {
    type: "collapse",
    name: "Set Genre",
    key: "setgenre",
    icon: <Icon fontSize="small">control_point</Icon>,
    route: "/setgenre",
    component: <Gen />,
  },
  {
    type: "collapse",
    name: "History",
    key: "history",
    icon: <Icon fontSize="small">history</Icon>,
    route: "/history",
    component: <SearchHistory />,
  },
  {
    type: "collapse",
    name: "logout",
    key: "logout",
    icon: <Icon fontSize="small">logout</Icon>,
    route: "/logout",
    component: <Logout />,
  },
  {
    key: "login",
    name: "login",
    route: "/login",
    component: <Login />,
  },
];

export default routes;
