import 'package:my_video/app_imports.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

class OfflineManager extends GetxController {
  static OfflineManager get instance => Get.find<OfflineManager>();

  final Logger _logger = Logger();

  bool _isOnline = true;
  bool get isOnline => _isOnline;

  bool _hasShownOfflineMessage = false;

  @override
  void onInit() {
    super.onInit();
    _initializeConnectivity();
    _listenToConnectivityChanges();
  }

  void _initializeConnectivity() async {
    try {
      _isOnline = await ConnectivityHelper.hasInternetConnection();
      update();
      _logger.i(
          'Initial connectivity status: ${_isOnline ? 'Online' : 'Offline'}');
    } catch (e) {
      _logger.e('Error checking initial connectivity: $e');
    }
  }

  void _listenToConnectivityChanges() {
    ConnectivityHelper.connectionStream.listen((status) {
      final wasOnline = _isOnline;
      _isOnline = status == InternetConnectionStatus.connected;
      update();

      if (wasOnline && !_isOnline) {
        _showOfflineMessage();
      } else if (!wasOnline && _isOnline) {
        _showOnlineMessage();
        _syncOfflineData();
      }

      _logger.i('Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');
    });
  }

  void _showOfflineMessage() {
    if (_hasShownOfflineMessage) return;

    _hasShownOfflineMessage = true;
    Get.snackbar(
      'Offline Mode',
      'You\'re now offline. Some features may be limited.',
      snackPosition: SnackPosition.TOP,
      backgroundColor: AppColorConstants.colorOrange,
      colorText: AppColorConstants.textPrimary,
      duration: const Duration(seconds: 4),
      margin: EdgeInsets.all(MySize.width(16)),
      borderRadius: MySize.radius(8),
      icon: Icon(
        Icons.wifi_off,
        color: AppColorConstants.textPrimary,
        size: MySize.height(24),
      ),
    );
  }

  void _showOnlineMessage() {
    _hasShownOfflineMessage = false;
    Get.snackbar(
      'Back Online',
      'Connection restored. Syncing data...',
      snackPosition: SnackPosition.TOP,
      backgroundColor: AppColorConstants.colorGreen,
      colorText: AppColorConstants.textPrimary,
      duration: const Duration(seconds: 3),
      margin: EdgeInsets.all(MySize.width(16)),
      borderRadius: MySize.radius(8),
      icon: Icon(
        Icons.wifi,
        color: AppColorConstants.textPrimary,
        size: MySize.height(24),
      ),
    );
  }

  Future<void> _syncOfflineData() async {
    try {
      // Sync any pending offline actions
      await _syncPlaylistChanges();
      await _syncUserSettings();
      _logger.i('Offline data synced successfully');
    } catch (e) {
      _logger.e('Error syncing offline data: $e');
    }
  }

  Future<void> _syncPlaylistChanges() async {
    try {
      // Get pending playlist changes from local storage
      final pendingChanges = HiveHelper.getSetting<List<dynamic>>(
          'pending_playlist_changes',
          defaultValue: []);

      if (pendingChanges != null && pendingChanges.isNotEmpty) {
        // TODO: Sync playlist changes with server
        // For now, just clear the pending changes
        await HiveHelper.deleteSetting('pending_playlist_changes');
        _logger.i('Synced ${pendingChanges.length} playlist changes');
      }
    } catch (e) {
      _logger.e('Error syncing playlist changes: $e');
    }
  }

  Future<void> _syncUserSettings() async {
    try {
      // Get pending settings changes from local storage
      final pendingSettings = HiveHelper.getSetting<Map<String, dynamic>>(
          'pending_settings_changes',
          defaultValue: {});

      if (pendingSettings != null && pendingSettings.isNotEmpty) {
        // TODO: Sync settings with server
        // For now, just clear the pending changes
        await HiveHelper.deleteSetting('pending_settings_changes');
        _logger.i('Synced ${pendingSettings.length} setting changes');
      }
    } catch (e) {
      _logger.e('Error syncing user settings: $e');
    }
  }

  // Queue actions for when back online
  Future<void> queuePlaylistChange(
      String action, Map<String, dynamic> data) async {
    try {
      final pendingChanges = HiveHelper.getSetting<List<dynamic>>(
              'pending_playlist_changes',
              defaultValue: []) ??
          [];

      pendingChanges.add({
        'action': action,
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
      });

      await HiveHelper.saveSetting('pending_playlist_changes', pendingChanges);
      _logger.i('Queued playlist change: $action');
    } catch (e) {
      _logger.e('Error queueing playlist change: $e');
    }
  }

  Future<void> queueSettingChange(String key, dynamic value) async {
    try {
      final pendingSettings = HiveHelper.getSetting<Map<String, dynamic>>(
              'pending_settings_changes',
              defaultValue: {}) ??
          {};

      pendingSettings[key] = {
        'value': value,
        'timestamp': DateTime.now().toIso8601String(),
      };

      await HiveHelper.saveSetting('pending_settings_changes', pendingSettings);
      _logger.i('Queued setting change: $key');
    } catch (e) {
      _logger.e('Error queueing setting change: $e');
    }
  }

  // Get cached data when offline
  List<MovieModel> getCachedMovies() {
    try {
      return HiveHelper.getAllMovies();
    } catch (e) {
      _logger.e('Error getting cached movies: $e');
      return [];
    }
  }

  List<CategoryModel> getCachedCategories() {
    try {
      return HiveHelper.getAllCategories();
    } catch (e) {
      _logger.e('Error getting cached categories: $e');
      return [];
    }
  }

  List<MovieModel> getCachedFeaturedMovies() {
    try {
      return HiveHelper.getFeaturedMovies();
    } catch (e) {
      _logger.e('Error getting cached featured movies: $e');
      return [];
    }
  }

  List<MovieModel> getCachedMoviesByCategory(String category) {
    try {
      return HiveHelper.getMoviesByCategory(category);
    } catch (e) {
      _logger.e('Error getting cached movies by category: $e');
      return [];
    }
  }

  // Check if we have sufficient cached data
  bool hasCachedData() {
    try {
      final movies = HiveHelper.getAllMovies();
      final categories = HiveHelper.getAllCategories();
      return movies.isNotEmpty && categories.isNotEmpty;
    } catch (e) {
      _logger.e('Error checking cached data: $e');
      return false;
    }
  }

  // Show offline indicator widget
  Widget buildOfflineIndicator() {
    if (_isOnline) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: MySize.width(16),
        vertical: MySize.height(8),
      ),
      color: AppColorConstants.colorOrange,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.wifi_off,
            color: AppColorConstants.textPrimary,
            size: MySize.height(16),
          ),
          Space.width(8),
          AppText(
            text: 'Offline Mode - Limited functionality',
            fontSize: MySize.fontSize(12),
            color: AppColorConstants.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ],
      ),
    );
  }

  @override
  void onClose() {
    super.onClose();
  }
}
