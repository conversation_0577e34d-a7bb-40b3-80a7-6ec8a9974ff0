/* eslint-disable */
// prop-types is a library for typechecking of props
import PropTypes from "prop-types";
// Material Dashboard 2 React context
import { useMaterialUIController } from "context";
import PlayCircleOutline from "@mui/icons-material/PlayCircleOutline";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { useCookies } from "react-cookie";
import "../View/index.js";
<>
  <link
    href="https://fonts.googleapis.com/icon?family=Material+Icons"
    rel="stylesheet"
  />
  <link
    href="https://fonts.googleapis.com/css?family=Roboto:300,400,500"
    rel="stylesheet"
  />
</>;
function Single_report({
  id,
  title,
  views,
  movie,
  caption,
  pid,
  post,
  reason,
  date,
  report_count,
  source,
  dest,
  noGutter,
  select,
  selectall,
  selectedData,
  poster,
}) {
  const [controller] = useMaterialUIController();
  const [data, setdata] = useState([]);
  const { darkMode } = controller;
  const navigate = useNavigate();
  const [cookie, setcookie] = useCookies();
  const token = cookie.admin;
  const delreport = async () => {
    let form = new FormData();
    form.append("id", id);
    form.append("post", pid);

    const del = await fetch(`${window.path}/admindeletereport`, {
      method: "DELETE",
      headers: { auth: token },
      body: form,
    });
    const res = await del.json();
    if (res.status === 1) {
      alert("deleted successfully");
      window.location.reload(false);
    } else {
      alert("cant delete");
    }
  };

  return (
    <>
      <div className="row">
        <div className="col-md-4 col-sm-12 col-lg-4">
          <div className="card card__dark card__dark--magenta bg-white">
            {select === true ? (
              <div className="d-flex justify-content-center p-2 ">
                <input
                  className=""
                  type="checkbox"
                  value={id}
                  onChange={selectedData}
                />
              </div>
            ) : (
              ""
            )}

            <div
              className="media media--16-9"
              onClick={() => {
                window.open(post, "blank");
              }}
            >
              <button className=" play-button">
                <PlayCircleOutline />
              </button>
              {post.includes("http") ? (
                <video
                  width="300"
                  poster={poster}
                  style={{ borderRadius: "10px" }}
                >
                  <source src={post} type="video/mp4"></source>
                </video>
              ) : (
                <img
                  src={post}
                  alt=""
                  width="640"
                  height="500"
                  style={{ borderRadius: "10px" }}
                />
              )}
            </div>
            <div className="primary-title">
              <div className="primary-text">
                <b>{title.length > 26 ? `${title.slice(0, 26)}...` : title}</b>
              </div>
              <div className="pt-0">
                <div className="supporting-text">
                  caption&nbsp;:&nbsp;&nbsp;
                  {caption.length > 18 ? caption.slice(0, 18) + "..." : caption}
                </div>
                <div className="supporting-text">
                  views&nbsp;:&nbsp;&nbsp;{views}
                </div>
                <div className="supporting-text">
                  total reports&nbsp;:&nbsp;&nbsp;{report_count}
                </div>
                <div className="supporting-text">
                  reason&nbsp;:&nbsp;&nbsp;
                  {reason?.length > 25 ? `${reason.slice(0, 25)}...` : reason}
                </div>
                <div className="supporting-text">
                  upload date&nbsp;:&nbsp;&nbsp;{date}
                </div>
              </div>
            </div>
            <div className="actions border-top my-1">
              <div className=" text-center">
                <button
                  className="btn btn-danger w-100"
                  width="100%"
                  type="button"
                  onClick={delreport}
                >
                  DELETE
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// Setting default values for the props of Bill
Single_report.defaultProps = {
  noGutter: false,
};

// Typechecking props for the Bill
Single_report.propTypes = {
  name: PropTypes.string.isRequired,
  dest: PropTypes.string.isRequired,
  source: PropTypes.string.isRequired,
  post: PropTypes.string,
  source: PropTypes.string.isRequired,
  date: PropTypes.string.isRequired,
};

export default Single_report;
