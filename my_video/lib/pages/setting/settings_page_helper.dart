import 'package:my_video/app_imports.dart';

class SettingsPageHelper {
  final SettingsPageState _state;
  final Logger _logger = Logger();

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  String _userName = 'Movie Lover';
  String get userName => _userName;

  String _userEmail = '<EMAIL>';
  String get userEmail => _userEmail;

  final RxBool _notificationsEnabled = true.obs;
  bool get notificationsEnabled => _notificationsEnabled.value;

  final RxBool _autoPlayEnabled = true.obs;
  bool get autoPlayEnabled => _autoPlayEnabled.value;

  final RxBool _downloadOnWifiOnly = true.obs;
  bool get downloadOnWifiOnly => _downloadOnWifiOnly.value;

  final RxString _videoQuality = 'Auto'.obs;
  String get videoQuality => _videoQuality.value;

  List<String> _videoQualityOptions = ['Auto', 'HD', 'SD', 'Low'];
  List<String> get videoQualityOptions => _videoQualityOptions;

  String _appVersion = '1.0.0';
  String get appVersion => _appVersion;

  ApiStatus apiStatus = ApiStatus.initial;

  SettingsPageHelper(this._state) {
    Future.delayed(const Duration(milliseconds: 10), () => _loadSettings());
  }

  void _loadSettings() {
    try {
      apiStatus = ApiStatus.loading;
      _state.settingsController.update();

      _userName =
          HiveHelper.getSetting<String>(
            'user_name',
            defaultValue: 'Movie Lover',
          ) ??
          'Movie Lover';
      _userEmail =
          HiveHelper.getSetting<String>(
            'user_email',
            defaultValue: '<EMAIL>',
          ) ??
          '<EMAIL>';

      _notificationsEnabled.value =
          HiveHelper.getSetting<bool>(
            'notifications_enabled',
            defaultValue: true,
          ) ??
          true;
      _autoPlayEnabled.value =
          HiveHelper.getSetting<bool>(
            'auto_play_enabled',
            defaultValue: true,
          ) ??
          true;
      _downloadOnWifiOnly.value =
          HiveHelper.getSetting<bool>(
            'download_wifi_only',
            defaultValue: true,
          ) ??
          true;
      _videoQuality.value =
          HiveHelper.getSetting<String>(
            'video_quality',
            defaultValue: 'Auto',
          ) ??
          'Auto';

      _logger.i('Settings loaded successfully');
      apiStatus = ApiStatus.success;
      _state.settingsController.update();
    } catch (e) {
      _logger.e('Error loading settings: $e');
      apiStatus = ApiStatus.error;
      _state.settingsController.update();
    }
  }

  Future<void> updateNotifications(bool enabled) async {
    try {
      _notificationsEnabled.value = enabled;
      await HiveHelper.saveSetting('notifications_enabled', enabled);
      _state.settingsController.update();
      _logger.i('Notifications setting updated: $enabled');
    } catch (e) {
      _logger.e('Error updating notifications setting: $e');
      _showError('Failed to update notifications setting');
    }
  }

  Future<void> updateAutoPlay(bool enabled) async {
    try {
      _autoPlayEnabled.value = enabled;
      await HiveHelper.saveSetting('auto_play_enabled', enabled);
      _state.settingsController.update();
      _logger.i('Auto-play setting updated: $enabled');
    } catch (e) {
      _logger.e('Error updating auto-play setting: $e');
      _showError('Failed to update auto-play setting');
    }
  }

  Future<void> updateDownloadWifiOnly(bool enabled) async {
    try {
      _downloadOnWifiOnly.value = enabled;
      await HiveHelper.saveSetting('download_wifi_only', enabled);
      _state.settingsController.update();
      _logger.i('Download WiFi-only setting updated: $enabled');
    } catch (e) {
      _logger.e('Error updating download WiFi-only setting: $e');
      _showError('Failed to update download setting');
    }
  }

  Future<void> updateVideoQuality(String quality) async {
    try {
      _videoQuality.value = quality;
      await HiveHelper.saveSetting('video_quality', quality);
      _state.settingsController.update();
      _logger.i('Video quality setting updated: $quality');
    } catch (e) {
      _logger.e('Error updating video quality setting: $e');
      _showError('Failed to update video quality setting');
    }
  }

  void openTermsOfService() {
    try {
      gotoTermsOfServicePage();
    } catch (e) {
      _logger.e('Error opening Terms of Service: $e');
    }
  }

  void openPrivacyPolicy() {
    try {
      gotoPrivacyPolicyPage();
    } catch (e) {
      _logger.e('Error opening Privacy Policy: $e');
    }
  }

  void contactSupport() {
    try {
      gotoContactSupportPage();
    } catch (e) {
      _logger.e('Error contacting support: $e');
    }
  }

  void rateApp() {
    try {
      // For iOS
      if (Platform.isIOS) {
        launchUrl(
          Uri.parse('https://apps.apple.com/app/id123456789'),
          mode: LaunchMode.externalApplication,
        );
      }
      // For Android
      else if (Platform.isAndroid) {
        launchUrl(
          Uri.parse(
            'https://play.google.com/store/apps/details?id=com.example.my_video',
          ),
          mode: LaunchMode.externalApplication,
        );
      }
      // Fallback
      else {
        _showInfo('Please rate us on your device\'s app store');
      }
    } catch (e) {
      _logger.e('Error opening app store: $e');
      _showError('Could not open app store');
    }
  }

  void shareApp() {
    try {
      const shareText = '''
Check out MyVideo - the best movie streaming app!

Watch unlimited movies with high-quality streaming.
Download now and enjoy premium content!

#MyVideo #MovieStreaming #Entertainment
''';

      Share.share(shareText);
      _logger.i('App shared successfully');
    } catch (e) {
      _logger.e('Error sharing app: $e');
      _showError('Failed to share app');
    }
  }

  Future<void> clearCache() async {
    try {
      // Show confirmation dialog
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const AppText(
            text: 'Clear Cache',
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColorConstants.textPrimary,
          ),
          content: const AppText(
            text:
                'This will clear all cached data including downloaded thumbnails. Are you sure?',
            fontSize: 14,
            color: AppColorConstants.textSecondary,
          ),
          backgroundColor: AppColorConstants.surfaceColor,
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const AppText(
                text: 'Cancel',
                color: AppColorConstants.textSecondary,
              ),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const AppText(
                text: 'Clear',
                color: AppColorConstants.colorRed,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // TODO: Implement actual cache clearing
        await Future.delayed(const Duration(seconds: 1)); // Simulate clearing

        _showSuccess('Cache cleared successfully');
        _logger.i('Cache cleared');
      }
    } catch (e) {
      _logger.e('Error clearing cache: $e');
      _showError('Failed to clear cache');
    }
  }

  Future<void> logout() async {
    try {
      // Show confirmation dialog
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const AppText(
            text: 'Logout',
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColorConstants.textPrimary,
          ),
          content: const AppText(
            text:
                'Are you sure you want to logout? You will need to login again to access your account.',
            fontSize: 14,
            color: AppColorConstants.textSecondary,
          ),
          backgroundColor: AppColorConstants.surfaceColor,
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const AppText(
                text: 'Cancel',
                color: AppColorConstants.textSecondary,
              ),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const AppText(
                text: 'Logout',
                color: AppColorConstants.colorRed,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // Clear user session data
        await HiveHelper.deleteSetting('user_name');
        await HiveHelper.deleteSetting('user_email');
        await HiveHelper.deleteSetting('is_premium_user');
        await HiveHelper.deleteSetting('current_plan_id');

        // Navigate to login page
        Get.offAllNamed(AppRoutes.login);

        _logger.i('User logged out');
      }
    } catch (e) {
      _logger.e('Error during logout: $e');
      _showError('Failed to logout');
    }
  }

  Map<String, int> getDatabaseInfo() {
    return HiveHelper.getDatabaseInfo();
  }

  void _showSuccess(String message) {
    Get.snackbar(
      'Success',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.colorGreen,
      colorText: AppColorConstants.textPrimary,
      duration: const Duration(seconds: 2),
    );
  }

  void _showError(String message) {
    Get.snackbar(
      'Error',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.colorRed,
      colorText: AppColorConstants.textPrimary,
      duration: const Duration(seconds: 3),
    );
  }

  void _showInfo(String message) {
    Get.snackbar(
      'Info',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.primaryColor,
      colorText: AppColorConstants.textPrimary,
      duration: const Duration(seconds: 3),
    );
  }
}
