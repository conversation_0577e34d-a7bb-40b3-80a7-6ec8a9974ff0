import 'package:my_video/app_imports.dart';

class HomePageHelper {
  final HomePageState _state;
  final Logger _logger = Logger();

  // Loading state
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // Data variables
  List<MovieModel> _featuredMovies = [];
  List<CategoryModel> _categories = [];
  Map<String, List<MovieModel>> _moviesByCategory = {};

  // Getters
  List<MovieModel> get featuredMovies => _featuredMovies;
  List<CategoryModel> get categories => _categories;

  // API Status
  ApiStatus apiStatus = ApiStatus.initial;

  HomePageHelper(this._state) {
    Future.delayed(const Duration(milliseconds: 10), () => loadInitialData());
  }

  Future<void> loadInitialData() async {
    _setLoading(true);
    apiStatus = ApiStatus.loading;
    _state.homeController.update();

    try {
      // Check if offline and has cached data
      final offlineManager = OfflineManager.instance;
      if (!offlineManager.isOnline && offlineManager.hasCachedData()) {
        await _loadCachedData();
        return;
      }

      await Future.wait([loadFeaturedMovies(), loadCategories()]);
      await loadMoviesForCategories();

      apiStatus = ApiStatus.success;
    } catch (e) {
      _logger.e('Error loading initial data: $e');
      ErrorHandler.handleError(e, context: 'Home Data Loading');
      apiStatus = ApiStatus.error;

      // Try to load cached data as fallback
      await _loadCachedData();
    } finally {
      _setLoading(false);
      _state.homeController.update();
    }
  }

  Future<void> _loadCachedData() async {
    try {
      final offlineManager = OfflineManager.instance;
      _featuredMovies = offlineManager.getCachedFeaturedMovies();
      _categories = offlineManager.getCachedCategories();

      for (final category in _categories) {
        _moviesByCategory[category.name] = offlineManager
            .getCachedMoviesByCategory(category.name);
      }

      _logger.i(
        'Loaded cached data: ${_featuredMovies.length} featured movies, ${_categories.length} categories',
      );
      apiStatus = ApiStatus.success;
    } catch (e) {
      _logger.e('Error loading cached data: $e');
      apiStatus = ApiStatus.error;
    }
  }

  Future<void> loadFeaturedMovies() async {
    // Load cached data first for immediate display
    _featuredMovies = HiveHelper.getFeaturedMovies();
    if (_featuredMovies.isNotEmpty) {
      _logger.i('Loaded ${_featuredMovies.length} cached featured movies');
      _state.homeController.update(); // Update UI immediately with cached data
    }

    // Then load from API in background
    _loadFeaturedMoviesFromAPI();
  }

  Future<void> _loadFeaturedMoviesFromAPI() async {
    try {
      final response = await _state.homeController.getFeaturedMoviesFromAPI();
      if (response.success && response.data.isNotEmpty) {
        _featuredMovies = response.data;
        _logger.i(
          'Updated with ${_featuredMovies.length} featured movies from API',
        );
        _state.homeController.update(); // Update UI with fresh API data
      }
    } catch (e) {
      _logger.w('API call failed, using cached data: $e');
      // Don't show error since we already have cached data
    }
  }

  Future<void> loadCategories() async {
    // Load cached data first for immediate display
    _categories = HiveHelper.getAllCategories();
    if (_categories.isNotEmpty) {
      _logger.i('Loaded ${_categories.length} cached categories');
      _state.homeController.update(); // Update UI immediately with cached data
    }

    // Then load from API in background
    _loadCategoriesFromAPI();
  }

  Future<void> _loadCategoriesFromAPI() async {
    try {
      final response = await _state.homeController.getCategoriesFromAPI();
      if (response.success && response.data.isNotEmpty) {
        _categories = response.data;
        _logger.i('Updated with ${_categories.length} categories from API');
        _state.homeController.update(); // Update UI with fresh API data
      }
    } catch (e) {
      _logger.w('API call failed, using cached data: $e');
      // Don't show error since we already have cached data
    }
  }

  Future<void> loadMoviesForCategories() async {
    // Load cached data first for immediate display
    for (final category in _categories) {
      final cachedMovies = HiveHelper.getMoviesByCategory(category.name);
      if (cachedMovies.isNotEmpty) {
        _moviesByCategory[category.name] = cachedMovies;
        _logger.i(
          'Loaded ${cachedMovies.length} cached movies for ${category.name}',
        );
      }
    }
    if (_moviesByCategory.isNotEmpty) {
      _state.homeController.update(); // Update UI immediately with cached data
    }

    // Then load from API in background
    // _loadMoviesForCategoriesFromAPI();
  }

  // Future<void> _loadMoviesForCategoriesFromAPI() async {
  //   for (final category in _categories) {
  //     try {
  //       final response = await _state.homeController.getMoviesByCategoryFromAPI(
  //         category.name,
  //         perPage: 10,
  //       );
  //       if (response.success && response.data.isNotEmpty) {
  //         _moviesByCategory[category.name] = response.data;
  //         _logger.i(
  //           'Updated ${response.data.length} movies for ${category.name} from API',
  //         );
  //         _state.homeController.update(); // Update UI with fresh API data
  //       }
  //     } catch (e) {
  //       _logger.w(
  //         'API call failed for ${category.name}, using cached data: $e',
  //       );
  //       // Don't show error since we already have cached data
  //     }
  //   }
  // }

  List<MovieModel> getMoviesByCategory(String categoryName) {
    return _moviesByCategory[categoryName] ?? [];
  }

  Future<void> refreshData() async {
    await loadInitialData();
  }

  void playMovie(MovieModel movie, BuildContext context) {
    _logger.i('Playing movie: ${movie.title}');

    AdsManager.showInterstitialAd(
      onAdClosed: () {
        context.go(AppRoutes.moviePlayer, extra: movie);
      },
    );
  }

  void viewAllMovies(CategoryModel category) {
    _logger.i('View all movies for category: ${category.name}');
    AppHelper.showToast('View all ${category.name} movies');
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    _state.homeController.update();
  }
}
