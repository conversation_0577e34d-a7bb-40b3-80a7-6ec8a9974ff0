<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Movies</title>
    <style>
      .info-popup {
        position: absolute;
        background: #fff;
        border: 1px solid #ddd;
        padding: 10px 12px;
        border-radius: 8px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        max-width: 250px;
        font-size: 14px;
        z-index: 9999;
        display: none;
      }

      .info-btn {
        background: none;
        border: none;
        border-radius: 100%;
        color: #007bff;
        font-size: 16px;
        cursor: pointer;
        margin-left: 5px;
        margin-top: 10px;
        margin-right: 10px;
      }

      .card {
        border: 1px solid #ccc;
        border-radius: 8px;
        margin-top: 20px;
        margin-left: 60px;
        width: calc(20% - 20px);
        height: 410px;
        float: left;
        box-sizing: border-box;
        overflow: hidden;
        box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.2),
          0 4px 15px 0 rgba(0, 0, 0, 0.19);
      }

      .card img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 8px;
        padding: -10px;
      }

      .card h3,
      .card p {
        margin: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-top: 8px;
        margin-left: 10px;
      }

      #loader {
        display: none;
        border: 16px solid #f3f3f3;
        border-radius: 50%;
        border-top: 16px solid #3498db;
        width: 120px;
        height: 120px;
        -webkit-animation: spin 2s linear infinite;
        animation: spin 2s linear infinite;
        position: absolute;
        top: 50%;
        left: 50%;
        margin-left: -60px;
        margin-top: -60px;
      }

      @-webkit-keyframes spin {
        0% {
          -webkit-transform: rotate(0deg);
        }
        100% {
          -webkit-transform: rotate(360deg);
        }
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .lds-roller,
      .lds-roller div,
      .lds-roller div:after {
        box-sizing: border-box;
      }
      .lds-roller {
        display: inline-block;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 80px;
        height: 80px;
      }

      .lds-roller div {
        animation: lds-roller 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
        transform-origin: 40px 40px;
      }
      .lds-roller div:after {
        content: " ";
        display: block;
        position: absolute;
        width: 7.2px;
        height: 7.2px;
        border-radius: 50%;
        background: currentColor;
        margin: -3.6px 0 0 -3.6px;
      }
      .lds-roller div:nth-child(1) {
        animation-delay: -0.036s;
      }
      .lds-roller div:nth-child(1):after {
        top: 62.62742px;
        left: 62.62742px;
      }
      .lds-roller div:nth-child(2) {
        animation-delay: -0.072s;
      }
      .lds-roller div:nth-child(2):after {
        top: 67.71281px;
        left: 56px;
      }
      .lds-roller div:nth-child(3) {
        animation-delay: -0.108s;
      }
      .lds-roller div:nth-child(3):after {
        top: 70.90963px;
        left: 48.28221px;
      }
      .lds-roller div:nth-child(4) {
        animation-delay: -0.144s;
      }
      .lds-roller div:nth-child(4):after {
        top: 72px;
        left: 40px;
      }
      .lds-roller div:nth-child(5) {
        animation-delay: -0.18s;
      }
      .lds-roller div:nth-child(5):after {
        top: 70.90963px;
        left: 31.71779px;
      }
      .lds-roller div:nth-child(6) {
        animation-delay: -0.216s;
      }
      .lds-roller div:nth-child(6):after {
        top: 67.71281px;
        left: 24px;
      }
      .lds-roller div:nth-child(7) {
        animation-delay: -0.252s;
      }
      .lds-roller div:nth-child(7):after {
        top: 62.62742px;
        left: 17.37258px;
      }
      .lds-roller div:nth-child(8) {
        animation-delay: -0.288s;
      }
      .lds-roller div:nth-child(8):after {
        top: 56px;
        left: 12.28719px;
      }
      @keyframes lds-roller {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
      #no-movies-message {
        text-align: center;
        margin-top: 400px;
        font-size: 18px;
      }
    </style>
  </head>
  <body>
    <div class="lds-roller" id="lds-roller">
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
    </div>
    <div style="text-align: center">
      <input
        type="text"
        id="trailer-link"
        placeholder="Trailer link"
        style="
          height: 30px;
          margin-top: 15px;
          width: 200px;
          border-radius: 10px;
          border: 1px solid black;
          font-size: 15px;
          padding-left: 10px;
          margin-left: 0px;
        "
      />
      <button
        id="submit-button"
        style="
          background-color: green;
          color: white;
          height: 35px;
          width: 100px;
          border-radius: 10px;
          margin-left: 5px;
          border: none;
          cursor: pointer;
        "
      >
        Check
      </button>
      <button
        id="home-button"
        style="
          background-color: black;
          color: white;
          height: 35px;
          width: 100px;
          border-radius: 10px;
          margin-left: 50px;
          border: none;
          cursor: pointer;
        "
      >
        Home
      </button>
      <button
        id="crawl-trailer-button"
        style="
          background-color: blue;
          color: white;
          height: 35px;
          width: 100px;
          border-radius: 10px;
          margin-left: 5px;
          border: none;
          cursor: pointer;
        "
      >
        Crawl Trailer
      </button>
      <button
        id="crawl-movies-button"
        style="
          background-color: red;
          color: white;
          height: 35px;
          width: 100px;
          border-radius: 10px;
          margin-left: 5px;
          border: none;
          cursor: pointer;
        "
      >
        Crawl Movies
      </button>

      <button
        id="unverifymoviesbutton"
        style="
          background-color: #ff9800;
          color: white;
          height: 35px;
          width: 150px;
          border-radius: 10px;
          margin-left: 5px;
          border: none;
          cursor: pointer;
        "
      >
        Unverified Movies
      </button>

      <input
        type="text"
        id="search-movie"
        placeholder="Search movie"
        style="
          height: 30px;
          margin-top: 15px;
          width: 200px;
          border-radius: 10px;
          border: 1px solid black;
          font-size: 15px;
          padding-left: 10px;
          margin-left: 200px;
        "
      />
    </div>

    <div id="data-list" style="margin-left: 160px; margin-right: 80px"></div>
    <div id="no-movies-message" style="display: none">No movies found</div>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const loader = document.getElementById("lds-roller");
        const dataList = document.getElementById("data-list");
        const searchInput = document.getElementById("search-movie");
        const trailerInput = document.getElementById("trailer-link");
        const submitButton = document.getElementById("submit-button");
        const crawlTrailerButton = document.getElementById(
          "crawl-trailer-button"
        );
        const crawlMoviesButton = document.getElementById(
          "crawl-movies-button"
        );
        const homeButton = document.getElementById("home-button");
        const apiEndpoint = "http://**************:8010/adminshowpost/1";
        let debounceTimer;
        let keyword = null;

        loader.style.display = "block";

        function fetchData(keyword) {
          const formData = new FormData();
          formData.append("sort", "date");
          formData.append("key", keyword);

          fetch(apiEndpoint, {
            method: "POST",
            headers: {
              Auth: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwiaWF0IjoxNzE2NzgyNDk2fQ.jOZUk0DLDcmVrsxczNcF-BlQmC-UI9s2B5i5sKv7wTY",
            },
            body: formData,
          })
            .then((response) => response.json())
            .then((data) => {
              loader.style.display = "none";
              dataList.innerHTML = "";
              console.log("data.result.length", data.status);
              if (data.status != 0) {
                data.result.forEach((item) => {
                  const card = document.createElement("div");
                  card.classList.add("card");
                  card.innerHTML = `
  <img src="${item.thumbnail}" alt="${item.title}">
  <h3>${item.title}</h3>
  <p>Caption :${item.caption}</p>
  <p>Type : ${item.ismovie == 1 ? "Movie" : "Web Series"}</p>
  <p>Language :${item.language}</p>
  <p>Category :${item.category}</p>
  <p>Genre :${item.genre}</p>
  <div style="display: flex; justify-content: space-between; padding: 10px;">
    <button type="button" onclick="window.open('${
      item.trailer
    }', '_blank')" style="background-color: #0D6efd; color: white; border: none; border-radius: 5px; padding: 5px 10px; cursor: pointer;height:30px;width:80px">Trailer</button>
      <button type="button" onclick="window.open('${
        item.post
      }', '_blank')" style="background-color: green; color: white; border: none; border-radius: 5px; padding: 5px 10px; cursor: pointer;height:30px;width:80px">Movie</button>
  </div>
`;
                  dataList.appendChild(card);
                });
                if (data.result.length === 0) {
                  document.getElementById("no-movies-message").style.display =
                    "block";
                } else {
                  document.getElementById("no-movies-message").style.display =
                    "none";
                }
              } else {
                document.getElementById("no-movies-message").style.display =
                  "block";
              }
            })
            .catch((error) => {
              loader.style.display = "none";
              console.error("Error fetching data:", error);
            });
        }

        searchInput.addEventListener("input", (event) => {
          const inputValue = event.target.value.trim();
          if (inputValue === "") {
            keyword = null;
          } else {
            keyword = inputValue;
          }

          clearTimeout(debounceTimer);
          debounceTimer = setTimeout(() => {
            fetchData(keyword);
          }, 500);
        });

        homeButton.addEventListener("click", (event) => {
          loader.style.display = "block";
          document.getElementById("no-movies-message").style.display = "none";
          fetchData(keyword);
        });

        submitButton.addEventListener("click", async () => {
          const trailerLink = trailerInput.value.trim();
          const regexp =
            /^.*(?:(?:youtu\.be\/|v\/|vi\/|u\/\w\/|embed\/|shorts\/)|(?:(?:watch)?\?v(?:i)?=|\&v(?:i)?=))([^#\&\?]*).*/;
          const videoid = trailerLink.match(regexp);
          let isrestric = false;

          if (videoid != null) {
            try {
              const response = await fetch(
                `https://www.googleapis.com/youtube/v3/videos?id=${videoid[1]}&key=AIzaSyCHWLzPxk8hO6jW-vAH4zn-pG54ihUcYa8&part=snippet,contentDetails,status`
              );
              const jsonData = await response.json();

              if (
                jsonData?.items[0]?.status?.embeddable === false ||
                jsonData?.items[0]?.contentDetails?.contentRating?.ytRating ===
                  "ytAgeRestricted" ||
                jsonData?.items[0]?.contentDetails?.regionRestriction?.allowed
                  ?.length > 0 ||
                jsonData?.items.length == 0
              ) {
                isrestric = true;
              }
            } catch (error) {
              console.error("Error fetching YouTube video data:", error);
            }
          }

          if (isrestric) {
            alert("Trailer link is not supported");
          }
          trailerInput.value = "";
        });

        crawlTrailerButton.addEventListener("click", () => {
          loader.style.display = "block";
          fetch("http://**************:8010/crawltrailer/1", {
            method: "POST",
            headers: {
              Auth: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwiaWF0IjoxNzE2NzgyNDk2fQ.jOZUk0DLDcmVrsxczNcF-BlQmC-UI9s2B5i5sKv7wTY",
            },
          })
            .then((response) => response.json())
            .then((data) => {
              loader.style.display = "none";
              dataList.innerHTML = "";
              console.log("data.result.length", data.status);
              if (data.status === 200) {
                data.data.forEach((item) => {
                  const card = document.createElement("div");
                  card.classList.add("card");
                  card.innerHTML = `
  <img src="${item.thumbnail}" alt="${item.title}">
  <h3>${item.title}</h3>
  <p>Caption :${item.caption}</p>
  <p>Type : ${item.ismovie == 1 ? "Movie" : "Web Series"}</p>
  <p>Language :${item.language}</p>
  <p>Category :${item.category}</p>
  <p>Genre :${item.genre}</p>
  <div style="display: flex; justify-content: space-between; padding: 10px;">
    <button type="button" onclick="window.open('${
      item.trailer
    }', '_blank')" style="background-color: #0D6efd; color: white; border: none; border-radius: 5px; padding: 5px 10px; cursor: pointer;height:30px;width:80px">Trailer</button>
      <button type="button" onclick="window.open('${
        item.link
      }', '_blank')" style="background-color: green; color: white; border: none; border-radius: 5px; padding: 5px 10px; cursor: pointer;height:30px;width:80px">Movie</button>
  </div>
`;
                  dataList.appendChild(card);
                });
                if (data.result.length === 0) {
                  document.getElementById("no-movies-message").style.display =
                    "block";
                } else {
                  document.getElementById("no-movies-message").style.display =
                    "none";
                }
              } else {
                document.getElementById("no-movies-message").style.display =
                  "block";
              }
            })
            .catch((error) => {
              loader.style.display = "none";
              console.error("Error fetching data:", error);
            });
        });

        crawlMoviesButton.addEventListener("click", () => {
          loader.style.display = "block";
          fetch("http://**************:8010/crawlmovies/1", {
            method: "POST",
            headers: {
              Auth: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwiaWF0IjoxNzE2NzgyNDk2fQ.jOZUk0DLDcmVrsxczNcF-BlQmC-UI9s2B5i5sKv7wTY",
            },
          })
            .then((response) => response.json())
            .then((data) => {
              loader.style.display = "none";
              dataList.innerHTML = "";
              console.log("data.result.length", data.status);
              if (data.status === 200) {
                data.data.forEach((item) => {
                  const card = document.createElement("div");
                  card.classList.add("card");
                  card.innerHTML = `
  <img src="${item.thumbnail}" alt="${item.title}">
  <h3>${item.title}</h3>
  <p>Caption :${item.caption}</p>
  <p>Type : ${item.ismovie == 1 ? "Movie" : "Web Series"}</p>
  <p>Language :${item.language}</p>
  <p>Category :${item.category}</p>
  <p>Genre :${item.genre}</p>
  <div style="display: flex; justify-content: space-between; padding: 10px;">
    <button type="button" onclick="window.open('${
      item.trailer
    }', '_blank')" style="background-color: #0D6efd; color: white; border: none; border-radius: 5px; padding: 5px 10px; cursor: pointer;height:30px;width:80px">Trailer</button>
      <button type="button" onclick="window.open('${
        item.link
      }', '_blank')" style="background-color: green; color: white; border: none; border-radius: 5px; padding: 5px 10px; cursor: pointer;height:30px;width:80px">Movie</button>
  </div>
`;
                  dataList.appendChild(card);
                });
                if (data.result.length === 0) {
                  document.getElementById("no-movies-message").style.display =
                    "block";
                } else {
                  document.getElementById("no-movies-message").style.display =
                    "none";
                }
              } else {
                document.getElementById("no-movies-message").style.display =
                  "block";
              }
            })
            .catch((error) => {
              loader.style.display = "none";
              console.error("Error fetching data:", error);
            });
        });

        unverifymoviesbutton.addEventListener("click", () => {
          loader.style.display = "block";
          fetch("http://**************:8010/adminshowunverifiedpost/1", {
            method: "POST",
            headers: {
              Auth: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwiaWF0IjoxNzE2NzgyNDk2fQ.jOZUk0DLDcmVrsxczNcF-BlQmC-UI9s2B5i5sKv7wTY",
            },
          })
            .then((response) => response.json())
            .then((data) => {
              loader.style.display = "none";
              dataList.innerHTML = "";
              console.log("data.result.length", data.status);
              if (data.status === 200) {
                data.result.forEach((item) => {
                  const card = document.createElement("div");
                  card.classList.add("card");
                  card.innerHTML = `
  <img src="${item.thumbnail}" alt="${
                    item.title
                  }" style="width: 100%; border-radius: 8px;">

  <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;padding-left :"10px">
    <h3 style="flex-grow: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; margin-left: 10px; ">
      ${item.title}
    </h3>
    <button class="info-btn" data-info="${item.title.replace(/"/g, "&quot;")}" 
      style="background: white; border: 1px solid #007bff; color: #007bff; font-size: 16px; cursor: pointer; margin-right:"5px";margin-top:"15px">ℹ</button>
  </div>

  <div style="display: flex; align-items: center; justify-content: space-between; width: 100%; ">
    <p style="flex-grow: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; margin-left: 10px; font-size: 14px;">
      Caption: ${item.caption}
    </p>
    <button class="info-btn" data-info="${item.caption.replace(
      /"/g,
      "&quot;"
    )}" 
     style="background: white; border: 1px solid #007bff; color: #007bff; font-size: 16px; cursor: pointer; margin-right:"5px";margin-top:"15px">ℹ</button>
  </div>

  <p style="margin: 5px 0; font-size: 14px;margin-left: 10px; ">Type: ${
    item.ismovie == 1 ? "Movie" : "Web Series"
  }</p>
  <p style="margin: 5px 0; font-size: 14px;margin-left: 10px; ">Language: ${
    item.language
  }</p>
  <p style="margin: 5px 0; font-size: 14px;margin-left: 10px; ">Category: ${
    item.category
  }</p>
  <p style="margin: 5px 0; font-size: 14px;margin-left: 10px; ">Genre: ${
    item.genre
  }</p>

  <div style="display: flex; justify-content: space-between; padding: 10px;margin-left: 10px; ">
    <button type="button" onclick="window.open('${item.trailer}', '_blank')" 
      style="background-color: #0D6efd; color: white; border: none; border-radius: 5px; padding: 5px 10px; cursor: pointer; height: 30px; width: 80px;">Trailer</button>
    <button type="button" onclick="window.open('${item.link}', '_blank')" 
      style="background-color: green; color: white; border: none; border-radius: 5px; padding: 5px 10px; cursor: pointer; height: 30px; width: 80px;">Movie</button>
  </div>
`;

                  dataList.appendChild(card);
                });
                if (data.result.length === 0) {
                  document.getElementById("no-movies-message").style.display =
                    "block";
                } else {
                  document.getElementById("no-movies-message").style.display =
                    "none";
                }
              } else {
                document.getElementById("no-movies-message").style.display =
                  "block";
              }
            })
            .catch((error) => {
              loader.style.display = "none";
              console.error("Error fetching data:", error);
            });
        });

        fetchData(keyword);
      });

      document.addEventListener("mousedown", function (e) {
        let popup = document.getElementById("infoPopup");

        if (!popup) {
          popup = document.createElement("div");
          popup.id = "infoPopup";
          document.body.appendChild(popup);
        }

        // Basic styles
        popup.style.position = "absolute";
        popup.style.background = "#fff";
        popup.style.border = "1px solid #ddd";
        popup.style.padding = "10px 12px";
        popup.style.borderRadius = "8px";
        popup.style.boxShadow = "0 4px 10px rgba(0, 0, 0, 0.15)";
        popup.style.fontSize = "14px";
        popup.style.zIndex = "9999";
        popup.style.maxWidth = "250px";
        popup.style.display = popup.style.display || "none";
        popup.style.userSelect = "text"; // allow text selection

        if (e.target.classList.contains("info-btn")) {
          const infoText = e.target.getAttribute("data-info");
          popup.innerText = infoText;

          // Position the popup near the clicked button
          const rect = e.target.getBoundingClientRect();
          popup.style.top = `${rect.bottom + window.scrollY + 5}px`;
          popup.style.left = `${rect.left + window.scrollX}px`;
          popup.style.display = "block";
        } else {
          // Only close if clicked outside popup and not on any info-btn
          if (
            !popup.contains(e.target) &&
            !e.target.classList.contains("info-btn")
          ) {
            popup.style.display = "none";
          }
        }
      });
    </script>
    <div id="infoPopup" class="info-popup"></div>
  </body>
</html>
