import 'package:my_video/app_imports.dart';

class SearchPage extends StatelessWidget {
  const SearchPage({super.key});

  @override
  Widget build(BuildContext context) {
    MySize.init(context);
    return GetBuilder<MovieSearchController>(
      init: MovieSearchController(),
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            title: Container(
              height: MySize.height(40),
              child: AppText<PERSON>orm<PERSON><PERSON>(
                controller: controller.searchTextController,
                hintText: 'Search movies...',
                prefixIcon: Icon(
                  Icons.search,
                  color: AppColorConstants.textSecondary,
                  size: MySize.height(20),
                ),
                suffixIcon: controller.searchQuery.isNotEmpty
                    ? GestureDetector(
                        onTap: controller.clearSearch,
                        child: Icon(
                          Icons.clear,
                          color: AppColorConstants.textSecondary,
                          size: MySize.height(20),
                        ),
                      )
                    : null,
              ),
            ),
            backgroundColor: AppColorConstants.backgroundColor,
            elevation: 0,
          ),
          body: Column(
            children: [
              // Category Filter
              if (controller.searchQuery.isNotEmpty) ...[
                _buildCategoryFilter(controller),
                const Divider(color: AppColorConstants.dividerColor),
              ],

              // Content
              Expanded(
                child: controller.searchQuery.isEmpty
                    ? _buildSearchSuggestions(controller)
                    : controller.isSearching
                    ? const Center(child: CircularProgressIndicator())
                    : controller.searchResults.isEmpty
                    ? _buildNoResults(controller)
                    : _buildSearchResults(controller),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCategoryFilter(MovieSearchController controller) {
    return Container(
      height: MySize.height(50),
      padding: EdgeInsets.symmetric(vertical: MySize.height(8)),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
        itemCount: controller.categories.length,
        itemBuilder: (context, index) {
          final category = controller.categories[index];
          final isSelected = category == controller.selectedCategory;

          return Container(
            margin: EdgeInsets.only(right: MySize.width(8)),
            child: FilterChip(
              label: AppText(
                text: category,
                fontSize: MySize.fontSize(14),
                color: isSelected
                    ? AppColorConstants.textPrimary
                    : AppColorConstants.textSecondary,
              ),
              selected: isSelected,
              onSelected: (_) => controller.selectCategory(category),
              backgroundColor: AppColorConstants.cardColor,
              selectedColor: AppColorConstants.primaryColor,
              checkmarkColor: AppColorConstants.textPrimary,
            ),
          );
        },
      ),
    );
  }

  Widget _buildSearchSuggestions(MovieSearchController controller) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(MySize.width(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search History
          if (controller.searchHistory.isNotEmpty) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AppText(
                  text: 'Recent Searches',
                  fontSize: MySize.fontSize(18),
                  fontWeight: FontWeight.bold,
                  color: AppColorConstants.textPrimary,
                ),
                GestureDetector(
                  onTap: controller.clearSearchHistory,
                  child: AppText(
                    text: 'Clear All',
                    fontSize: MySize.fontSize(14),
                    color: AppColorConstants.primaryColor,
                  ),
                ),
              ],
            ),
            Space.height(12),
            ...controller.searchHistory.map((query) {
              return Container(
                margin: EdgeInsets.only(bottom: MySize.height(8)),
                child: ListTile(
                  leading: Icon(
                    Icons.history,
                    color: AppColorConstants.textSecondary,
                    size: MySize.height(20),
                  ),
                  title: AppText(
                    text: query,
                    fontSize: MySize.fontSize(16),
                    color: AppColorConstants.textPrimary,
                  ),
                  trailing: GestureDetector(
                    onTap: () => controller.removeFromHistory(query),
                    child: Icon(
                      Icons.close,
                      color: AppColorConstants.textSecondary,
                      size: MySize.height(18),
                    ),
                  ),
                  onTap: () => controller.searchWithQuery(query),
                  tileColor: AppColorConstants.cardColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(MySize.radius(8)),
                  ),
                ),
              );
            }),
            Space.height(24),
          ],

          // Popular Searches
          AppText(
            text: 'Popular Searches',
            fontSize: MySize.fontSize(18),
            fontWeight: FontWeight.bold,
            color: AppColorConstants.textPrimary,
          ),
          Space.height(12),
          Wrap(
            spacing: MySize.width(8),
            runSpacing: MySize.height(8),
            children: controller.popularSearches.map((search) {
              return GestureDetector(
                onTap: () => controller.searchWithQuery(search),
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: MySize.width(12),
                    vertical: MySize.height(8),
                  ),
                  decoration: BoxDecoration(
                    color: AppColorConstants.cardColor,
                    borderRadius: BorderRadius.circular(MySize.radius(20)),
                    border: Border.all(color: AppColorConstants.dividerColor),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.trending_up,
                        color: AppColorConstants.primaryColor,
                        size: MySize.height(16),
                      ),
                      Space.width(4),
                      AppText(
                        text: search,
                        fontSize: MySize.fontSize(14),
                        color: AppColorConstants.textPrimary,
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResults(MovieSearchController controller) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: MySize.height(80),
            color: AppColorConstants.textHint,
          ),
          Space.height(16),
          AppText(
            text: 'No results found',
            fontSize: MySize.fontSize(18),
            fontWeight: FontWeight.w600,
            color: AppColorConstants.textPrimary,
          ),
          Space.height(8),
          AppText(
            text:
                'Try searching with different keywords\nor check your spelling',
            fontSize: MySize.fontSize(14),
            color: AppColorConstants.textSecondary,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults(MovieSearchController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Results count
        Padding(
          padding: EdgeInsets.all(MySize.width(16)),
          child: AppText(
            text:
                '${controller.searchResults.length} results for "${controller.searchQuery}"',
            fontSize: MySize.fontSize(16),
            color: AppColorConstants.textSecondary,
          ),
        ),

        // Results grid
        Expanded(
          child: GridView.builder(
            padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.7,
              crossAxisSpacing: MySize.width(12),
              mainAxisSpacing: MySize.height(16),
            ),
            itemCount: controller.searchResults.length,
            itemBuilder: (context, index) {
              final movie = controller.searchResults[index];
              return MovieCard(
                movie: movie,
                onTap: () => controller.playMovie(movie),
                showTitle: true,
                showRating: true,
                showDuration: true,
              );
            },
          ),
        ),
      ],
    );
  }
}
