import 'package:flutter/material.dart';

class AppColorConstants {
  static const Color primaryColor = Color(0xff0345A2);
  static const Color primaryColor2 = Color(0xff006CD1);
  static const Color blue225BFF = Color(0xff225BFF);
  static const Color blue1434A7 = Color(0xff1434A7);

  static const Color backgroundColor = Color(0xFF121212);
  static const Color surfaceColor = Color(0xFF1E1E1E);
  static const Color cardColor = Color(0xFF2D2D2D);
  static const Color dividerColor = Color(0xFF3D3D3D);

  static const Color colorPrimary = primaryColor;
  static const Color colorCCE1F5 = Color(0xffCCE1F5);

  static const Color colorPrimaryDark = blue1434A7;
  static const Color colorAccent = blue225BFF;
  static const Color colorWhite = Color(0xFFFFFFFF);
  static const Color colorBlack = Color(0xFF000000);
  static const Color colorGrey = Color(0xFF9E9E9E);
  static const Color colorLightGrey = Color(0xFF2D2D2D);
  static const Color colorDarkGrey = Color(0xFF424242);
  static const Color colorRed = Color(0xFFF44336);
  static const Color colorGreen = Color(0xFF4CAF50);
  static const Color colorBlue = primaryColor2;
  static const Color colorOrange = Color(0xFFFF9800);
  static const Color colorYellow = Color(0xFFFFEB3B);
  static const Color colorPurple = Color(0xFF9C27B0);
  static const Color colorTransparent = Colors.transparent;

  // Text Colors for Dark Theme
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFB3B3B3);
  static const Color textHint = Color(0xFF666666);
}

class AppAssetsConstants {
  static const String defaultFont = 'Arial';
  static const String secondaryFont = 'DinRound';

  // Image paths
  static const String imagesPath = 'assets/images/';
  static const String iconsPath = 'assets/icons/';
  static const String animationsPath = 'assets/animation/';
  static const String fontsPath = 'assets/fonts/';
  static const String dataPath = 'assets/data/';

  // Common icons
  static const String iconLogo = '${iconsPath}logo.png';
  static const String iconPlaceholder = '${iconsPath}placeholder.png';

  // Common images
  static const String imagePlaceholder = '${imagesPath}placeholder.png';
  static const String imageNoData = '${imagesPath}no_data.png';
  static const String imageNoInternet = '${imagesPath}no_internet.png';
}

class AppSizeConstants {
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingExtraLarge = 32.0;

  static const double marginSmall = 8.0;
  static const double marginMedium = 16.0;
  static const double marginLarge = 24.0;
  static const double marginExtraLarge = 32.0;

  static const double radiusSmall = 4.0;
  static const double radiusMedium = 8.0;
  static const double radiusLarge = 12.0;
  static const double radiusExtraLarge = 16.0;

  static const double elevationSmall = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationLarge = 8.0;

  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 14.0;
  static const double fontSizeLarge = 16.0;
  static const double fontSizeExtraLarge = 18.0;
  static const double fontSizeTitle = 20.0;
  static const double fontSizeHeading = 24.0;
}

class AppDurationConstants {
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration splashDuration = Duration(seconds: 3);
  static const Duration toastDuration = Duration(seconds: 2);
  static const Duration debounceDelay = Duration(milliseconds: 500);
}
