/* eslint-disable */
import React, { useState, useEffect } from "react";
import { Link, useParams, useNavigate } from "react-router-dom";
import "bootstrap/dist/css/bootstrap.min.css";

import CardMedia from "@mui/material/CardMedia";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import MDButton from "components/MDButton";
import Footer from "examples/Footer";
import "bootstrap/dist/css/bootstrap.min.css";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
const index = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [data, setdata] = useState();
  const token = sessionStorage.getItem("auth");
  const getdata = async () => {
    const res = await fetch(`${window.path}/adminshowinduser/${id}`, {
      method: "GET",
      headers: {
        auth: token,
      },
    });

    const result = await res.json();
    setdata(result);
  };

  useEffect(() => {
    getdata();
  }, []);

  const del = async () => {
    console.log(token);
    let form = new FormData();
    form.append("id", data.result[0].id);
    //console.log(form.get("id"))
    const res = await fetch(`${window.path}/admindeleteuser`, {
      method: "DELETE",
      headers: {
        auth: token,
      },
      body: form,
    });
    const delres = await res.json();
    if (delres.status === 201) {
      alert("deleted successfully");
      toast("deleted succefully");
      setTimeout(navigate("/admin/users"), 3000);
    } else {
      toast("invalid credintial");
    }
  };
  return (
    <DashboardLayout>
      <DashboardNavbar />

      <section>
        <div className="container py-5">
          {data?.result?.map((e) => {
            return (
              <div className="row" key={e.id}>
                <div className="col-lg-4">
                  <div className=" mb-4">
                    <CardMedia
                      src={`http://localhost:8000/upload/profile/${e.profile}`}
                      className="card p-4 text-center"
                      component={
                        e.profile?.slice(
                          e.profile.length - 3,
                          e.profile.length
                        ) == "mp4"
                          ? "video"
                          : "img"
                      }
                      title="profile picture "
                      controls
                      sx={{
                        maxWidth: "100%",
                        margin: 0,
                        boxShadow: ({ boxShadows: { md } }) => md,
                        objectFit: "cover",
                        objectPosition: "center",
                      }}
                    />
                  </div>
                </div>
                <div className="col-lg-8">
                  <div className="card mb-4">
                    <div className="card-body">
                      <div className="row">
                        <div className="col-sm-3 ">
                          <p className="mb-0">user name: </p>
                        </div>
                        <div className="col-sm-9">
                          <p className="text-muted mb-0">{`${e.fname} ${e.lname}`}</p>
                        </div>
                      </div>
                      <hr />
                      <div className="row">
                        <div className="col-sm-3">
                          <p className="mb-0">email :</p>
                        </div>
                        <div className="col-sm-9">
                          <p className="text-muted mb-0">{e.email}</p>
                        </div>
                      </div>
                      <hr />
                      <div className="row">
                        <div className="col-sm-3">
                          <p className="mb-0">post :</p>
                        </div>
                        <div className="col-sm-9">
                          <p className="text-muted mb-0">{e.post_count}</p>
                        </div>
                      </div>
                      <hr />
                      <div className="row">
                        <div className="col-sm-3">
                          <p className="mb-0">follower :</p>
                        </div>
                        <div className="col-sm-9">
                          <p className="text-muted mb-0">{e.follower_count}</p>
                        </div>
                      </div>
                      <hr />
                      <div className="row">
                        <div className="col-sm-3">
                          <p className="mb-0">following :</p>
                        </div>
                        <div className="col-sm-9">
                          <p className="text-muted mb-0">{e.following_count}</p>
                        </div>
                      </div>
                      <hr />
                      <div className="row">
                        <div className="col-sm-3">
                          <p className="mb-0">gender :</p>
                        </div>
                        <div className="col-sm-9">
                          <p className="text-muted mb-0">{e.gender}</p>
                        </div>
                      </div>
                      <hr />
                      <div className="row">
                        <div className="col-sm-3">
                          <p className="mb-0">DOB :</p>
                        </div>
                        <div className="col-sm-9">
                          <p className="text-muted mb-0">
                            {e.dob?.slice(0, 10)}
                          </p>
                        </div>
                      </div>
                      <hr />
                      <div className="row">
                        <div className="col-sm-3">
                          <p className="mb-0">phone</p>
                        </div>
                        <div className="col-sm-9">
                          <p className="text-muted mb-0">{e.phone}</p>
                        </div>
                      </div>
                      <hr />
                      <div className="row">
                        <div className="col-sm-3">
                          <p className="mb-0">join date :</p>
                        </div>
                        <div className="col-sm-9">
                          <p className="text-muted mb-0">
                            {e.created_date?.slice(0, 10)}
                          </p>
                        </div>
                      </div>
                      <hr />
                      <div className="row">
                        <div className="col-sm-3">
                          <p className="mb-0">updated date :</p>
                        </div>
                        <div className="col-sm-9 ">
                          <p className="text-muted mb-0">
                            {e.updated_date?.slice(0, 10)}
                          </p>
                        </div>
                      </div>

                      <hr />
                      <div className="row">
                        <div className="col-sm-5"></div>
                        <div className="col-sm-7 mt-4 ">
                          <MDButton
                            onClick={del}
                            variant="contained"
                            size="medium"
                            color="error"
                          >
                            delete
                          </MDButton>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </section>
      <Footer />
    </DashboardLayout>
  );
};

export default index;
