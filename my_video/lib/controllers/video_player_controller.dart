import 'package:my_video/app_imports.dart';

class VideoPlayerController extends GetxController {
  final MovieRepository _movieRepository = GetIt.instance<MovieRepository>();
  final Logger _logger = Logger();

  @override
  void onInit() {
    super.onInit();
  }

  // API Methods - Only API calls, no UI state management
  Future<List<MovieModel>> getRelatedMoviesFromAPI(
    String movieId,
    String category,
  ) async {
    try {
      return await _movieRepository.getRelatedMovies(movieId, category);
    } catch (e) {
      _logger.e('Error getting related movies from API: $e');
      rethrow;
    }
  }
}
