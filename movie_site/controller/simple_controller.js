const bodyParser = require("body-parser");
const database = require("../db/database");
const express = require("express");
const bcrypt = require("bcrypt");
const fs = require("fs");
const jwt = require("jsonwebtoken");
require("dotenv").config();

jsonparser = bodyParser.json();

var connection = database;

// Simple dashboard endpoint
const dashboard = (req, res) => {
  try {
    console.log("Dashboard endpoint called");
    
    // Get basic stats from database
    connection.query(
      "SELECT COUNT(*) as total_posts FROM post_tbl",
      (err, postCount) => {
        if (err) {
          console.error("Error getting post count:", err);
          return res.status(500).json({ status: 0, message: "Database error" });
        }

        connection.query(
          "SELECT COUNT(*) as total_categories FROM category_tbl",
          (err, catCount) => {
            if (err) {
              console.error("Error getting category count:", err);
              return res.status(500).json({ status: 0, message: "Database error" });
            }

            const response = {
              status: 1,
              message: "Dashboard data retrieved",
              totalpost: postCount[0]?.total_posts || 0,
              totalcategories: catCount[0]?.total_categories || 0,
              months: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
              monthlypost: [10, 20, 15, 25, 30, 35],
              monthlyuser: [5, 10, 8, 12, 15, 18],
              totalGenre: ["Action", "Comedy", "Drama"],
              countGenreWisePost: [15, 10, 8],
              totallang: ["English", "Hindi"],
              countLangWisePost: [20, 13],
              postIncreaseLastYearPercentage: 25
            };

            res.json(response);
          }
        );
      }
    );
  } catch (error) {
    console.error("Dashboard error:", error);
    res.status(500).json({ status: 0, message: "Internal server error" });
  }
};

// Show posts endpoint
const UserShowPosts = (req, res) => {
  try {
    console.log("UserShowPosts endpoint called");
    const page = req.params.page || 1;
    const limit = 20;
    const offset = (page - 1) * limit;

    const query = `
      SELECT
        p.id,
        p.title,
        p.link,
        p.thumbnail,
        p.trailer_link,
        p.caption,
        p.views,
        p.imdb_rating,
        p.google_rating,
        p.created_date,
        p.source_name as user_name,
        p.source_id as user_id,
        p.source_profile as profile,
        p.ismovie,
        p.season,
        p.cat_id,
        p.lang_id,
        p.genre_id
      FROM post_tbl p
      ORDER BY p.created_date DESC
      LIMIT ? OFFSET ?
    `;

    connection.query(query, [limit, offset], (err, data) => {
      if (err) {
        console.error("Error fetching posts:", err);
        return res.status(500).json({ status: 0, message: "Database error" });
      }

      if (data && data.length > 0) {
        const result = data.map(post => ({
          ...post,
          thumbnail: post.thumbnail ? `${process.env.PUBLIC_POST_PATH || '/upload/thumbnail/'}${post.thumbnail}` : null,
          profile: post.profile ? `${process.env.PUBLIC_PROFILE_PATH || '/upload/profile/'}${post.profile}` : null,
          category: "Action,Comedy", // Simplified for now
          language: "English,Hindi", // Simplified for now
          genre: "Action" // Simplified for now
        }));

        res.json({
          status: 1,
          message: "Posts found",
          result: result,
          lastpage: data.length < limit,
          records: data.length
        });
      } else {
        res.json({
          status: 0,
          message: "No posts found",
          result: [],
          lastpage: true,
          records: 0
        });
      }
    });
  } catch (error) {
    console.error("UserShowPosts error:", error);
    res.status(500).json({ status: 0, message: "Internal server error" });
  }
};

// Search posts endpoint
const UserSearchPost = (req, res) => {
  try {
    console.log("UserSearchPost endpoint called");
    const page = req.params.page || 1;
    const searchKey = req.body.key || '';
    const limit = 20;
    const offset = (page - 1) * limit;

    if (!searchKey) {
      return res.json({
        status: 0,
        message: "Search key is required",
        result: [],
        lastpage: true,
        records: 0
      });
    }

    const query = `
      SELECT
        p.id,
        p.title,
        p.link,
        p.thumbnail,
        p.trailer_link,
        p.caption,
        p.views,
        p.imdb_rating,
        p.google_rating,
        p.created_date,
        p.source_name as user_name,
        p.source_id as user_id,
        p.source_profile as profile,
        p.ismovie,
        p.season,
        p.cat_id,
        p.lang_id,
        p.genre_id
      FROM post_tbl p
      WHERE p.title LIKE ?
      ORDER BY p.created_date DESC
      LIMIT ? OFFSET ?
    `;

    connection.query(query, [`%${searchKey}%`, limit, offset], (err, data) => {
      if (err) {
        console.error("Error searching posts:", err);
        return res.status(500).json({ status: 0, message: "Database error" });
      }

      if (data && data.length > 0) {
        const result = data.map(post => ({
          ...post,
          thumbnail: post.thumbnail ? `${process.env.PUBLIC_POST_PATH || '/upload/thumbnail/'}${post.thumbnail}` : null,
          profile: post.profile ? `${process.env.PUBLIC_PROFILE_PATH || '/upload/profile/'}${post.profile}` : null,
          category: "Action,Comedy", // Simplified for now
          language: "English,Hindi", // Simplified for now
          genre: "Action" // Simplified for now
        }));

        res.json({
          status: 1,
          message: "Posts found",
          result: result,
          lastpage: data.length < limit,
          records: data.length
        });
      } else {
        res.json({
          status: 0,
          message: "No posts found",
          result: [],
          lastpage: true,
          records: 0
        });
      }
    });
  } catch (error) {
    console.error("UserSearchPost error:", error);
    res.status(500).json({ status: 0, message: "Internal server error" });
  }
};

// Show filters endpoint
const ShowFilter = (req, res) => {
  try {
    console.log("ShowFilter endpoint called");
    
    // Get categories
    connection.query("SELECT * FROM category_tbl ORDER BY cat_name", (err, categories) => {
      if (err) {
        console.error("Error fetching categories:", err);
        return res.status(500).json({ status: 0, message: "Database error" });
      }

      // Get languages
      connection.query("SELECT * FROM language_tbl ORDER BY language", (err, languages) => {
        if (err) {
          console.error("Error fetching languages:", err);
          return res.status(500).json({ status: 0, message: "Database error" });
        }

        // Get genres
        connection.query("SELECT * FROM genre_tbl ORDER BY genre", (err, genres) => {
          if (err) {
            console.error("Error fetching genres:", err);
            return res.status(500).json({ status: 0, message: "Database error" });
          }

          res.json({
            status: 1,
            message: "Filters retrieved",
            categories: categories || [],
            languages: languages || [],
            genres: genres || []
          });
        });
      });
    });
  } catch (error) {
    console.error("ShowFilter error:", error);
    res.status(500).json({ status: 0, message: "Internal server error" });
  }
};

// Admin login endpoint
const adminLogin = (req, res) => {
  try {
    console.log("Admin login attempt - body:", req.body);
    console.log("Admin login attempt - headers:", req.headers);
    const { email, username, password } = req.body;

    // Check admin credentials from environment variables
    const adminEmail = process.env.ADMIN || "admin";
    const adminPassword = process.env.PASS || "admin123";

    // Accept both email and username fields
    const loginField = email || username;

    if (loginField === adminEmail && password === adminPassword) {
      // Generate a simple JWT token (you can enhance this)
      const jwt = require("jsonwebtoken");
      const token = jwt.sign(
        { username: adminEmail, role: "admin" },
        process.env.ADMIN_SECRET || "joge15fs4a5sd4f7abns5a4a",
        { expiresIn: "24h" }
      );

      res.json({
        status: 1,
        message: "Login successful",
        token: token,
        cb: token, // Admin panel expects 'cb' field
        user: {
          username: adminEmail,
          email: adminEmail,
          role: "admin"
        }
      });
    } else {
      res.status(401).json({
        status: 0,
        message: "Invalid credentials"
      });
    }
  } catch (error) {
    console.error("Admin login error:", error);
    res.status(500).json({ status: 0, message: "Internal server error" });
  }
};

// Admin dashboard test endpoint
const adminDashboardTest = (req, res) => {
  try {
    console.log("Admin dashboard test endpoint called");

    // Get basic stats from database
    connection.query(
      "SELECT COUNT(*) as total_posts FROM post_tbl",
      (err, postCount) => {
        if (err) {
          console.error("Error getting post count:", err);
          return res.status(500).json({ status: 0, message: "Database error" });
        }

        connection.query(
          "SELECT COUNT(*) as total_users FROM user_tbl",
          (err, userCount) => {
            if (err) {
              console.error("Error getting user count:", err);
              return res.status(500).json({ status: 0, message: "Database error" });
            }

            const response = {
              status: 1,
              message: "Admin dashboard data retrieved",
              totalpost: postCount[0]?.total_posts || 0,
              totaluser: userCount[0]?.total_users || 0,
              totalcategories: 14,
              months: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
              monthlypost: [10, 20, 15, 25, 30, 35],
              monthlyuser: [5, 10, 8, 12, 15, 18],
              totalGenre: ["Action", "Comedy", "Drama"],
              countGenreWisePost: [15, 10, 8],
              totallang: ["English", "Hindi"],
              countLangWisePost: [20, 13],
              postIncreaseLastYearPercentage: 25,
              userIncreaseLastYearPercentage: 15
            };

            res.json(response);
          }
        );
      }
    );
  } catch (error) {
    console.error("Admin dashboard test error:", error);
    res.status(500).json({ status: 0, message: "Internal server error" });
  }
};

module.exports = {
  dashboard,
  UserShowPosts,
  UserSearchPost,
  ShowFilter,
  adminLogin,
  adminDashboardTest
};
