import React, { useState, useEffect } from "react";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import { useParams } from "react-router-dom";
import axios from "axios";
import { ToastContainer, toast } from "react-toastify";

function Episodes() {
  const { id } = useParams(); // This is your movid
  const [episodes, setEpisodes] = useState([]);

  const [link, setLink] = useState("");
  const [epno, setEpno] = useState("");
  const [subTitle, setSubTitle] = useState("");

  const handleAddEpisode = async () => {
    if (!link || !epno) {
      toast.error("Link and Episode number are required");
      return;
    }

    const duplicate = episodes.find((ep) => ep.epNo == epno);

    console.log("episodes", JSON.stringify(episodes));

    console.log("exists ep", epno, duplicate);
    if (duplicate) {
      toast.error("Episode number already exists", {
        position: "top-center",
        autoClose: 1500,
      });
      return;
    }

    try {
      const response = await axios.post(
        "http://206.189.137.63:8010/createshorts",
        {
          link,
          epno,
          movieid: id, // Pass movieId from props or state
          sub_title: subTitle,
        },
        {
          headers: {
            "Content-Type": "application/json",
            // Add withCredentials if session is needed
            // withCredentials: true
          },
        }
      );

      if (response.data.status === 201) {
        toast.success("Episode added successfully!", {
          position: "top-center",
          autoClose: 1000,
        });
        setLink("");
        setEpno("");
        fetchEpisodes();
      } else {
        toast.error("Failed to add episode");
      }
    } catch (err) {
      toast.error("Error adding episode");
      console.error(err);
    }
  };

  const fetchEpisodes = async () => {
    try {
      const params = new URLSearchParams();
      params.append("movid", id);

      const response = await axios.post(
        "http://206.189.137.63:8010/mxvideobymovie",
        params,
        {
          headers: {
            auth: "Habt5o0cDNWjc42y",
            "Content-Type": "application/x-www-form-urlencoded",
            // Cookie header is optional if CORS and cookies setup correctly on backend
            // Cookie: "connect.sid=your_cookie_here",
          },
        }
      );

      if (response.data.status === 200 && Array.isArray(response.data.data)) {
        // Map API data to your state shape
        const mappedEpisodes = response.data.data.map((ep) => ({
          epNo: ep.epno,
          epLink: ep.link,
          title: ep.title, // Optional: if you want to use the title
          thumbnail: ep.thumbnail, // Optional: if you want to use the thumbnail
          id: ep.id, // Optional: keep episode id for reference
          subTitle: ep.sub_title,
          is_trailer: ep.is_trailer,
        }));

        setEpisodes(mappedEpisodes);
      } else {
        setEpisodes([]); // clear if no data
      }
    } catch (error) {
      console.error("Failed to fetch episodes:", error);
      setEpisodes([]); // clear on error
    }
  };
  useEffect(() => {
    if (!id) return;

    fetchEpisodes();
  }, [id]);

  const handleInputChange = (index, field, value) => {
    const updatedEpisodes = [...episodes];
    updatedEpisodes[index][field] = value;
    setEpisodes(updatedEpisodes);
  };

  const handleDelete = async (index) => {
    const confirmEdit = window.confirm(
      "Are you sure you want to delete this episode?"
    );
    if (!confirmEdit) return;
    const episode = episodes[index];

    try {
      const response = await axios.delete(
        `http://206.189.137.63:8010/deletemxvideo/${episode.id}`,
        {
          headers: {
            // If your backend does not require the cookie in frontend, omit this
            // If required, you might need to pass credentials and backend must allow it via CORS
            // Example: withCredentials: true
            auth: "Habt5o0cDNWjc42y",
          },
        }
      );

      if (response.data.status === 200) {
        toast.success("Episode Deleted Successfully", {
          position: "top-center",
          autoClose: 1000,
        });

        // Remove from state
        const updatedEpisodes = episodes.filter((_, i) => i !== index);
        setEpisodes(updatedEpisodes);
      } else {
        toast.error("Failed to delete episode", {
          position: "top-center",
          autoClose: 1000,
        });
      }
    } catch (error) {
      console.error("Delete failed:", error);
      toast.error("Error deleting episode", {
        position: "top-center",
        autoClose: 1000,
      });
    }
  };

  const handleEdit = async (episode) => {
    try {
      // Prepare the data object to send
      const payload = {
        id: episode.id,
        epno: episode.epNo,
        link: episode.epLink,
        sub_title: episode.subTitle,
        is_trailer: episode.is_trailer,
      };

      if (episode.is_trailer) {
        delete payload.epno;
        delete payload.sub_title;
      }

      const response = await axios.post(
        "http://206.189.137.63:8010/editmxvideo", // Replace with your update endpoint
        payload,
        {
          headers: {
            auth: "Habt5o0cDNWjc42y",
            "Content-Type": "application/json",
          },
        }
      );

      if (response.data.status === 200) {
        toast.success("Episodes Updated Successfully", {
          position: "top-center",
          autoClose: 1000,
        });
        fetchEpisodes();
        // Optionally refresh the list or update state here
      } else {
        alert("Failed to update episode.");
      }
    } catch (error) {
      console.error("Error updating episode:", error);
      alert("Error updating episode.");
    }
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <ToastContainer />
      <div
        style={{ marginBottom: "20px", float: "right", marginRight: "30px" }}
      >
        <input
          type="text"
          placeholder="Episode Number"
          value={epno}
          onChange={(e) => setEpno(e.target.value)}
          style={{
            width: "70px",
            marginRight: "10px",
            padding: "5px",
            height: "35px",
            borderRadius: "5px",
            border: "1px solid black",
            fontSize: "15px",
          }}
        />
        <input
          type="text"
          placeholder="Episode Sub title"
          value={subTitle}
          onChange={(e) => setSubTitle(e.target.value)}
          style={{
            flex: 1,
            marginRight: "10px",
            padding: "5px",
            borderRadius: "5px",
            border: "1px solid black",
            fontSize: "15px",
            height: "35px",
          }}
        />
        <input
          type="text"
          placeholder="Episode Link"
          value={link}
          onChange={(e) => setLink(e.target.value)}
          style={{
            flex: 1,
            marginRight: "10px",
            padding: "5px",
            borderRadius: "5px",
            border: "1px solid black",
            fontSize: "15px",
            height: "35px",
          }}
        />

        <button
          type="button"
          onClick={() => handleAddEpisode()}
          style={{
            marginRight: "10px",
            height: "35px",
            borderRadius: "5px",
            width: "100px",
            fontSize: "15px",
            border: "none",
            backgroundColor: "green",
            color: "white",
          }}
        >
          Add Episodes
        </button>
      </div>
      <div style={{ padding: "20px", marginTop: "50px" }}>
        <div style={{ display: "grid", gap: "10px" }}>
          {episodes.map((ep, index) => (
            <div
              key={ep.id || index}
              style={{
                display: "flex",
                alignItems: "center",
                borderRadius: "8px",
              }}
            >
              <input
                type="text"
                placeholder="No"
                value={ep.is_trailer ? "Trailer" : ep.epNo}
                onChange={(e) =>
                  handleInputChange(index, "epNo", e.target.value)
                }
                style={{
                  width: "70px",
                  marginRight: "10px",
                  padding: "5px",
                  height: "35px",
                  borderRadius: "5px",
                  border: "1px solid black",
                  fontSize: "15px",
                  textAlign: "center",
                }}
                disabled={ep.is_trailer}
              />
              <input
                type="text"
                placeholder="Sub title"
                value={ep.subTitle}
                onChange={(e) =>
                  handleInputChange(index, "subTitle", e.target.value)
                }
                style={{
                  marginRight: "10px",
                  padding: "5px",
                  borderRadius: "5px",
                  border: "1px solid black",
                  fontSize: "15px",
                  height: "35px",
                  width: "620px",
                }}
              />
              <input
                type="text"
                placeholder="Link"
                value={ep.epLink}
                onChange={(e) =>
                  handleInputChange(index, "epLink", e.target.value)
                }
                style={{
                  flex: 1,
                  marginRight: "10px",
                  padding: "5px",
                  borderRadius: "5px",
                  border: "1px solid black",
                  fontSize: "15px",
                  height: "35px",
                }}
              />

              <button
                type="button"
                onClick={() => handleEdit(ep)}
                style={{
                  marginRight: "10px",
                  height: "35px",
                  borderRadius: "5px",
                  width: "60px",
                  fontSize: "15px",
                  border: "none",
                  backgroundColor: "green",
                  color: "white",
                }}
              >
                Update
              </button>

              <button
                type="button"
                onClick={() => handleDelete(index)}
                style={{
                  marginRight: "10px",
                  height: "35px",
                  borderRadius: "5px",
                  width: "60px",
                  fontSize: "15px",
                  border: "none",
                  backgroundColor: "red",
                  color: "white",
                }}
              >
                Delete
              </button>
            </div>
          ))}
        </div>
      </div>
    </DashboardLayout>
  );
}

export default Episodes;
