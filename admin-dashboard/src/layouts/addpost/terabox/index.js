/* eslint-disable */
// react and useEffect
import React, { useEffect, useState } from "react";
// @mui material components
import Grid from "@mui/material/Grid";
import SearchBar from "material-ui-search-bar";
// Material Dashboard 2 React components
import { useTheme } from "@material-ui/core/styles";
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
// Material Dashboard 2 React example components
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import Footer from "examples/Footer";
import SearchIcon from "@mui/icons-material/Search";
import DefaultProjectCard from "examples/Cards/ProjectCards/DefaultProjectCard";
import { ToastContainer, toast } from "react-toastify";
// Overview page components
import Header from "./components/Header";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import { useSelector, useDispatch } from "react-redux";
import { fetchPost } from "../../redux/postslice";
import { Link, useParams, useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { useCookies } from "react-cookie";
import Select from "react-select";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";

function notsupported() {
  const location = useLocation();
  const [isShow, setIsShow] = useState(true);
  const { posts } = useSelector((state) => state);
  const [finaldata, setfinaldata] = useState(null);
  const [sortoptionvalue, setSortoptionvalue] = useState({
    label: "Date Modefied",
    value: "date",
  });
  const [pagereload, setpagereload] = useState(false);
  const [backpage, setbackpage] = useState(1);
  const [page, setpage] = useState(backpage);
  let [searchParams, setSearchParams] = useSearchParams({ page: 1 });
  // useEffect(()=>{

  //   setSearchParams({page: searchParams.get("page") ? parseInt(searchParams.get("page")) : 1})
  // },[])
  const [cookie, getcookie] = useCookies();
  const token = cookie.admin;
  const [searchkey, setsearchkey] = useState(null);
  const [isdeleted, setisdeleted] = useState(false);
  const dispatch = useDispatch();
  //  useEffect(()=>{
  //   setpage(useParams().page)
  //  },[useParams().page])

  const form = new FormData();
  form.append("sort", sortoptionvalue.value);
  form.append("key", searchkey);

  const notsupported = async () => {
    try {
      console.log("api start calling");
      const response = await fetch("http://localhost:8010/testdata", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });
      if (!response.ok) {
        setfinaldata([]);
        throw new Error("Network response was not ok");
      }
      const jsonData = await response.json();
      console.log("api terabox response success", JSON.stringify(jsonData));
      if (jsonData.result === 0) {
        setfinaldata([]);
      } else {
        console.log("terabox result", JSON.stringify(jsonData.resultsWithStatus));
        setfinaldata(jsonData.resultsWithStatus);
      }
    } catch (error) {
      setfinaldata([]);
      console.error("Error fetching data:", error);
    }
  };

  // useEffect(() => {
  //   notsupported();
  // }, []);

  //  useEffect(()=>{
  //   setfinaldata(posts?.posts?.result)
  //  },[])

  const sortoption = [
    { label: "Date Modefied", value: "date" },
    { label: "Title", value: "title" },
    { label: "Name", value: "name" },
  ];

  const prev = () => {
    if (parseInt(searchParams.get("page")) > 1) {
      setSearchParams({ page: parseInt(searchParams.get("page")) - 1 });
    }
  };

  const next = () => {
    if (posts?.posts?.lastpage != true) {
      setSearchParams({ page: parseInt(searchParams.get("page")) + 1 });
    }
  };
  const pages = Math.ceil(posts?.posts?.records / 8);
  const nopages = [];
  for (let i = 1; i <= pages; i++) {
    nopages.push(i);
  }
  let currentpages = [];
  if (parseInt(searchParams.get("page")) > 10) {
    let s = (Math.ceil(parseFloat(searchParams.get("page") / 10)) - 1) * 10;
    currentpages = [...nopages.splice(s, 10)];
  } else {
    currentpages = [...nopages.splice(0, 10)];
  }

  let sortedarray;
  const sorthandle = (e) => {
    setSortoptionvalue(e);
  };

  const canclelogout = () => {
    window.history.back();
  };

  const logout = () => {
    setIsShow(false);
    notsupported();
  };

  const searchHanlde = (e) => {
    e.preventDefault();
    if (e.target.value) {
      setSearchParams({ page: 1 });

      setsearchkey(e.target.value);
    } else {
      setsearchkey(null);
    }
    // if (e.length > 0 && e.length < 4) {
    //   setSearchParams({ page: 1 });
    // }

    // if (e.length > 0) {
    //   setsearchkey(e);
    // } else {
    //   setsearchkey(null);
    // }
  };

  // const crawlpost = async() =>{
  //   const fetchdata = await fetch(`${window.path}/AdminAutoDeletePost`,{
  //     method:"POST",
  //     headers:{
  //       auth:token
  //     }
  //   })
  //   const response = await fetchPost.json()
  //   if(response.status == 1){
  //     alert(response.data)
  //   }else{
  //     alert("cant delete")
  //   }
  // }
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox mb={2} />
      {isShow ? (
        <div>
          <div className="modalBackground">
            <div className="modalContainer">
              <div className="titleCloseBtn">
                <button onClick={() => canclelogout()}>X</button>
              </div>
              <div className="title">
                <h5>Are You Sure You Want to Show Data?</h5>
              </div>
              <div className="footer">
                <button id="cancelBtn" onClick={() => canclelogout()}>
                  No
                </button>
                <button onClick={() => logout()}>Yes</button>
              </div>
            </div>
          </div>
          <ToastContainer />
        </div>
      ) : (
        <Header>
          <MDBox pt={2} px={2} lineHeight={1.5} display={isMobile ? "block" : "flex"}>
            <MDTypography variant="h6" fontWeight="medium">
              Not Supported Posts
            </MDTypography>
          </MDBox>
          <MDBox
            width="100%"
            display={isMobile ? "block" : "flex"}
            flexDirection={isMobile ? "column" : "row"}
            align=""
            lineHeight={1.25}
          ></MDBox>

          <MDBox p={2}>
            <Grid container spacing={3}>
              {finaldata == null ? (
                <MDBox align="center" width="100%" mt={10}>
                  <MDTypography
                    component="a"
                    href="#"
                    variant="button"
                    color="text"
                    fontWeight="medium"
                  >
                    Loading...
                  </MDTypography>
                </MDBox>
              ) : finaldata.length == 0 ? (
                <MDBox align="center" width="100%" mt={10}>
                  <MDTypography
                    component="a"
                    href="#"
                    variant="button"
                    color="text"
                    fontWeight="medium"
                  >
                    No Data Found{" "}
                  </MDTypography>
                </MDBox>
              ) : (
                finaldata?.map((e) => {
                  return (
                    <Grid
                      item
                      xs={12}
                      md={6}
                      xl={3}
                      mt={-2}
                      key={e.post_id}
                      overflow={isMobile ? "auto" : ""}
                    >
                      <DefaultProjectCard
                        post_id={e?.post_id}
                        post={e.post != null ? e.post : e.thumbnail}
                        label={e?.user_name}
                        title={e?.title}
                        description={e?.caption}
                        trailer={e?.trailer}
                        poster={e?.thumbnail}
                        genre={e?.genre}
                        userid={e?.user_id}
                        page={parseInt(searchParams.get("page"))}
                        isdeleted={setisdeleted}
                        action={{
                          type: "internal",
                          route: `/posts/indpost/${e.post_id}`,
                          color: "info",
                          label: "view detail",
                        }}
                      />
                    </Grid>
                  );
                })
              )}
            </Grid>
          </MDBox>
        </Header>
      )}
      <Footer />
    </DashboardLayout>
  );
}

export default notsupported;
