import { createSlice,createAsyncThunk } from "@reduxjs/toolkit";

const initialState = {
    loading :true,
    posts :[],
    error:''
}
export  const fetchPost =createAsyncThunk('post/fetchPost',async({page,token,form})=>{
    
    const res = await   fetch(`${window.path}/adminshowpost/${page}`,{
        method:"POST",
        headers:{
            auth:token
        },
        body:form
            
        

    })
    const data = await res.json()
    return data;
})

const PostSlice = createSlice({
    name:'post',
    initialState,
    extraReducers:(builder)=>{
        builder.addCase(fetchPost.pending,(state)=>{
            state.loading = true;
        })
        builder.addCase(fetchPost.fulfilled,(state,action)=>{
            state.loading = false;
            state.posts = action.payload
            state.error = ''
        })
        builder.addCase(fetchPost.rejected,(state,action)=>{
            state.loading = false;
            state.posts = ''
            state.error = action.error.message
        })
    }
})

export default PostSlice.reducer