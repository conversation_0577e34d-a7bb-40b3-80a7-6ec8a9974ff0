/* eslint-disable */
// @mui material components
import Card from "@mui/material/Card";
import React,{ useEffect } from 'react'
// Material Dashboard 2 React components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import MDButton from "components/MDButton"
import "bootstrap/dist/css/bootstrap.min.css"
// Billing page components
import Single_report from "layouts/reports/components/View";
import { useSelector, useDispatch } from "react-redux";
import { fetchReports } from '../../../../redux/reportslice';
import {useCookies} from "react-cookie"
import { useState } from "react";
function BillingInformation() {
  const [data,setdata] = useState([])
  const [cookie,setcookie] = useCookies()
  const token = cookie.user
    const fetchReports = async()=>{
      const report = await fetch(`${window.path}/adminshowreportcount`,{
        method:"GET",
        headers:{
          auth:`bearer ${token}`
        }
      })
      const res = await report.json()
      console.log(res)
      setdata(res)
    }
    console.log(data)
    const prev = ()=>{
      if(page>1){
        setpage(page-1);
        console.log(page)
      }
      
    }
    useEffect(()=>{
      fetchReports({page,token})
    },[])
    const next = ()=>{
      
      if(posts.lastpage===false){
        setpage(page+1);
        console.log(page)
      }
    }
  return (
    <Card id="delete-account">
      <MDBox pt={3} px={2}>
        <MDTypography variant="h6" fontWeight="medium">
          reported post
        </MDTypography>
      </MDBox>
      <MDBox pt={1} pb={2} px={2}>

        <MDBox component="ul"  p={0} m={0}>
        <div className="row">

          {data?.result?.map((e)=>{
            return(
              <div className="col-lg-4 col-sm-12">
              <Single_report
              title={e.title}
              post={`http://localhost:8000/upload/post/${e.post}`}
              caption={e.caption}
              report = {e.report_count}
              date={e.created_date}
              views = {e.views}
              count={e.report_count}
              id={e.id}
              uid={e.source_id}
              />
              </div>
              )
            })}
            </div>
        </MDBox>
        <MDBox display="flex" flexDirection="space-around" align="center" mt={10}>
                <MDBox width="50%"  >

               

                  <MDButton
                  onClick={prev}
                  variant="contained"
                  size="medium"
                  color="error"
                  >
                    Previous
                  </MDButton>
                  </MDBox >
                  <MDBox width="50%" >
                  <MDButton
                    onClick={next}
                    
                      variant="contained"
                      size="medium"
                      color="error">
                          next
                    </MDButton>
                    </MDBox>
                   
                  </MDBox>
      </MDBox>
    </Card>
  );
}

export default BillingInformation;
