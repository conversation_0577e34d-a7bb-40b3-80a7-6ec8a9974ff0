/* eslint-disable */
import React from "react";
import "./Modal.css";

function Modal({ setOpenModal, setdelete }) {
  return (
    <div className="modalBackground">
      <div className="modalContainer">
        <div className="titleCloseBtn">
          <button
            onClick={() => {
              setOpenModal(false);
            }}
          >
            X
          </button>
        </div>
        <div className="title">
          <h5>Are You Sure You Want to Unblock User?</h5>
        </div>
        <div className="footer">
          <button
            onClick={() => {
              setOpenModal(false);
            }}
            id="cancelBtn"
          >
            No
          </button>
          <button
            onClick={() => {
              setdelete(true);
              setOpenModal(false);
            }}
          >
            Yes
          </button>
        </div>
      </div>
    </div>
  );
}

export default Modal;
