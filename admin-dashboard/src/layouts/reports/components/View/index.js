/* eslint-disable */
// prop-types is a library for typechecking of props
import PropTypes from "prop-types";
import { useMaterialUIController } from "context";
<><link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" /><link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500" rel="stylesheet" /></>
import "./yoyo.css"
function Single_report({id,uid, title, post,report,views, date, caption,noGutter}) {
  const token = sessionStorage.getItem('auth')
  const [controller] = useMaterialUIController();
  const { darkMode } = controller;
  const del =  async()=>{
    const form =  new FormData()
    form.append('id',uid)
    form.append('dest',id)
    
      const deletepost = await fetch("http://localhost:8000/admindeletepost",{
          method:"DELETE",
          headers:{
            auth:`bearer ${token}`
          },
          body:form
        })
        const res  = await  deletepost.json()
        if(res.status === 201){
          alert("deleted successfully")
           window.location.reload(false)
        }
        else{
          alert("can't delete")
        }
    }
    
  return (
    <>
    
    <div class="row">
    <div class="col-md-4 col-sm-12 col-lg-4">
        <div class="card card__dark card__dark--magenta">
            <div class="media media--16-9" onClick={()=>{window.open(post,'blank')}}>
            {post.slice(post.length-3,post.length)==="mp4"?
            <video width="350" height="620">
              <source src={post} type="video/mp4"></source>
            </video>
            :
                  <img src={post} alt="" width="640" height="500"/>
          }
                
            </div>
            <div class="primary-title">
                <div class="primary-text">{title}</div>
                <div className="pt-1">

                <div class="supporting-text">caption&nbsp;:&nbsp;&nbsp;{caption}</div>
                <div class="supporting-text">
                views&nbsp;:&nbsp;&nbsp;{views}
                </div>
                <div class="supporting-text">
                reports&nbsp;:&nbsp;&nbsp;{report}
                </div>
                <div class="supporting-text">
                upload date&nbsp;:&nbsp;&nbsp;{date.slice(0,10)}
                </div>
                </div>
                
                </div>

                <div class="actions border-top">
                    <div class="action-buttons float-right">
                        <button class="button" type="button">View Details</button>
                    </div>
                    <div class="action-icons"> <i class="material-icons action-icon" role="button" title="Share"></i> <i class="material-icons action-icon" role="button" title="More options">spa</i> <i class="material-icons action-icon" role="button" title="More options">pool</i></div>
                </div>
            </div>
        </div>
        </div>
    </>

  );
}

// Setting default values for the props of Bill
Single_report.defaultProps = {
  noGutter: false,
};

// Typechecking props for the Bill
Single_report.propTypes = {
  name: PropTypes.string.isRequired,
  dest: PropTypes.string,
  source: PropTypes.string.isRequired,
  post: PropTypes.string,
  source: PropTypes.string.isRequired,
  date: PropTypes.string.isRequired,
  count: PropTypes.string.isRequired,
};

export default Single_report;
