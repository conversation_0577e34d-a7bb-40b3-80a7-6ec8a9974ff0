import { configureStore } from '@reduxjs/toolkit'
import reportedUserreducer from './redux/reported_user';
import userReducer from './redux/userslice'
import reportslice from './redux/reportslice';
import postslice from './redux/postslice';

 const store = configureStore({
    reducer:{
        users:userReducer ,
        posts:postslice,
        reports:reportslice,
        reportedUser : reportedUserreducer,
        }
})

export default store;