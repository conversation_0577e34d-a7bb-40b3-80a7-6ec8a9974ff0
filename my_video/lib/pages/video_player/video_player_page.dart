import 'package:my_video/app_imports.dart';

class VideoPlayerPage extends StatefulWidget {
  final MovieModel movie;

  const VideoPlayerPage({super.key, required this.movie});

  @override
  State<VideoPlayerPage> createState() => VideoPlayerPageState();
}

class VideoPlayerPageState extends State<VideoPlayerPage> {
  VideoPlayerPageHelper? _videoPlayerPageHelper;
  late VideoPlayerController videoPlayerController;

  @override
  Widget build(BuildContext context) {
    MySize.init(context);
    _videoPlayerPageHelper =
        _videoPlayerPageHelper ?? VideoPlayerPageHelper(this, widget.movie);

    return GetBuilder(
      init: VideoPlayerController(),
      builder: (VideoPlayerController controller) {
        videoPlayerController = controller;
        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) {
            if (!didPop) {
              _videoPlayerPageHelper?.dispose();
              context.go(AppRoutes.mainNavigation);
            }
          },
          child: Scaffold(
            backgroundColor: AppColorConstants.backgroundColor,
            appBar: _videoPlayerPageHelper!.isFullScreen
                ? null
                : AppBar(
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    leading: IconButton(
                      icon: const Icon(
                        Icons.arrow_back,
                        color: AppColorConstants.textPrimary,
                      ),
                      onPressed: () {
                        _videoPlayerPageHelper?.dispose();
                        context.go(AppRoutes.mainNavigation);
                      },
                    ),
                  ),
            body: _videoPlayerPageHelper!.isFullScreen
                ? _buildFullScreenPlayer()
                : Column(
                    children: [
                      // Video Player Section
                      _buildVideoPlayer(),

                      // Content Section
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Movie Info
                              _buildMovieInfo(),

                              // Action Buttons
                              _buildActionButtons(),

                              // Related Videos
                              _buildRelatedVideos(),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _videoPlayerPageHelper?.dispose();
    super.dispose();
  }

  Widget _buildFullScreenPlayer() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black,
      child: Stack(
        children: [
          // YouTube Player
          if (_videoPlayerPageHelper!.youtubeController != null)
            Center(
              child: YoutubePlayer(
                controller: _videoPlayerPageHelper!.youtubeController!,
                aspectRatio: 16 / 9,
              ),
            ),

          // Loading indicator
          if (_videoPlayerPageHelper!.youtubeController == null)
            const Center(
              child: CircularProgressIndicator(
                color: AppColorConstants.primaryColor,
              ),
            ),

          // Exit fullscreen button
          Positioned(
            top: MySize.height(40),
            left: MySize.width(16),
            child: GestureDetector(
              onTap: _videoPlayerPageHelper!.toggleFullScreen,
              child: Container(
                padding: EdgeInsets.all(MySize.height(8)),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.fullscreen_exit,
                  color: AppColorConstants.textPrimary,
                  size: MySize.height(24),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoPlayer() {
    return Container(
      width: double.infinity,
      height: _videoPlayerPageHelper!.isFullScreen
          ? MediaQuery.of(context).size.height
          : MySize.height(220),
      color: Colors.black,
      child: Stack(
        children: [
          // YouTube Player
          if (_videoPlayerPageHelper!.youtubeController != null)
            YoutubePlayer(
              controller: _videoPlayerPageHelper!.youtubeController!,
              aspectRatio: 16 / 9,
            ),

          // Loading indicator
          if (_videoPlayerPageHelper!.youtubeController == null)
            const Center(
              child: CircularProgressIndicator(
                color: AppColorConstants.primaryColor,
              ),
            ),

          // Fullscreen Toggle
          if (!_videoPlayerPageHelper!.isFullScreen)
            Positioned(
              top: MySize.height(8),
              right: MySize.width(8),
              child: GestureDetector(
                onTap: _videoPlayerPageHelper!.toggleFullScreen,
                child: Container(
                  padding: EdgeInsets.all(MySize.height(8)),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.6),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.fullscreen,
                    color: AppColorConstants.textPrimary,
                    size: MySize.height(20),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMovieInfo() {
    return Padding(
      padding: EdgeInsets.all(MySize.width(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppText(
            text: widget.movie.title,
            fontSize: MySize.fontSize(20),
            fontWeight: FontWeight.bold,
            color: AppColorConstants.textPrimary,
          ),
          Space.height(8),
          Row(
            children: [
              if (widget.movie.rating != null) ...[
                Icon(Icons.star, size: MySize.height(16), color: Colors.amber),
                Space.width(4),
                AppText(
                  text: widget.movie.formattedRating,
                  fontSize: MySize.fontSize(14),
                  color: AppColorConstants.textSecondary,
                ),
                Space.width(16),
              ],
              if (widget.movie.duration != null) ...[
                Icon(
                  Icons.access_time,
                  size: MySize.height(16),
                  color: AppColorConstants.textSecondary,
                ),
                Space.width(4),
                AppText(
                  text: widget.movie.formattedDuration,
                  fontSize: MySize.fontSize(14),
                  color: AppColorConstants.textSecondary,
                ),
                Space.width(16),
              ],
              if (widget.movie.releaseYear != null) ...[
                AppText(
                  text: widget.movie.releaseYear.toString(),
                  fontSize: MySize.fontSize(14),
                  color: AppColorConstants.textSecondary,
                ),
              ],
            ],
          ),
          if (widget.movie.genre != null && widget.movie.genre!.isNotEmpty) ...[
            Space.height(8),
            AppText(
              text: widget.movie.genreString,
              fontSize: MySize.fontSize(14),
              color: AppColorConstants.primaryColor,
            ),
          ],
          if (widget.movie.description != null) ...[
            Space.height(12),
            AppText(
              text: widget.movie.description!,
              fontSize: MySize.fontSize(14),
              color: AppColorConstants.textSecondary,
              maxLines: _videoPlayerPageHelper!.isDescriptionExpanded
                  ? null
                  : 3,
              overflow: _videoPlayerPageHelper!.isDescriptionExpanded
                  ? null
                  : TextOverflow.ellipsis,
            ),
            Space.height(8),
            GestureDetector(
              onTap: _videoPlayerPageHelper!.toggleDescription,
              child: AppText(
                text: _videoPlayerPageHelper!.isDescriptionExpanded
                    ? 'Show Less'
                    : 'Show More',
                fontSize: MySize.fontSize(14),
                color: AppColorConstants.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
      child: Row(
        children: [
          Expanded(
            child: AppButton(
              text: _videoPlayerPageHelper!.isInPlaylist
                  ? 'Remove from Playlist'
                  : 'Add to Playlist',
              onPressed: _videoPlayerPageHelper!.togglePlaylist,
              backgroundColor: _videoPlayerPageHelper!.isInPlaylist
                  ? AppColorConstants.colorRed
                  : AppColorConstants.primaryColor,
              icon: Icon(
                _videoPlayerPageHelper!.isInPlaylist ? Icons.remove : Icons.add,
                color: AppColorConstants.textPrimary,
              ),
            ),
          ),
          Space.width(12),
          AppIconButton(
            icon: Icons.share,
            onPressed: _videoPlayerPageHelper!.shareMovie,
            backgroundColor: AppColorConstants.cardColor,
            color: AppColorConstants.textPrimary,
          ),
        ],
      ),
    );
  }

  Widget _buildRelatedVideos() {
    if (_videoPlayerPageHelper!.relatedMovies.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Space.height(24),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
          child: AppText(
            text: 'Related Videos',
            fontSize: MySize.fontSize(18),
            fontWeight: FontWeight.bold,
            color: AppColorConstants.textPrimary,
          ),
        ),
        Space.height(12),
        SizedBox(
          height: MySize.height(240),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
            itemCount: _videoPlayerPageHelper!.relatedMovies.length,
            itemBuilder: (context, index) {
              final relatedMovie = _videoPlayerPageHelper!.relatedMovies[index];
              return MovieCard(
                movie: relatedMovie,
                onTap: () => _videoPlayerPageHelper!.playRelatedMovie(
                  relatedMovie,
                  context,
                ),
                showTitle: true,
                showRating: true,
                showDuration: true,
              );
            },
          ),
        ),
        Space.height(100), // Bottom padding
      ],
    );
  }
}
