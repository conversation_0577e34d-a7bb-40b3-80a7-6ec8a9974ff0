/* eslint-disable */
import React, { useEffect } from "react";
// @mui material components
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";
// Material Dashboard 2 React components
import profile from "../../assets/images/profile/porfile.png";
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import MDAvatar from "components/MDAvatar";
import MDButton from "components/MDButton";
import SearchIcon from "@mui/icons-material/Search";
// Material Dashboard 2 React example components
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import Modal from "examples/Cards/ProjectCards/DefaultProjectCard/Modal3";
import Footer from "examples/Footer";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DataTable from "examples/Tables/DataTable";
import { useSelector, useDispatch } from "react-redux";
import { fetchUsers } from "../../redux/userslice";
import { useState } from "react";
import { useNavigate, Link, useSearchParams } from "react-router-dom";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import SearchBar from "material-ui-search-bar";
import moment from "moment/moment";
import { useCookies } from "react-cookie";
import Select from "react-select";
import { Button, Typography } from "@mui/material";
import Cover from "layouts/authentication/sign-up";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";

import useMediaQuery from "@material-ui/core/useMediaQuery";
import { useTheme } from "@material-ui/core/styles";

function Index() {
  const [page, setPage] = useState(1);
  const [userdata, setUserData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState(null);
  const [unblockemail, setUnblockEmail] = useState(null);
  const [isOpen, setIsOpen] = useState();
  const [deletee, setdeletee] = useState(false);
  const [total, settotal] = useState(0);
  const Unblockuser = async () => {
    try {
      const jsonbody = {
        email: unblockemail,
      };
      setdeletee(false);
      const res = await fetch(`${window.path}/unblockbyadmin`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Auth: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwiaWF0IjoxNzI0ODI3MTI4fQ.1em7IezVmGV8TZ_okAzY_Ox0op7a9JTD9fgX0UcuZ4s",
        },
        body: JSON.stringify(jsonbody),
      });
      if (res.status == 200) {
        toast.success("User Unblocked Successfully", {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 1500,
        });
        getBlockedUser();
        setUnblockEmail("");
        setdeletee(false);
      } else if (res.status == 404) {
        toast.error("User Not Found", {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 1500,
        });
        setUnblockEmail("");
        setdeletee(false);
      } else {
        toast.error("Something Went Wrong", {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 1500,
        });
        setUnblockEmail("");
        setdeletee(false);
      }
    } catch (err) {
      toast.error("Something Went Wrong", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 1500,
      });
      setUnblockEmail("");
      setdeletee(false);
    }
  };

  if (deletee == true) {
    Unblockuser();
  }

  const getBlockedUser = async () => {
    try {
      setLoading(true);
      const res = await fetch(
        `${window.path}/getlistbockedbyadmin?page=${page}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Auth: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwiaWF0IjoxNzI0ODI3MTI4fQ.1em7IezVmGV8TZ_okAzY_Ox0op7a9JTD9fgX0UcuZ4s",
          },
        }
      );
      const result = await res.json();
      if (result.status !== 1) {
        settotal(0);
        setUserData([]);
      } else {
        console.log("result.result", result.result);
        setUserData(result.result);
        settotal(result.total);
      }
    } catch (err) {
      setUserData([]);
      settotal(0);

      toast.error("Something Went Wrong !", {
        position: toast.POSITION.TOP_CENTER,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getBlockedUser();
  }, [page]);
  const searchHanlde = (e) => {
    e.preventDefault();
    if (e.target.value) {
      setEmail(e.target.value);
    } else {
      setEmail(null);
    }
  };

  const prev = () => {
    if (page > 1) {
      setPage(page - 1);
    }
  };

  const next = () => {
    //  if (posts?.posts?.lastpage != true) {
    setPage(page + 1);
    //  }
  };

  const pages = Math.ceil(total / 10);
  const nopages = [];
  for (let i = 1; i <= pages; i++) {
    nopages.push(i);
  }
  let currentpages = [];
  if (parseInt(page) > 10) {
    let s = (Math.ceil(parseFloat(page / 10)) - 1) * 10;
    currentpages = [...nopages.splice(s, 10)];
  } else {
    currentpages = [...nopages.splice(0, 10)];
  }
  const blockuser = async () => {
    try {
      if (email == null || email == "" || email == " ") {
        return toast.error("Plese Enter Valid Email", {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 1500,
        });
      }
      const jsonbody = {
        email: email,
      };
      const res = await fetch(`${window.path}/blockbyadmin`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Auth: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwiaWF0IjoxNzI0ODI3MTI4fQ.1em7IezVmGV8TZ_okAzY_Ox0op7a9JTD9fgX0UcuZ4s",
        },
        body: JSON.stringify(jsonbody),
      });
      if (res.status == 200) {
        getBlockedUser();
        setEmail("");
        return toast.success("User Blocked Successfully", {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 1500,
        });
      } else if (res.status == 404) {
        setEmail("");
        return toast.error("User Not Found", {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 1500,
        });
      } else {
        setEmail("");
        return toast.error("Something Went Wrong", {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 1500,
        });
      }
    } catch (err) {
      setEmail("");
      return toast.error("Something Went Wrong", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 1500,
      });
    }
  };

  useEffect(() => {
    getBlockedUser();
  }, []); // Dependency array ensures the function runs on mount and when 'page' changes

  const Author = ({ image, name, email }) => {
    return (
      <MDBox display="flex" alignItems="center" lineHeight={1}>
        <MDAvatar
          src={image}
          name={name}
          size="lg"
          style={{ objectFit: "fill" }}
        />
        <MDBox ml={2} lineHeight={1}>
          <MDTypography display="block" variant="button" fontWeight="medium">
            {name}
          </MDTypography>
          <MDTypography variant="caption">{email}</MDTypography>
        </MDBox>
      </MDBox>
    );
  };

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <DashboardLayout>
      <ToastContainer />
      <DashboardNavbar />
      <MDBox pt={-4} pb={3}>
        {isOpen && <Modal setOpenModal={setIsOpen} setdelete={setdeletee} />}
        <Grid container spacing={0}>
          <MDBox
            pt={0}
            px={2}
            lineHeight={1.5}
            style={{ width: "100%" }}
            display={isMobile ? "block" : "flex"}
          >
            <MDBox
              style={{
                overflow: "auto",
                alignItems: isMobile ? "" : "center",
                justifyContent: isMobile ? "" : "center",
                width: "50%",
                display: "flex",
                flexDirection: "column",
                marginTop: "0px",
                marginLeft: "300px",
              }}
            >
              <table
                style={{
                  height: "15px",
                  border: "1px solid #c7c7c7",
                  overflowX: "auto",
                }}
              >
                <thead style={{ display: "block" }}>
                  <tr style={{ display: "flex", width: "100%" }}>
                    <td
                      scope="col"
                      style={{
                        width: "40px",
                        textAlign: "center",
                        borderRight: "1px solid #b1b5b2",
                        cursor: "pointer",
                        color: "#646669",
                      }}
                      onClick={() => {
                        parseInt(page - 10) >= 1
                          ? setPage(Math.floor(parseInt(page) / 10) * 10 - 9)
                          : "";
                      }}
                    >
                      <KeyboardArrowLeftIcon style={{ marginRight: "-10px" }} />
                      <KeyboardArrowLeftIcon />
                    </td>
                    <td
                      scope="col"
                      style={{
                        width: "40px",
                        textAlign: "center",
                        cursor: "pointer",
                        color: "#646669",
                      }}
                      onClick={prev}
                    >
                      <KeyboardArrowLeftIcon />
                    </td>
                    {currentpages[0] > 1 ? (
                      <>
                        <td
                          style={{
                            width: "40px",
                            textAlign: "center",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            borderLeft: "1px solid #b1b5b2",
                            cursor: "pointer",
                            fontSize: "17px",
                            color: "#646669",
                          }}
                          onClick={() => {
                            setPage(1);
                          }}
                        >
                          1
                        </td>
                        <td
                          style={{
                            width: "40px",
                            textAlign: "center",
                            borderLeft: "1px solid #b1b5b2",
                            cursor: "pointer",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            fontSize: "17px",
                            color: "#646669",
                          }}
                        >
                          ...
                        </td>
                      </>
                    ) : (
                      ""
                    )}

                    {currentpages?.map((e) => {
                      return (
                        <td
                          style={{
                            borderLeft: "1px solid #b1b5b2",
                            width: "40px",
                            textAlign: "center",
                            cursor: "pointer",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            fontSize: "17px",
                            backgroundColor: `${
                              e == parseInt(page) ? "#3993EE" : ""
                            }`,
                          }}
                          onClick={() => {
                            setPage(e);
                          }}
                        >
                          {" "}
                          <span>
                            {" "}
                            <Link
                              to=""
                              style={{
                                position: "absolute",
                                color: `${
                                  e == parseInt(page) ? "white" : "#646669"
                                }`,
                              }}
                              className={`p-1   position-relative  text-underline-hover`}
                            >
                              {e}
                            </Link>
                          </span>
                        </td>
                      );
                    })}

                    {currentpages[currentpages.length - 1] < pages ? (
                      <>
                        <td
                          style={{
                            width: "40px",
                            textAlign: "center",
                            borderLeft: "1px solid #b1b5b2",
                            cursor: "pointer",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            fontSize: "17px",
                            color: "#646669",
                          }}
                        >
                          ...
                        </td>
                        <td
                          style={{
                            width: "40px",
                            textAlign: "center",
                            borderLeft: "1px solid #b1b5b2",
                            cursor: "pointer",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            fontSize: "17px",
                            color: "#646669",
                          }}
                          onClick={() => {
                            setPage(pages);
                          }}
                        >
                          {pages}
                        </td>
                      </>
                    ) : (
                      ""
                    )}

                    <td
                      style={{
                        width: "40px",
                        textAlign: "center",
                        borderLeft: "1px solid #b1b5b2",
                        cursor: "pointer",
                        color: "#646669",
                      }}
                      onClick={next}
                    >
                      {" "}
                      <NavigateNextIcon />{" "}
                    </td>
                    <td
                      style={{
                        width: "40px",
                        textAlign: "center",
                        borderLeft: "1px solid #b1b5b2",
                        cursor: "pointer",
                        color: "#646669",
                      }}
                      onClick={() => {
                        parseInt(page) + 10 <= pages
                          ? setPage(Math.ceil(parseInt(page) / 10) * 10 + 1)
                          : "";
                      }}
                    >
                      {" "}
                      <NavigateNextIcon style={{ marginRight: "-10px" }} />
                      <NavigateNextIcon />
                    </td>
                  </tr>
                </thead>
              </table>
            </MDBox>
            <MDBox mb={2} style={{ display: "flex", marginLeft: "200px" }}>
              <input
                style={{
                  paddingLeft: "10PX",
                  height: "40px",
                  borderRadius: "10px",
                  border: "1px solid #7b809a",
                  width: isMobile ? "95%" : "250px",
                  marginLeft: isMobile ? "10px" : "",
                  marginTop: isMobile ? "10px" : "0px",
                  marginRight: isMobile ? "" : "10px",
                  fontSize: "16.5px",
                }}
                value={email}
                onChange={(e) => {
                  searchHanlde(e);
                }}
                placeholder="Enter User Email"
              />
              <button
                style={{
                  width: "80px",
                  border: "1px solid black",
                  padding: "2.5px",
                  background: "red",
                  color: "white",
                  borderRadius: "5px",
                  marginRight: "10px",
                  fontSize: "16.5px",
                  marginTop: isMobile ? "10px" : "0px",
                }}
                onClick={() => blockuser()}
              >
                Submit
              </button>
            </MDBox>
          </MDBox>
          <Grid item xs={12}>
            {loading ? (
              <MDBox align="center" mt={10}>
                <MDTypography
                  component="a"
                  href="#"
                  variant="button"
                  color="text"
                  fontWeight="medium"
                >
                  Loading...
                </MDTypography>
              </MDBox>
            ) : (
              <DataTable
                table={{
                  columns: [
                    {
                      Header: "name",
                      accessor: "author",
                      width: "25%",
                      align: "left",
                    },
                    {
                      Header: "follower",
                      accessor: "follower",
                      align: "left",
                    },
                    {
                      Header: "following",
                      accessor: "following",
                      align: "left",
                    },
                    { Header: "post", accessor: "post", align: "left" },
                    {
                      Header: "join date",
                      accessor: "employed",
                      align: "center",
                    },
                    {
                      Header: "action",
                      accessor: "action",
                      align: "center",
                    },
                  ],
                  rows:
                    userdata?.map((e) => ({
                      follower: (
                        <MDTypography
                          component="a"
                          href="#"
                          variant="button"
                          color="text"
                          fontWeight="medium"
                        >
                          {e.follower_count}
                        </MDTypography>
                      ),
                      following: (
                        <MDTypography
                          component="a"
                          href="#"
                          variant="button"
                          color="text"
                          fontWeight="medium"
                        >
                          {e.following_count}
                        </MDTypography>
                      ),
                      post: (
                        <MDTypography
                          component="a"
                          href="#"
                          variant="button"
                          color="text"
                          fontWeight="medium"
                        >
                          {e.post_count}
                        </MDTypography>
                      ),
                      author: (
                        <Author
                          image={e.profile == null ? profile : e.profile}
                          name={`${e.fname} ${e.lname}`}
                          email={e.email}
                        />
                      ),
                      employed: (
                        <MDTypography
                          component="a"
                          href="#"
                          variant="caption"
                          color="text"
                          fontWeight="medium"
                        >
                          {e.created_date.slice(0, 10)}
                        </MDTypography>
                      ),
                      action: (
                        <MDBox ml={-1}>
                          <button
                            className="btn btn-sm btn-success"
                            onClick={() => {
                              setIsOpen(true);
                              setUnblockEmail(e.email);
                            }}
                          >
                            UNBLOCK
                          </button>
                        </MDBox>
                      ),
                    })) ?? [],
                }}
                isSorted={false}
                entriesPerPage={false}
                showTotalEntries={false}
              />
            )}
          </Grid>
        </Grid>
      </MDBox>
    </DashboardLayout>
  );
}

export default Index;
