/* eslint-disable */
import React from "react";
import "./Modal.css";

function Modal({ setIsUpdateOpen,setUpdate }) {
  
  return (
    <div className="modalBackground">
      <div className="modalContainer">
        <div className="titleCloseBtn">
          <button
            onClick={() => {
              setIsUpdateOpen(false);
            }}
          >
            X
          </button>
        </div>
        <div className="title">
          <h5>Are You Sure You Want to Update?</h5>
        </div>
        <div className="footer">
          <button
            onClick={() => {
              setIsUpdateOpen(false);
            }}
            id="cancelBtn"
          >
            Cancel
          </button>
          <button  onClick={() => {
              setUpdate(true);
              setIsUpdateOpen(false);
            }} >Update</button>
        </div>
      </div>
    </div>
  );
}

export default Modal;
