import 'package:my_video/app_imports.dart';

class SignupPageHelper {
  final SignupPageState _state;
  final Logger _logger = Logger();

  SignupPageHelper(this._state) {
    _initializeData();
  }

  void _initializeData() {
    _logger.i('Signup page initialized');
  }

  void showTermsOfService() {
    try {
      Get.context!.go(AppRoutes.termsOfService);
    } catch (e) {
      _logger.e('Error opening Terms of Service: $e');
    }
  }

  void showPrivacyPolicy() {
    try {
      Get.context!.go(AppRoutes.privacyPolicy);
    } catch (e) {
      _logger.e('Error opening Privacy Policy: $e');
    }
  }

  void navigateToLogin() {
    try {
      Get.context!.go(AppRoutes.login);
    } catch (e) {
      _logger.e('Error navigating to login: $e');
    }
  }

  void dispose() {
    _logger.i('Signup page helper disposed');
  }
}
