
> express@1.0.0 start
> nodemon app.js

[33m[nodemon] 2.0.20[39m
[33m[nodemon] to restart at any time, enter `rs`[39m
[33m[nodemon] watching path(s): *.*[39m
[33m[nodemon] watching extensions: js,mjs,json[39m
[32m[nodemon] starting `node app.js`[39m
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: EBADF: bad file descriptor, read
Emitted 'error' event on ReadStream instance at:
    at emitErrorNT (node:internal/streams/destroy:164:8)
    at errorOrDestroy (node:internal/streams/destroy:227:7)
    at node:internal/fs/streams:258:9
    at FSReqCallback.wrapper [as oncomplete] (node:fs:661:5) {
  errno: -9,
  code: 'EBADF',
  syscall: 'read'
}

Node.js v17.9.1
npm notice 
npm notice New major version of npm available! 8.11.0 -> 9.1.3
npm notice Changelog: <https://github.com/npm/cli/releases/tag/v9.1.3>
npm notice Run `npm install -g npm@9.1.3` to update!
npm notice 

> express@1.0.0 start
> nodemon app.js

[33m[nodemon] 2.0.20[39m
[33m[nodemon] to restart at any time, enter `rs`[39m
[33m[nodemon] watching path(s): *.*[39m
[33m[nodemon] watching extensions: js,mjs,json[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker58109910.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker58109910.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Tue Dec 06 2022 09:27:32 GMT+0000 (Coordinated Universal Time)',
  iat: 1670318852
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker989522307.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker989522307.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Tue Dec 06 2022 09:27:32 GMT+0000 (Coordinated Universal Time)',
  iat: 1670318852
}

> express@1.0.0 start
> nodemon app.js

[33m[nodemon] 2.0.20[39m
[33m[nodemon] to restart at any time, enter `rs`[39m
[33m[nodemon] watching path(s): *.*[39m
[33m[nodemon] watching extensions: js,mjs,json[39m
[32m[nodemon] starting `node app.js`[39m
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker988956900.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker988956900.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Tue Dec 06 2022 09:27:32 GMT+0000 (Coordinated Universal Time)',
  iat: 1670318852
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker593116184.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker593116184.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Tue Dec 06 2022 09:27:32 GMT+0000 (Coordinated Universal Time)',
  iat: 1670318852
}
/var/www/movie_site/node_modules/mysql/lib/protocol/Parser.js:437
      throw err; // Rethrow non-MySQL errors
      ^

Error: ER_DATA_TOO_LONG: Data too long for column 'fname' at row 1
    at Query.Sequence._packetToError (/var/www/movie_site/node_modules/mysql/lib/protocol/sequences/Sequence.js:47:14)
    at Query.ErrorPacket (/var/www/movie_site/node_modules/mysql/lib/protocol/sequences/Query.js:79:18)
    at Protocol._parsePacket (/var/www/movie_site/node_modules/mysql/lib/protocol/Protocol.js:291:23)
    at Parser._parsePacket (/var/www/movie_site/node_modules/mysql/lib/protocol/Parser.js:433:10)
    at Parser.write (/var/www/movie_site/node_modules/mysql/lib/protocol/Parser.js:43:10)
    at Protocol.write (/var/www/movie_site/node_modules/mysql/lib/protocol/Protocol.js:38:16)
    at Socket.<anonymous> (/var/www/movie_site/node_modules/mysql/lib/Connection.js:88:28)
    at Socket.<anonymous> (/var/www/movie_site/node_modules/mysql/lib/Connection.js:528:10)
    at Socket.emit (node:events:527:28)
    at addChunk (node:internal/streams/readable:324:12)
    --------------------
    at Protocol._enqueue (/var/www/movie_site/node_modules/mysql/lib/protocol/Protocol.js:144:48)
    at Connection.query (/var/www/movie_site/node_modules/mysql/lib/Connection.js:198:25)
    at /var/www/movie_site/routes/router.js:319:26
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at auth (/var/www/movie_site/middleware/oath.js:29:25)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at done (/var/www/movie_site/node_modules/multer/lib/make-middleware.js:45:7)
    at indicateDone (/var/www/movie_site/node_modules/multer/lib/make-middleware.js:49:68) {
  code: 'ER_DATA_TOO_LONG',
  errno: 1406,
  sqlMessage: "Data too long for column 'fname' at row 1",
  sqlState: '22001',
  index: 0,
  sql: `UPDATE user_tbl SET fname='\\"\\\\\\"\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"shubham\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\"\\\\\\"\\"',lname='\\"\\\\\\"\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"avasth\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\"\\\\\\"\\"',gender='\\"',profile='1670401204371-8020341be491-116184.jpg' WHERE id ='36'`
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
Habt5o0cDNWjc42y
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker860486217.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker860486217.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Tue Dec 06 2022 09:27:32 GMT+0000 (Coordinated Universal Time)',
  iat: 1670318852
}
/var/www/movie_site/node_modules/mysql/lib/protocol/Parser.js:437
      throw err; // Rethrow non-MySQL errors
      ^

Error: ER_DATA_TOO_LONG: Data too long for column 'fname' at row 1
    at Query.Sequence._packetToError (/var/www/movie_site/node_modules/mysql/lib/protocol/sequences/Sequence.js:47:14)
    at Query.ErrorPacket (/var/www/movie_site/node_modules/mysql/lib/protocol/sequences/Query.js:79:18)
    at Protocol._parsePacket (/var/www/movie_site/node_modules/mysql/lib/protocol/Protocol.js:291:23)
    at Parser._parsePacket (/var/www/movie_site/node_modules/mysql/lib/protocol/Parser.js:433:10)
    at Parser.write (/var/www/movie_site/node_modules/mysql/lib/protocol/Parser.js:43:10)
    at Protocol.write (/var/www/movie_site/node_modules/mysql/lib/protocol/Protocol.js:38:16)
    at Socket.<anonymous> (/var/www/movie_site/node_modules/mysql/lib/Connection.js:88:28)
    at Socket.<anonymous> (/var/www/movie_site/node_modules/mysql/lib/Connection.js:528:10)
    at Socket.emit (node:events:527:28)
    at addChunk (node:internal/streams/readable:324:12)
    --------------------
    at Protocol._enqueue (/var/www/movie_site/node_modules/mysql/lib/protocol/Protocol.js:144:48)
    at Connection.query (/var/www/movie_site/node_modules/mysql/lib/Connection.js:198:25)
    at /var/www/movie_site/routes/router.js:319:26
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at auth (/var/www/movie_site/middleware/oath.js:29:25)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at done (/var/www/movie_site/node_modules/multer/lib/make-middleware.js:45:7)
    at indicateDone (/var/www/movie_site/node_modules/multer/lib/make-middleware.js:49:68) {
  code: 'ER_DATA_TOO_LONG',
  errno: 1406,
  sqlMessage: "Data too long for column 'fname' at row 1",
  sqlState: '22001',
  index: 0,
  sql: `UPDATE user_tbl SET fname='\\"\\\\\\"\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"shubham\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\"\\\\\\"\\"',lname='\\"\\\\\\"\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"avasth\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\"\\\\\\"\\"',gender='\\"',profile='1670401506798-3448e950b010-486217.jpg' WHERE id ='36'`
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting child process[39m
[32m[nodemon] restarting child process[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
{
  id: 38,
  time: 'Mon Dec 05 2022 04:57:42 GMT+0000 (Coordinated Universal Time)',
  iat: 1670216262
}
[Object: null prototype] {
  fname: 'shubham',
  lname: 'avasth',
  gender: 'male'
}
undefined
Habt5o0cDNWjc42y
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker951426513.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker951426513.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Tue Dec 06 2022 09:27:32 GMT+0000 (Coordinated Universal Time)',
  iat: 1670318852
}
[Object: null prototype] {
  fname: '"shubham"',
  lname: '"avast"',
  gender: '"other"'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker951426513.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg',
  destination: 'uploads/profile',
  filename: '1670402036527-a770048cc487-426513.jpg',
  path: 'uploads/profile/1670402036527-a770048cc487-426513.jpg',
  size: 18054
}
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker176247604.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker176247604.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Tue Dec 06 2022 09:27:32 GMT+0000 (Coordinated Universal Time)',
  iat: 1670318852
}
[Object: null prototype] {
  fname: '"shubham"',
  lname: '"avast"',
  gender: '"other"'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker176247604.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg',
  destination: 'uploads/profile',
  filename: '1670402552950-77b95b0f8f4c-247604.jpg',
  path: 'uploads/profile/1670402552950-77b95b0f8f4c-247604.jpg',
  size: 18054
}
Habt5o0cDNWjc42y
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker664081577.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker664081577.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Tue Dec 06 2022 09:27:32 GMT+0000 (Coordinated Universal Time)',
  iat: 1670318852
}
[Object: null prototype] {
  fname: '"shubham"',
  lname: '"vast"',
  gender: '"other"'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker664081577.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg',
  destination: 'uploads/profile',
  filename: '1670402621870-1c17d90b7358-081577.jpg',
  path: 'uploads/profile/1670402621870-1c17d90b7358-081577.jpg',
  size: 5434
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker370843648.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker370843648.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Tue Dec 06 2022 09:27:32 GMT+0000 (Coordinated Universal Time)',
  iat: 1670318852
}
[Object: null prototype] {
  fname: '"\\"shubham\\""',
  lname: '"\\"vast\\""',
  gender: '"other"'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker370843648.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg',
  destination: 'uploads/profile',
  filename: '1670402649047-3c9088595276-843648.jpg',
  path: 'uploads/profile/1670402649047-3c9088595276-843648.jpg',
  size: 5434
}
Habt5o0cDNWjc42y
{
  id: 38,
  time: 'Mon Dec 05 2022 04:57:42 GMT+0000 (Coordinated Universal Time)',
  iat: 1670216262
}
[Object: null prototype] {
  fname: 'shubham',
  lname: 'avasth',
  gender: 'male'
}
undefined
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker169998992.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker169998992.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Tue Dec 06 2022 09:27:32 GMT+0000 (Coordinated Universal Time)',
  iat: 1670318852
}
[Object: null prototype] {
  fname: '"shubham"',
  lname: '"s"',
  gender: '"other"'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker169998992.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg',
  destination: 'uploads/profile',
  filename: '1670402747161-da1d26d49932-998992.jpg',
  path: 'uploads/profile/1670402747161-da1d26d49932-998992.jpg',
  size: 5434
}
Habt5o0cDNWjc42y
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker427170830.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker427170830.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Tue Dec 06 2022 09:27:32 GMT+0000 (Coordinated Universal Time)',
  iat: 1670318852
}
[Object: null prototype] {
  fname: '"shubham"',
  lname: '"s"',
  gender: '"other"'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker427170830.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg',
  destination: 'uploads/profile',
  filename: '1670402985346-b0660f6a8598-170830.jpg',
  path: 'uploads/profile/1670402985346-b0660f6a8598-170830.jpg',
  size: 18054
}
Habt5o0cDNWjc42y
{
  id: 38,
  time: 'Mon Dec 05 2022 04:57:42 GMT+0000 (Coordinated Universal Time)',
  iat: 1670216262
}
[Object: null prototype] {
  fname: 'shubham',
  lname: 'avasth',
  gender: 'male'
}
undefined
Habt5o0cDNWjc42y
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker993987536.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker993987536.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Tue Dec 06 2022 09:27:32 GMT+0000 (Coordinated Universal Time)',
  iat: 1670318852
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker993987536.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg',
  destination: 'uploads/profile',
  filename: '1670403350190-a8eeed90956e-987536.jpg',
  path: 'uploads/profile/1670403350190-a8eeed90956e-987536.jpg',
  size: 18054
}
Habt5o0cDNWjc42y

> express@1.0.0 start
> nodemon app.js

[33m[nodemon] 2.0.20[39m
[33m[nodemon] to restart at any time, enter `rs`[39m
[33m[nodemon] watching path(s): *.*[39m
[33m[nodemon] watching extensions: js,mjs,json[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
{
  id: 38,
  time: 'Mon Dec 05 2022 04:57:42 GMT+0000 (Coordinated Universal Time)',
  iat: 1670216262
}
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
[32m[nodemon] restarting child process[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
[
  {
    id: 39,
    fname: 'shubham',
    lname: 'avasthi',
    email: '<EMAIL>',
    gender: 'other',
    post_count: 0,
    follower_count: 0,
    following_count: 0,
    profile: 'http://**************:8010/upload/profile/1670403593222-86bf5069dc7a-ot (3).png',
    created_date: '2022-12-05T07:00:37.000Z',
    updated_date: '2022-12-07T08:59:53.000Z'
  }
]
{
  id: 39,
  time: 'Wed Dec 07 2022 10:01:23 GMT+0000 (Coordinated Universal Time)'
}
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker634401124.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker634401124.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Tue Dec 06 2022 09:27:32 GMT+0000 (Coordinated Universal Time)',
  iat: 1670318852
}
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker632283019.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker632283019.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Tue Dec 06 2022 09:27:32 GMT+0000 (Coordinated Universal Time)',
  iat: 1670318852
}
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker552460448.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker552460448.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Tue Dec 06 2022 09:27:32 GMT+0000 (Coordinated Universal Time)',
  iat: 1670318852
}
{
  id: 38,
  time: 'Sat Dec 03 2022 12:22:57 GMT+0000 (Coordinated Universal Time)',
  iat: 1670070177
}
38
true
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
{
  id: 38,
  time: 'Sat Dec 03 2022 12:22:57 GMT+0000 (Coordinated Universal Time)',
  iat: 1670070177
}
38
true
{
  id: 38,
  time: 'Sat Dec 03 2022 12:22:57 GMT+0000 (Coordinated Universal Time)',
  iat: 1670070177
}
38
true
{
  id: 38,
  time: 'Sat Dec 03 2022 12:22:57 GMT+0000 (Coordinated Universal Time)',
  iat: 1670070177
}
38
true

> express@1.0.0 start
> nodemon app.js

[33m[nodemon] 2.0.20[39m
[33m[nodemon] to restart at any time, enter `rs`[39m
[33m[nodemon] watching path(s): *.*[39m
[33m[nodemon] watching extensions: js,mjs,json[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
Habt5o0cDNWjc42y
{
  id: 38,
  time: 'Sat Dec 03 2022 12:22:57 GMT+0000 (Coordinated Universal Time)',
  iat: 1670070177
}
true
[32m[nodemon] restarting child process[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
{
  id: 38,
  time: 'Sat Dec 03 2022 12:22:57 GMT+0000 (Coordinated Universal Time)',
  iat: 1670070177
}
true
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
[
  {
    id: 36,
    fname: 'Deepak',
    lname: 'Ghodse',
    email: '<EMAIL>',
    gender: 'other',
    post_count: 0,
    follower_count: 0,
    following_count: 0,
    profile: 'http://**************:8010/upload/profile/1670415909856-66aa6b359b75-525056.jpg',
    created_date: '2022-12-03T11:08:58.000Z',
    updated_date: '2022-12-07T12:25:10.000Z'
  }
]
{
  id: 36,
  time: 'Thu Dec 08 2022 05:33:41 GMT+0000 (Coordinated Universal Time)'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker173196143.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker173196143.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Thu Dec 08 2022 05:33:41 GMT+0000 (Coordinated Universal Time)',
  iat: 1670477621
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker596816997.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker596816997.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Thu Dec 08 2022 05:33:41 GMT+0000 (Coordinated Universal Time)',
  iat: 1670477621
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker533772997.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker533772997.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Thu Dec 08 2022 05:33:41 GMT+0000 (Coordinated Universal Time)',
  iat: 1670477621
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker292283496.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker292283496.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Thu Dec 08 2022 05:33:41 GMT+0000 (Coordinated Universal Time)',
  iat: 1670477621
}
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
[
  {
    id: 36,
    fname: 'Deep',
    lname: 'John',
    email: '<EMAIL>',
    gender: 'other',
    post_count: 0,
    follower_count: 0,
    following_count: 0,
    profile: 'http://**************:8010/upload/profile/1670477700102-eb56558f884a-283496.jpg',
    created_date: '2022-12-03T11:08:58.000Z',
    updated_date: '2022-12-08T05:35:00.000Z'
  }
]
{
  id: 36,
  time: 'Thu Dec 08 2022 05:40:23 GMT+0000 (Coordinated Universal Time)'
}
{
  fieldname: 'profile',
  originalname: 'Screenshot (3).png',
  encoding: '7bit',
  mimetype: 'image/png'
}
{
  fieldname: 'profile',
  originalname: 'Screenshot (3).png',
  encoding: '7bit',
  mimetype: 'image/png'
}
{
  id: 39,
  time: 'Mon Dec 05 2022 11:26:57 GMT+0000 (Coordinated Universal Time)',
  iat: 1670239617
}
{
  fieldname: 'profile',
  originalname: 'Screenshot (3).png',
  encoding: '7bit',
  mimetype: 'image/png'
}
{
  fieldname: 'profile',
  originalname: 'Screenshot (3).png',
  encoding: '7bit',
  mimetype: 'image/png'
}
{
  id: 39,
  time: 'Mon Dec 05 2022 11:26:57 GMT+0000 (Coordinated Universal Time)',
  iat: 1670239617
}
{
  fieldname: 'profile',
  originalname: 'Screenshot (3).png',
  encoding: '7bit',
  mimetype: 'image/png'
}
{
  fieldname: 'profile',
  originalname: 'Screenshot (3).png',
  encoding: '7bit',
  mimetype: 'image/png'
}
{
  id: 39,
  time: 'Mon Dec 05 2022 11:26:57 GMT+0000 (Coordinated Universal Time)',
  iat: 1670239617
}
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
[
  {
    id: 36,
    fname: 'Deep',
    lname: 'John',
    email: '<EMAIL>',
    gender: 'other',
    post_count: 0,
    follower_count: 0,
    following_count: 0,
    profile: 'http://**************:8010/upload/profile/1670477700102-eb56558f884a-283496.jpg',
    created_date: '2022-12-03T11:08:58.000Z',
    updated_date: '2022-12-08T05:35:00.000Z'
  }
]
{
  id: 36,
  time: 'Thu Dec 08 2022 05:44:59 GMT+0000 (Coordinated Universal Time)'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker148171264.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker148171264.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  id: 36,
  time: 'Thu Dec 08 2022 05:44:59 GMT+0000 (Coordinated Universal Time)',
  iat: 1670478299
}
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
node:internal/modules/cjs/loader:936
  throw err;
  ^

Error: Cannot find module '../middleware/strictAuth'
Require stack:
- /var/www/movie_site/routes/router.js
- /var/www/movie_site/app.js
    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:933:15)
    at Function.Module._load (node:internal/modules/cjs/loader:778:27)
    at Module.require (node:internal/modules/cjs/loader:999:19)
    at require (node:internal/modules/cjs/helpers:102:18)
    at Object.<anonymous> (/var/www/movie_site/routes/router.js:15:20)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Module.require (node:internal/modules/cjs/loader:999:19) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/var/www/movie_site/routes/router.js',
    '/var/www/movie_site/app.js'
  ]
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
node:internal/modules/cjs/loader:936
  throw err;
  ^

Error: Cannot find module '../middleware/strictAuth'
Require stack:
- /var/www/movie_site/routes/router.js
- /var/www/movie_site/app.js
    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:933:15)
    at Function.Module._load (node:internal/modules/cjs/loader:778:27)
    at Module.require (node:internal/modules/cjs/loader:999:19)
    at require (node:internal/modules/cjs/helpers:102:18)
    at Object.<anonymous> (/var/www/movie_site/routes/router.js:15:20)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Module.require (node:internal/modules/cjs/loader:999:19) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/var/www/movie_site/routes/router.js',
    '/var/www/movie_site/app.js'
  ]
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
{
  fieldname: 'profile',
  originalname: 'Screenshot (3).png',
  encoding: '7bit',
  mimetype: 'image/png'
}
{
  fieldname: 'profile',
  originalname: 'Screenshot (3).png',
  encoding: '7bit',
  mimetype: 'image/png'
}
/var/www/movie_site/node_modules/mysql/lib/protocol/Parser.js:437
      throw err; // Rethrow non-MySQL errors
      ^

SyntaxError: Unexpected token u in JSON at position 0
    at JSON.parse (<anonymous>)
    at Query.<anonymous> (/var/www/movie_site/routes/router.js:327:37)
    at Query.<anonymous> (/var/www/movie_site/node_modules/mysql/lib/Connection.js:528:10)
    at Query._callback (/var/www/movie_site/node_modules/mysql/lib/Connection.js:490:16)
    at Query.Sequence.end (/var/www/movie_site/node_modules/mysql/lib/protocol/sequences/Sequence.js:83:24)
    at Query._handleFinalResultPacket (/var/www/movie_site/node_modules/mysql/lib/protocol/sequences/Query.js:149:8)
    at Query.EofPacket (/var/www/movie_site/node_modules/mysql/lib/protocol/sequences/Query.js:133:8)
    at Protocol._parsePacket (/var/www/movie_site/node_modules/mysql/lib/protocol/Protocol.js:291:23)
    at Parser._parsePacket (/var/www/movie_site/node_modules/mysql/lib/protocol/Parser.js:433:10)
    at Parser.write (/var/www/movie_site/node_modules/mysql/lib/protocol/Parser.js:43:10)

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
[
  {
    id: 17,
    fname: 'adarsh',
    lname: 'dixit',
    email: '<EMAIL>',
    gender: 'male',
    post_count: 17,
    follower_count: 1,
    following_count: 0,
    profile: 'http://**************:8010/upload/profile/1667543583624-17-wallpaperflare.com_wallpaper (1).jpg',
    created_date: '2022-11-04T00:00:00.000Z',
    updated_date: '2022-12-06T11:11:18.000Z'
  }
]
{
  id: 17,
  time: 'Thu Dec 08 2022 06:27:49 GMT+0000 (Coordinated Universal Time)'
}
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1

> express@1.0.0 start
> nodemon app.js

[33m[nodemon] 2.0.20[39m
[33m[nodemon] to restart at any time, enter `rs`[39m
[33m[nodemon] watching path(s): *.*[39m
[33m[nodemon] watching extensions: js,mjs,json[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
npm notice 
npm notice New major version of npm available! 8.11.0 -> 9.2.0
npm notice Changelog: <https://github.com/npm/cli/releases/tag/v9.2.0>
npm notice Run `npm install -g npm@9.2.0` to update!
npm notice 

> express@1.0.0 start
> nodemon app.js

[33m[nodemon] 2.0.20[39m
[33m[nodemon] to restart at any time, enter `rs`[39m
[33m[nodemon] watching path(s): *.*[39m
[33m[nodemon] watching extensions: js,mjs,json[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
Habt5o0cDNWjc42y
[32m[nodemon] restarting child process[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
17
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
17
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
17
/var/www/movie_site/node_modules/mysql/lib/protocol/Parser.js:437
      throw err; // Rethrow non-MySQL errors
      ^

Error: ER_PARSE_ERROR: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '-10' at line 1
    at Query.Sequence._packetToError (/var/www/movie_site/node_modules/mysql/lib/protocol/sequences/Sequence.js:47:14)
    at Query.ErrorPacket (/var/www/movie_site/node_modules/mysql/lib/protocol/sequences/Query.js:79:18)
    at Protocol._parsePacket (/var/www/movie_site/node_modules/mysql/lib/protocol/Protocol.js:291:23)
    at Parser._parsePacket (/var/www/movie_site/node_modules/mysql/lib/protocol/Parser.js:433:10)
    at Parser.write (/var/www/movie_site/node_modules/mysql/lib/protocol/Parser.js:43:10)
    at Protocol.write (/var/www/movie_site/node_modules/mysql/lib/protocol/Protocol.js:38:16)
    at Socket.<anonymous> (/var/www/movie_site/node_modules/mysql/lib/Connection.js:88:28)
    at Socket.<anonymous> (/var/www/movie_site/node_modules/mysql/lib/Connection.js:528:10)
    at Socket.emit (node:events:527:28)
    at addChunk (node:internal/streams/readable:324:12)
    --------------------
    at Protocol._enqueue (/var/www/movie_site/node_modules/mysql/lib/protocol/Protocol.js:144:48)
    at Connection.query (/var/www/movie_site/node_modules/mysql/lib/Connection.js:198:25)
    at Query.<anonymous> (/var/www/movie_site/routes/router.js:270:22)
    at Query.<anonymous> (/var/www/movie_site/node_modules/mysql/lib/Connection.js:528:10)
    at Query._callback (/var/www/movie_site/node_modules/mysql/lib/Connection.js:490:16)
    at Query.Sequence.end (/var/www/movie_site/node_modules/mysql/lib/protocol/sequences/Sequence.js:83:24)
    at Query._handleFinalResultPacket (/var/www/movie_site/node_modules/mysql/lib/protocol/sequences/Query.js:149:8)
    at Query.EofPacket (/var/www/movie_site/node_modules/mysql/lib/protocol/sequences/Query.js:133:8)
    at Protocol._parsePacket (/var/www/movie_site/node_modules/mysql/lib/protocol/Protocol.js:291:23)
    at Parser._parsePacket (/var/www/movie_site/node_modules/mysql/lib/protocol/Parser.js:433:10) {
  code: 'ER_PARSE_ERROR',
  errno: 1064,
  sqlMessage: "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '-10' at line 1",
  sqlState: '42000',
  index: 0,
  sql: "select post_tbl.id as post_id,post_tbl.source_id as user_id, post_tbl.source_name as user_name,case when post_tbl.source_profile like ('%http://%') or post_tbl.source_profile like ('%https://%') or post_tbl.source_profile like ('%www.%') then post_tbl.source_profile when post_tbl.source_profile = '' then post_tbl.source_profile else concat('http://**************:8010/upload/profile/',post_tbl.source_profile) end as profile, post_tbl.title, post_tbl.ismovie, post_tbl.season, post_tbl.caption, post_tbl.views, concat('http://**************:8010/upload/thumbnail/',post_tbl.thumbnail) as thumbnail,post_tbl.post, post_tbl.report_count, post_tbl.imdb_rating, post_tbl.google_rating, post_tbl.created_date,genre_tbl.genre as genre,GROUP_CONCAT(DISTINCT language_tbl.language) as language,GROUP_CONCAT(DISTINCT category_tbl.cat_name) as category from post_tbl inner join user_tbl on source_id = user_tbl.id  inner join language_tbl on FIND_IN_SET(language_tbl.lang_id,post_tbl.lang_id) > 0 inner join category_tbl on FIND_IN_SET(category_tbl.cat_id,post_tbl.cat_id) > 0 inner join genre_tbl on post_tbl.genre_id=genre_tbl.id where post_tbl.source_id='17'  GROUP by post_tbl.id order by post_tbl.created_date desc limit 10 offset -10"
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m

> express@1.0.0 start
> nodemon app.js

[33m[nodemon] 2.0.20[39m
[33m[nodemon] to restart at any time, enter `rs`[39m
[33m[nodemon] watching path(s): *.*[39m
[33m[nodemon] watching extensions: js,mjs,json[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
[32m[nodemon] restarting child process[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
[
  {
    id: 17,
    fname: 'adarsh',
    lname: 'dixit',
    email: '<EMAIL>',
    gender: 'male',
    post_count: 17,
    follower_count: 1,
    following_count: 0,
    profile: 'http://**************:8010/upload/profile/1667543583624-17-wallpaperflare.com_wallpaper (1).jpg',
    created_date: '2022-11-04T00:00:00.000Z',
    updated_date: '2022-12-06T11:11:18.000Z'
  }
]
{
  id: 17,
  time: 'Thu Dec 08 2022 10:36:17 GMT+0000 (Coordinated Universal Time)'
}
authentication user errorJsonWebTokenError: jwt malformed
[
  {
    id: 17,
    fname: 'adarsh',
    lname: 'dixit',
    email: '<EMAIL>',
    gender: 'male',
    post_count: 17,
    follower_count: 1,
    following_count: 0,
    profile: 'http://**************:8010/upload/profile/1667543583624-17-wallpaperflare.com_wallpaper (1).jpg',
    created_date: '2022-11-04T00:00:00.000Z',
    updated_date: '2022-12-06T11:11:18.000Z'
  }
]
{
  id: 17,
  time: 'Thu Dec 08 2022 10:36:26 GMT+0000 (Coordinated Universal Time)'
}
TypeError: Cannot read properties of undefined (reading 'id')
    at exports.UserShowPosts (/var/www/movie_site/controller/controller.js:877:33)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at auth (/var/www/movie_site/middleware/oath.js:13:17)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at Route.dispatch (/var/www/movie_site/node_modules/express/lib/router/route.js:114:3)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at /var/www/movie_site/node_modules/express/lib/router/index.js:284:15
    at param (/var/www/movie_site/node_modules/express/lib/router/index.js:365:14)
TypeError: Cannot read properties of undefined (reading 'id')
    at exports.UserShowPosts (/var/www/movie_site/controller/controller.js:877:33)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at auth (/var/www/movie_site/middleware/oath.js:13:17)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at Route.dispatch (/var/www/movie_site/node_modules/express/lib/router/route.js:114:3)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at /var/www/movie_site/node_modules/express/lib/router/index.js:284:15
    at param (/var/www/movie_site/node_modules/express/lib/router/index.js:365:14)
TypeError: Cannot read properties of undefined (reading 'id')
    at exports.UserShowPosts (/var/www/movie_site/controller/controller.js:877:33)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at auth (/var/www/movie_site/middleware/oath.js:13:17)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at Route.dispatch (/var/www/movie_site/node_modules/express/lib/router/route.js:114:3)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at /var/www/movie_site/node_modules/express/lib/router/index.js:284:15
    at param (/var/www/movie_site/node_modules/express/lib/router/index.js:365:14)
{
  id: 34,
  time: 'Tue Dec 06 2022 10:19:20 GMT+0000 (Coordinated Universal Time)',
  iat: 1670321960
}
null
TypeError: Cannot read properties of undefined (reading 'id')
    at exports.UserShowPosts (/var/www/movie_site/controller/controller.js:877:33)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at auth (/var/www/movie_site/middleware/oath.js:13:17)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at Route.dispatch (/var/www/movie_site/node_modules/express/lib/router/route.js:114:3)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at /var/www/movie_site/node_modules/express/lib/router/index.js:284:15
    at param (/var/www/movie_site/node_modules/express/lib/router/index.js:365:14)
TypeError: Cannot read properties of undefined (reading 'id')
    at exports.UserShowPosts (/var/www/movie_site/controller/controller.js:877:33)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at auth (/var/www/movie_site/middleware/oath.js:13:17)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at Route.dispatch (/var/www/movie_site/node_modules/express/lib/router/route.js:114:3)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at /var/www/movie_site/node_modules/express/lib/router/index.js:284:15
    at param (/var/www/movie_site/node_modules/express/lib/router/index.js:365:14)
{
  id: 34,
  time: 'Tue Dec 06 2022 10:19:20 GMT+0000 (Coordinated Universal Time)',
  iat: 1670321960
}
null
{
  id: 34,
  time: 'Tue Dec 06 2022 10:19:20 GMT+0000 (Coordinated Universal Time)',
  iat: 1670321960
}
null
{
  id: 34,
  time: 'Tue Dec 06 2022 10:19:20 GMT+0000 (Coordinated Universal Time)',
  iat: 1670321960
}
{
  id: 34,
  time: 'Tue Dec 06 2022 10:19:20 GMT+0000 (Coordinated Universal Time)',
  iat: 1670321960
}
null
{
  id: 34,
  time: 'Tue Dec 06 2022 10:19:20 GMT+0000 (Coordinated Universal Time)',
  iat: 1670321960
}
TypeError: Cannot read properties of undefined (reading 'id')
    at exports.UserShowPosts (/var/www/movie_site/controller/controller.js:877:33)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at auth (/var/www/movie_site/middleware/oath.js:13:17)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at Route.dispatch (/var/www/movie_site/node_modules/express/lib/router/route.js:114:3)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at /var/www/movie_site/node_modules/express/lib/router/index.js:284:15
    at param (/var/www/movie_site/node_modules/express/lib/router/index.js:365:14)
TypeError: Cannot read properties of undefined (reading 'id')
    at exports.UserShowPosts (/var/www/movie_site/controller/controller.js:877:33)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at auth (/var/www/movie_site/middleware/oath.js:13:17)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at Route.dispatch (/var/www/movie_site/node_modules/express/lib/router/route.js:114:3)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at /var/www/movie_site/node_modules/express/lib/router/index.js:284:15
    at param (/var/www/movie_site/node_modules/express/lib/router/index.js:365:14)
TypeError: Cannot read properties of undefined (reading 'id')
    at exports.UserShowPosts (/var/www/movie_site/controller/controller.js:877:33)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at auth (/var/www/movie_site/middleware/oath.js:13:17)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at Route.dispatch (/var/www/movie_site/node_modules/express/lib/router/route.js:114:3)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at /var/www/movie_site/node_modules/express/lib/router/index.js:284:15
    at param (/var/www/movie_site/node_modules/express/lib/router/index.js:365:14)
TypeError: Cannot read properties of undefined (reading 'id')
    at exports.UserShowPosts (/var/www/movie_site/controller/controller.js:877:33)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at auth (/var/www/movie_site/middleware/oath.js:13:17)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at Route.dispatch (/var/www/movie_site/node_modules/express/lib/router/route.js:114:3)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at /var/www/movie_site/node_modules/express/lib/router/index.js:284:15
    at param (/var/www/movie_site/node_modules/express/lib/router/index.js:365:14)
Habt5o0cDNWjc42y
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
/var/www/movie_site/node_modules/mysql/lib/protocol/Parser.js:437
      throw err; // Rethrow non-MySQL errors
      ^

ReferenceError: id is not defined
    at Query.<anonymous> (/var/www/movie_site/controller/controller.js:983:280)
    at Query.<anonymous> (/var/www/movie_site/node_modules/mysql/lib/Connection.js:528:10)
    at Query._callback (/var/www/movie_site/node_modules/mysql/lib/Connection.js:490:16)
    at Query.Sequence.end (/var/www/movie_site/node_modules/mysql/lib/protocol/sequences/Sequence.js:83:24)
    at Query._handleFinalResultPacket (/var/www/movie_site/node_modules/mysql/lib/protocol/sequences/Query.js:149:8)
    at Query.EofPacket (/var/www/movie_site/node_modules/mysql/lib/protocol/sequences/Query.js:133:8)
    at Protocol._parsePacket (/var/www/movie_site/node_modules/mysql/lib/protocol/Protocol.js:291:23)
    at Parser._parsePacket (/var/www/movie_site/node_modules/mysql/lib/protocol/Parser.js:433:10)
    at Parser.write (/var/www/movie_site/node_modules/mysql/lib/protocol/Parser.js:43:10)
    at Protocol.write (/var/www/movie_site/node_modules/mysql/lib/protocol/Protocol.js:38:16)

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
TypeError: Cannot read properties of undefined (reading 'id')
    at exports.UserShowPosts (/var/www/movie_site/controller/controller.js:878:25)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at auth (/var/www/movie_site/middleware/oath.js:13:17)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at next (/var/www/movie_site/node_modules/express/lib/router/route.js:144:13)
    at Route.dispatch (/var/www/movie_site/node_modules/express/lib/router/route.js:114:3)
    at Layer.handle [as handle_request] (/var/www/movie_site/node_modules/express/lib/router/layer.js:95:5)
    at /var/www/movie_site/node_modules/express/lib/router/index.js:284:15
    at param (/var/www/movie_site/node_modules/express/lib/router/index.js:365:14)
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
{
  id: 17,
  time: 'Thu Dec 08 2022 10:36:26 GMT+0000 (Coordinated Universal Time)',
  iat: 1670495786
}
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
null
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m

> express@1.0.0 start
> nodemon app.js

[33m[nodemon] 2.0.20[39m
[33m[nodemon] to restart at any time, enter `rs`[39m
[33m[nodemon] watching path(s): *.*[39m
[33m[nodemon] watching extensions: js,mjs,json[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
Habt5o0cDNWjc42y
[32m[nodemon] restarting child process[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker102316436.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker102316436.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker938747826.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
{
  fieldname: 'profile',
  originalname: 'com.sparta.quira.movieflicker938747826.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg'
}
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
[
  {
    id: 34,
    fname: 'shubham',
    lname: 'pushkar',
    email: '<EMAIL>',
    gender: 'male',
    post_count: 0,
    follower_count: 0,
    following_count: 0,
    profile: 'http://**************:8010/upload/profile/thiusis',
    created_date: '2022-12-02T12:29:08.000Z',
    updated_date: '2022-12-02T12:29:08.000Z'
  }
]
{
  id: 34,
  time: 'Thu Dec 08 2022 11:52:38 GMT+0000 (Coordinated Universal Time)'
}
38
true
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
The solution is:  1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
Habt5o0cDNWjc42y
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
/var/www/movie_site/node_modules/express/lib/router/route.js:211
        throw new Error(msg);
        ^

Error: Route.post() requires a callback function but got a [object Undefined]
    at Route.<computed> [as post] (/var/www/movie_site/node_modules/express/lib/router/route.js:211:15)
    at Function.proto.<computed> [as post] (/var/www/movie_site/node_modules/express/lib/router/index.js:521:19)
    at Object.<anonymous> (/var/www/movie_site/routes/router.js:180:8)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Module.require (node:internal/modules/cjs/loader:999:19)
    at require (node:internal/modules/cjs/helpers:102:18)
    at Object.<anonymous> (/var/www/movie_site/app.js:8:16)

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
/var/www/movie_site/node_modules/express/lib/router/route.js:211
        throw new Error(msg);
        ^

Error: Route.post() requires a callback function but got a [object Undefined]
    at Route.<computed> [as post] (/var/www/movie_site/node_modules/express/lib/router/route.js:211:15)
    at Function.proto.<computed> [as post] (/var/www/movie_site/node_modules/express/lib/router/index.js:521:19)
    at Object.<anonymous> (/var/www/movie_site/routes/router.js:180:8)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Module.require (node:internal/modules/cjs/loader:999:19)
    at require (node:internal/modules/cjs/helpers:102:18)
    at Object.<anonymous> (/var/www/movie_site/app.js:8:16)

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: Connection lost: The server closed the connection.
    at Protocol.end (/var/www/movie_site/node_modules/mysql/lib/protocol/Protocol.js:112:13)
    at Socket.<anonymous> (/var/www/movie_site/node_modules/mysql/lib/Connection.js:94:28)
    at Socket.<anonymous> (/var/www/movie_site/node_modules/mysql/lib/Connection.js:528:10)
    at Socket.emit (node:events:539:35)
    at endReadableNT (node:internal/streams/readable:1345:12)
    at processTicksAndRejections (node:internal/process/task_queues:83:21)
Emitted 'error' event on Connection instance at:
    at Connection._handleProtocolError (/var/www/movie_site/node_modules/mysql/lib/Connection.js:423:8)
    at Protocol.emit (node:events:527:28)
    at Protocol._delegateError (/var/www/movie_site/node_modules/mysql/lib/protocol/Protocol.js:398:10)
    at Protocol.end (/var/www/movie_site/node_modules/mysql/lib/protocol/Protocol.js:116:8)
    at Socket.<anonymous> (/var/www/movie_site/node_modules/mysql/lib/Connection.js:94:28)
    [... lines matching original stack trace ...]
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  fatal: true,
  code: 'PROTOCOL_CONNECTION_LOST'
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[33mwarn[39m:    --minUptime not set. Defaulting to: 1000ms
[33mwarn[39m:    --spinSleepTime not set. Your script will exit if it does not stay up for at least 1000ms
[32minfo[39m:    Forever processing file: [90mapp.js[39m
(node:17502) Warning: Accessing non-existent property 'padLevels' of module exports inside circular dependency
(Use `node --trace-warnings ...` to show where the warning was created)
(node:17502) Warning: Accessing non-existent property 'padLevels' of module exports inside circular dependency
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
Habt5o0cDNWjc42y
null
null
Habt5o0cDNWjc42y
null
null
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
null
Habt5o0cDNWjc42y
null
null
Habt5o0cDNWjc42y
null
null
Habt5o0cDNWjc42y
null
null
null
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
node:events:505
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8010
    at Server.setupListenHandle [as _listen2] (node:net:1380:16)
    at listenInCluster (node:net:1428:12)
    at Server.listen (node:net:1516:7)
    at Function.listen (/var/www/movie_site/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/var/www/movie_site/app.js:55:5)
    at Module._compile (node:internal/modules/cjs/loader:1099:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1153:10)
    at Module.load (node:internal/modules/cjs/loader:975:32)
    at Function.Module._load (node:internal/modules/cjs/loader:822:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:77:12)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1407:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 8010
}

Node.js v17.9.1
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
null
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `node app.js`[39m
port is running on 8010
The solution is:  1
Habt5o0cDNWjc42y
null
null
null
