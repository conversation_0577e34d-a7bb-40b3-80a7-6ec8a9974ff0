/* eslint-disable */
// react and useEffect
import React, { useEffect, useState } from "react";
// @mui material components
import Grid from "@mui/material/Grid";
import SearchBar from "material-ui-search-bar";
// Material Dashboard 2 React components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import "./loader.css";

import useMediaQuery from "@material-ui/core/useMediaQuery";
import { useTheme } from "@material-ui/core/styles";

// Material Dashboard 2 React example components
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import Footer from "examples/Footer";
import { ToastContainer, toast } from "react-toastify";
import DefaultProjectCard from "examples/Cards/ProjectCards/DefaultProjectCard";
import CircularProgress from "@mui/material/CircularProgress";

// Overview page components
import Header from "./components/Header";

import { useSelector, useDispatch } from "react-redux";
import { fetchPost } from "../../redux/postslice";
import {
  Link,
  useParams,
  useLocation,
  useNavigate,
  useSearchParams,
} from "react-router-dom";
import { useCookies } from "react-cookie";
import Select from "react-select";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import FormControlLabel from "@mui/material/FormControlLabel";
import Switch from "@mui/material/Switch";

function Overview() {
  const location = useLocation();
  const [posts, setPosts] = useState({ lastpage: false, records: 1 });
  const [finalData, setFinalData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams({ page: 1 });
  const [cookies] = useCookies(["admin"]);
  const token = cookies.admin;
  const [isDeleted, setIsDeleted] = useState(false);
  const [crawlPostSwitch, setCrawlPostSwitch] = useState(
    localStorage.getItem("movieSwitch")?.toLowerCase() === "true"
  );
  const [crawlTrailerSwitch, setCrawlTrailerSwitch] = useState(
    localStorage.getItem("trailerSwitch")?.toLowerCase() === "true"
  );
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  useEffect(() => {
    if (crawlPostSwitch) {
      crawlPost();
    } else {
      crawlTrailer();
    }
  }, [parseInt(searchParams.get("page"))]);

  const fetchCrawlData = async (url) => {
    setLoading(true);
    const response = await fetch(url, {
      method: "POST",
      headers: { auth: token },
    });
    const result = await response.json();
    if (result.status === 200) {
      setFinalData(result.data);
      setPosts({ lastpage: result.lastpage, records: result.records });
    } else {
      setFinalData([]);
    }
    setLoading(false);
  };

  const crawlPost = () => {
    fetchCrawlData(`${window.path}/crawlmovies/1`);
  };

  const crawlPostManual = () => {
    fetchCrawlData(
      `${window.path}/AdminAutoDeletePost/${parseInt(searchParams.get("page"))}`
    );
  };

  const crawlTrailer = () => {
    fetchCrawlData(`${window.path}/crawltrailer/1`);
  };

  const crawlTrailerManual = () => {
    fetchCrawlData(
      `${window.path}/AdminAutoDeleteTrailer/${parseInt(
        searchParams.get("page")
      )}`
    );
  };

  const checkCrawl = () => {
    if (crawlPostSwitch) {
      crawlPostManual();
    } else {
      crawlTrailerManual();
    }
  };

  const handleSwitchChange = (type) => {
    if (type === "trailer") {
      localStorage.setItem("trailerSwitch", true);
      localStorage.setItem("movieSwitch", false);
      setCrawlTrailerSwitch(true);
      setCrawlPostSwitch(false);
      crawlTrailer();
    } else {
      localStorage.setItem("trailerSwitch", false);
      localStorage.setItem("movieSwitch", true);
      setCrawlTrailerSwitch(false);
      setCrawlPostSwitch(true);
      crawlPost();
    }
  };

  const prev = () => {
    const currentPage = parseInt(searchParams.get("page"));
    if (currentPage > 1) {
      setSearchParams({ page: currentPage - 1 });
    }
  };

  const next = () => {
    if (!posts.lastpage) {
      const currentPage = parseInt(searchParams.get("page"));
      setSearchParams({ page: currentPage + 1 });
    }
  };

  const pages = Math.ceil(posts.records / 8);
  const currentPages = Array.from({ length: pages }, (_, i) => i + 1).slice(
    Math.max(0, parseInt(searchParams.get("page")) - 10),
    parseInt(searchParams.get("page"))
  );

  return (
    <DashboardLayout>
      <ToastContainer />
      <DashboardNavbar />
      <MDBox mb={2} />
      <Header>
        <MDBox pt={-2} px={2} lineHeight={1.5}>
          <MDBox
            width="100%"
            style={{
              display: "flex",
              justifyContent: "flex-end",
              alignItems: "center",
            }}
          >
            <FormControlLabel
              control={
                <Switch
                  checked={crawlTrailerSwitch}
                  onChange={() => handleSwitchChange("trailer")}
                  name="trailerSwitch"
                />
              }
              label="Trailer"
            />
            <FormControlLabel
              control={
                <Switch
                  checked={crawlPostSwitch}
                  onChange={() => handleSwitchChange("movie")}
                  name="movieSwitch"
                />
              }
              label="Movie"
            />
            <button
              style={{
                height: "35px",
                width: "150px",
                backgroundColor: "#1976D2",
                color: "white",
                border: "none",
                borderRadius: "10px",
                fontSize: "17px",
                cursor: "pointer",
                marginLeft: "10px",
                zIndex: "1",
              }}
              onClick={checkCrawl}
            >
              Check crawl
            </button>
          </MDBox>
        </MDBox>
        <MDBox style={{ float: "right" }} float="right" lineHeight={1.25} />
        <MDBox p={2}>
          <Grid container spacing={6}>
            {loading ? (
              <MDBox align="center" width="100%" mt={10}>
                <MDTypography
                  component="a"
                  href="#"
                  variant="button"
                  color="text"
                  fontWeight="medium"
                >
                  <CircularProgress />
                </MDTypography>
              </MDBox>
            ) : finalData?.length > 0 ? (
              finalData.map((e) => (
                <Grid item xs={12} md={6} xl={3} key={e?.post_id}>
                  <DefaultProjectCard
                    post_id={e?.post_id}
                    post={e.post ?? e.thumbnail}
                    label={e?.user_name}
                    title={e?.title}
                    movie={e?.link}
                    description={e?.caption}
                    trailer={e?.trailer}
                    poster={e?.thumbnail}
                    genre={e?.genre}
                    userid={e?.user_id}
                    page={parseInt(searchParams.get("page"))}
                    isdeleted={setIsDeleted}
                    action={{
                      type: "internal",
                      route: `/posts/indpost/${e.post_id}`,
                      color: "info",
                      label: "view detail",
                    }}
                  />
                </Grid>
              ))
            ) : (
              <MDBox align="center" width="100%" mt={10}>
                <MDTypography
                  component="a"
                  href="#"
                  variant="button"
                  color="text"
                  fontWeight="medium"
                >
                  No Post Found
                </MDTypography>
              </MDBox>
            )}
          </Grid>
        </MDBox>
      </Header>
      <Footer />
    </DashboardLayout>
  );
}

export default Overview;
