import 'package:my_video/app_imports.dart';

class AppHelper {
  static void hideKeyboard() {
    FocusManager.instance.primaryFocus?.unfocus();
  }

  static void showSnackBar(String message, {bool isError = false}) {
    Get.snackbar(
      isError ? 'Error' : 'Success',
      message,
      backgroundColor: isError ? AppColorConstants.colorRed : AppColorConstants.colorGreen,
      colorText: AppColorConstants.colorWhite,
      snackPosition: SnackPosition.BOTTOM,
      margin: const EdgeInsets.all(AppSizeConstants.marginMedium),
      borderRadius: AppSizeConstants.radiusMedium,
      duration: AppDurationConstants.toastDuration,
    );
  }

  static void showToast(String message, {bool isError = false}) {
    BotToast.showText(
      text: message,
      textStyle: const TextStyle(
        color: AppColorConstants.colorWhite,
        fontSize: AppSizeConstants.fontSizeMedium,
      ),
      contentColor: isError ? AppColorConstants.colorRed : AppColorConstants.colorGreen,
      duration: AppDurationConstants.toastDuration,
      borderRadius: BorderRadius.circular(AppSizeConstants.radiusMedium),
    );
  }

  static void showLoader() {
    BotToast.showLoading();
  }

  static void hideLoader() {
    BotToast.closeAllLoading();
  }

  static String formatDateTime(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
  }

  static String formatDate(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy').format(dateTime);
  }

  static String formatTime(DateTime dateTime) {
    return DateFormat('HH:mm').format(dateTime);
  }

  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  static bool isValidPhone(String phone) {
    return RegExp(r'^[0-9]{10}$').hasMatch(phone);
  }

  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!isValidEmail(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }
    if (!isValidPhone(value)) {
      return 'Please enter a valid phone number';
    }
    return null;
  }

  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  static void logDebug(String message) {
    if (kDebugMode) {
      Logger().d(message);
    }
  }

  static void logError(String message, [dynamic error, StackTrace? stackTrace]) {
    if (kDebugMode) {
      Logger().e(message, error: error, stackTrace: stackTrace);
    }
  }

  static void logInfo(String message) {
    if (kDebugMode) {
      Logger().i(message);
    }
  }
}
