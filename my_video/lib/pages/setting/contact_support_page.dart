import 'package:my_video/app_imports.dart';

class ContactSupportPage extends StatefulWidget {
  const ContactSupportPage({super.key});

  @override
  State<ContactSupportPage> createState() => _ContactSupportPageState();
}

class _ContactSupportPageState extends State<ContactSupportPage> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _subjectController = TextEditingController();
  final TextEditingController _messageController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  String _selectedCategory = 'General Inquiry';
  final List<String> _categories = [
    'General Inquiry',
    'Technical Issue',
    'Billing Question',
    'Feature Request',
    'Bug Report',
    'Account Problem',
    'Content Issue',
    'Other',
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _subjectController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    MySize.init(context);
    return Scaffold(
      appBar: AppBar(
        title: AppText(
          text: 'Contact Support',
          fontSize: MySize.fontSize(20),
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        backgroundColor: AppColorConstants.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: AppColorConstants.textPrimary,
            size: MySize.height(24),
          ),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(MySize.width(16)),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(MySize.width(16)),
                decoration: BoxDecoration(
                  color: AppColorConstants.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(MySize.radius(12)),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.support_agent,
                      size: MySize.height(48),
                      color: AppColorConstants.primaryColor,
                    ),
                    Space.height(8),
                    AppText(
                      text: 'We\'re here to help!',
                      fontSize: MySize.fontSize(18),
                      fontWeight: FontWeight.bold,
                      color: AppColorConstants.textPrimary,
                      textAlign: TextAlign.center,
                    ),
                    Space.height(4),
                    AppText(
                      text:
                          'Send us a message and we\'ll get back to you as soon as possible.',
                      fontSize: MySize.fontSize(14),
                      color: AppColorConstants.textSecondary,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              Space.height(24),

              // Contact Form
              _buildTextField(
                controller: _nameController,
                label: 'Full Name',
                hint: 'Enter your full name',
                icon: Icons.person_outline,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your name';
                  }
                  return null;
                },
              ),

              Space.height(16),

              _buildTextField(
                controller: _emailController,
                label: 'Email Address',
                hint: 'Enter your email address',
                icon: Icons.email_outlined,
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your email';
                  }
                  if (!GetUtils.isEmail(value)) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),

              Space.height(16),

              // Category Dropdown
              AppText(
                text: 'Category',
                fontSize: MySize.fontSize(16),
                fontWeight: FontWeight.w600,
                color: AppColorConstants.textPrimary,
              ),
              Space.height(8),
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: MySize.width(12)),
                decoration: BoxDecoration(
                  color: AppColorConstants.cardColor,
                  borderRadius: BorderRadius.circular(MySize.radius(8)),
                  border: Border.all(color: AppColorConstants.dividerColor),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: _selectedCategory,
                    isExpanded: true,
                    icon: const Icon(Icons.keyboard_arrow_down),
                    items: _categories.map((String category) {
                      return DropdownMenuItem<String>(
                        value: category,
                        child: AppText(
                          text: category,
                          fontSize: MySize.fontSize(14),
                          color: AppColorConstants.textPrimary,
                        ),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        setState(() {
                          _selectedCategory = newValue;
                        });
                      }
                    },
                  ),
                ),
              ),

              Space.height(16),

              _buildTextField(
                controller: _subjectController,
                label: 'Subject',
                hint: 'Brief description of your inquiry',
                icon: Icons.subject_outlined,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a subject';
                  }
                  return null;
                },
              ),

              Space.height(16),

              _buildTextField(
                controller: _messageController,
                label: 'Message',
                hint: 'Please describe your issue or question in detail...',
                icon: Icons.message_outlined,
                maxLines: 5,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your message';
                  }
                  if (value.length < 10) {
                    return 'Message must be at least 10 characters long';
                  }
                  return null;
                },
              ),

              Space.height(32),

              // Submit Button
              SizedBox(
                width: double.infinity,
                child: AppButton(
                  text: 'Send Message',
                  onPressed: _submitForm,
                  icon: const Icon(
                    Icons.send,
                    color: AppColorConstants.textPrimary,
                  ),
                ),
              ),

              Space.height(24),

              // Alternative Contact Methods
              _buildAlternativeContacts(),

              Space.height(100), // Bottom padding
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          text: label,
          fontSize: MySize.fontSize(16),
          fontWeight: FontWeight.w600,
          color: AppColorConstants.textPrimary,
        ),
        Space.height(8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, color: AppColorConstants.primaryColor),
            filled: true,
            fillColor: AppColorConstants.cardColor,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(MySize.radius(8)),
              borderSide: BorderSide(color: AppColorConstants.dividerColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(MySize.radius(8)),
              borderSide: BorderSide(color: AppColorConstants.dividerColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(MySize.radius(8)),
              borderSide: BorderSide(color: AppColorConstants.primaryColor),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(MySize.radius(8)),
              borderSide: BorderSide(color: AppColorConstants.colorRed),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAlternativeContacts() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(MySize.width(16)),
      decoration: BoxDecoration(
        color: AppColorConstants.cardColor,
        borderRadius: BorderRadius.circular(MySize.radius(12)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppText(
            text: 'Other Ways to Reach Us',
            fontSize: MySize.fontSize(16),
            fontWeight: FontWeight.bold,
            color: AppColorConstants.textPrimary,
          ),
          Space.height(12),

          _buildContactItem(
            icon: Icons.email_outlined,
            title: 'Email',
            subtitle: '<EMAIL>',
            onTap: () => _launchEmail('<EMAIL>'),
          ),

          _buildContactItem(
            icon: Icons.phone_outlined,
            title: 'Phone',
            subtitle: '+****************',
            onTap: () => _launchPhone('+15551234567'),
          ),

          _buildContactItem(
            icon: Icons.schedule_outlined,
            title: 'Support Hours',
            subtitle: 'Mon-Fri: 9AM-6PM EST',
          ),
        ],
      ),
    );
  }

  Widget _buildContactItem({
    required IconData icon,
    required String title,
    required String subtitle,
    VoidCallback? onTap,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: MySize.height(8)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(MySize.radius(8)),
        child: Padding(
          padding: EdgeInsets.all(MySize.height(8)),
          child: Row(
            children: [
              Icon(
                icon,
                color: AppColorConstants.primaryColor,
                size: MySize.height(20),
              ),
              Space.width(12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppText(
                      text: title,
                      fontSize: MySize.fontSize(14),
                      fontWeight: FontWeight.w600,
                      color: AppColorConstants.textPrimary,
                    ),
                    AppText(
                      text: subtitle,
                      fontSize: MySize.fontSize(12),
                      color: AppColorConstants.textSecondary,
                    ),
                  ],
                ),
              ),
              if (onTap != null)
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColorConstants.textSecondary,
                  size: MySize.height(16),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      // Simulate form submission
      Get.snackbar(
        'Message Sent',
        'Thank you for contacting us! We\'ll get back to you within 24 hours.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColorConstants.colorGreen,
        colorText: AppColorConstants.textPrimary,
        duration: const Duration(seconds: 3),
      );

      // Clear form
      _nameController.clear();
      _emailController.clear();
      _subjectController.clear();
      _messageController.clear();
      setState(() {
        _selectedCategory = 'General Inquiry';
      });
    }
  }

  void _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=MyVideo Support Request',
    );

    try {
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      } else {
        Get.snackbar(
          'Error',
          'Could not open email client',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColorConstants.colorRed,
          colorText: AppColorConstants.textPrimary,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Could not open email client',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColorConstants.colorRed,
        colorText: AppColorConstants.textPrimary,
      );
    }
  }

  void _launchPhone(String phone) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phone);

    try {
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        Get.snackbar(
          'Error',
          'Could not open phone dialer',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColorConstants.colorRed,
          colorText: AppColorConstants.textPrimary,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Could not open phone dialer',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColorConstants.colorRed,
        colorText: AppColorConstants.textPrimary,
      );
    }
  }
}
