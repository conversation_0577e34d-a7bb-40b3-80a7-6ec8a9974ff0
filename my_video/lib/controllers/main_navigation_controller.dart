import 'package:my_video/app_imports.dart';

class MainNavigationController extends GetxController {
  int _currentIndex = 0;
  int get currentIndex => _currentIndex;

  final List<Widget> _pages = [
    const HomePage(),
    const MyPlaylistPage(),
    const SubscriptionPage(),
    const SettingsPage(),
  ];

  List<Widget> get pages => _pages;

  void changeTab(int index) {
    if (_currentIndex != index) {
      _currentIndex = index;
      update();
    }
  }

  void showAddMovieDialog(BuildContext ctx) {
    showModalBottomSheet(
      context: ctx,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          padding: EdgeInsets.all(MySize.width(20)),
          decoration: BoxDecoration(
            color: AppColorConstants.surfaceColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(MySize.radius(20)),
              topRight: Radius.circular(MySize.radius(20)),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: MySize.width(40),
                height: MySize.height(4),
                decoration: BoxDecoration(
                  color: AppColorConstants.dividerColor,
                  borderRadius: BorderRadius.circular(MySize.radius(2)),
                ),
              ),
              Space.height(20),

              Row(
                children: [
                  Icon(
                    Icons.add_circle_outline,
                    color: AppColorConstants.primaryColor,
                    size: MySize.height(24),
                  ),
                  Space.width(8),
                  AppText(
                    text: 'Add Content',
                    fontSize: MySize.fontSize(18),
                    fontWeight: FontWeight.bold,
                    color: AppColorConstants.textPrimary,
                  ),
                ],
              ),
              Space.height(20),

              _buildBottomSheetOption(
                icon: Icons.movie_outlined,
                title: 'Add Movie',
                subtitle: 'Add a new movie to your collection',
                onTap: () {
                  Navigator.of(ctx).pop();
                  ScaffoldMessenger.of(ctx).showSnackBar(
                    SnackBar(
                      content: const Text(
                        'Add movie feature will be available soon',
                      ),
                      backgroundColor: AppColorConstants.primaryColor,
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                },
              ),
              Space.height(10),
              _buildBottomSheetOption(
                icon: Icons.playlist_add_outlined,
                title: 'Create Playlist',
                subtitle: 'Create a new custom playlist',
                onTap: () {
                  Navigator.of(ctx).pop();
                  ScaffoldMessenger.of(ctx).showSnackBar(
                    SnackBar(
                      content: const Text(
                        'Create playlist feature will be available soon',
                      ),
                      backgroundColor: AppColorConstants.primaryColor,
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                },
              ),

              Space.height(50),
            ],
          ),
        );
      },
    );

    // Logger().e('Error showing add movie dialog: ');
    // Get.snackbar(
    //   'Error',
    //   'Could not open add content menu',
    //   snackPosition: SnackPosition.BOTTOM,
    //   backgroundColor: AppColorConstants.colorRed,
    //   colorText: AppColorConstants.t
    // extPrimary,
    // );
  }

  Widget _buildBottomSheetOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Builder(
      builder: (context) {
        MySize.init(context);
        return InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(MySize.radius(12)),
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.all(MySize.width(16)),
            margin: EdgeInsets.only(bottom: MySize.height(8)),
            decoration: BoxDecoration(
              color: AppColorConstants.cardColor,
              borderRadius: BorderRadius.circular(MySize.radius(12)),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(MySize.width(12)),
                  decoration: BoxDecoration(
                    color: AppColorConstants.colorCCE1F5,
                    borderRadius: BorderRadius.circular(MySize.radius(8)),
                  ),
                  child: Icon(
                    icon,
                    color: AppColorConstants.primaryColor,
                    size: MySize.height(24),
                  ),
                ),
                Space.width(16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppText(
                        text: title,
                        fontSize: MySize.fontSize(16),
                        fontWeight: FontWeight.w600,
                        color: AppColorConstants.textPrimary,
                      ),
                      Space.height(2),
                      AppText(
                        text: subtitle,
                        fontSize: MySize.fontSize(14),
                        color: AppColorConstants.textSecondary,
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColorConstants.textSecondary,
                  size: MySize.height(16),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
