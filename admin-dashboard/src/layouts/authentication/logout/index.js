//  /* eslint-disable */
//  // import { useNavigate } from 'react-router-dom'
// import { ToastContainer, toast } from 'react-toastify';
// import React, { useEffect } from 'react';
//  import 'react-toastify/dist/ReactToastify.css';
// import { useCookies } from 'react-cookie';
// import { useNavigate} from "react-router-dom";

// const index = () => {
//     const navigate = useNavigate()
//     const [cookie,setcookie,remove] = useCookies(['admin','userId','userName'])
//     remove("admin",{
//       path:"/"
//     })
//     remove("userId",{
//       path:"/"
//     })
//     remove("userName",{
//       path:"/"
//     })
//     sessionStorage.clear()
//     toast.success('logout successfully !', {
//         position: toast.POSITION.TOP_CENTER
//       });

//       // setTimeout(()=>{
//       //     window.location.reload()
//       // },1000)
//       useEffect(()=>{
//         navigate("/login")
//       },[])

//   return (
//     <ToastContainer/>
//   )
// }

// export default index

/* eslint-disable */
import React, { useEffect } from "react";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useCookies } from "react-cookie";
import { useNavigate } from "react-router-dom";
import Modal2 from "examples/Cards/ProjectCards/DefaultProjectCard/Modal2";
import "examples/Cards/ProjectCards/DefaultProjectCard/Modal.css";
const Index = () => {
  const navigate = useNavigate();
  const [cookie, setCookie, remove] = useCookies([
    "admin",
    "userId",
    "userName",
  ]);

  const logout = () => {
    remove("admin", {
      path: "/",
    });
    remove("userId", {
      path: "/",
    });
    remove("userName", {
      path: "/",
    });
    sessionStorage.clear();
    toast.success("Logout successfully!", {
      position: toast.POSITION.TOP_CENTER,
    });
    navigate("/login");
  };

  const canclelogout = () => {
    window.history.back();
  };
  // const shouldLogout = window.confirm("Are you sure you want to logout?");
  // if (shouldLogout) {
  //   remove("admin", {
  //     path: "/",
  //   });
  //   remove("userId", {
  //     path: "/",
  //   });
  //   remove("userName", {
  //     path: "/",
  //   });
  //   sessionStorage.clear();
  //   toast.success("Logout successfully!", {
  //     position: toast.POSITION.TOP_CENTER,
  //   });
  //   navigate("/login");
  // } else {
  //   // If the user clicks Cancel, go back to the previous page
  //   window.history.back();
  // }

  return (
    <div>
      <div className="modalBackground">
        <div className="modalContainer">
          <div className="titleCloseBtn">
            <button onClick={() => canclelogout()}>X</button>
          </div>
          <div className="title">
            <h5>Are You Sure You Want to Logout?</h5>
          </div>
          <div className="footer">
            <button id="cancelBtn" onClick={() => canclelogout()}>
              No
            </button>
            <button onClick={() => logout()}>Yes</button>
          </div>
        </div>
      </div>
      <ToastContainer />
    </div>
  );
};

export default Index;
