// API Configuration
const API_CONFIG = {
  // Change this to your local backend URL
  BASE_URL: 'http://localhost:8010',
  
  // Admin authentication token (you may need to update this)
  AUTH_TOKEN: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwiaWF0IjoxNzE2NzgyNDk2fQ.jOZUk0DLDcmVrsxczNcF-BlQmC-UI9s2B5i5sKv7wTY',
  
  // Static key for some endpoints
  STATIC_KEY: 'Habt5o0cDNWjc42y'
};

// API Endpoints
export const API_ENDPOINTS = {
  DASHBOARD: `${API_CONFIG.BASE_URL}/dashboard_test`,
  ADMIN_SHOW_POST: `${API_CONFIG.BASE_URL}/adminshowpost`,
  ADMIN_CHECK_EMBEDDED: `${API_CONFIG.BASE_URL}/Admincheckembeded`,
  COUNTRY_WISE_RESTRICT: `${API_CONFIG.BASE_URL}/countrywiserestricmovies`,
  TEST_DATA: `${API_CONFIG.BASE_URL}/testdata`,
  MX_VIDEO_BY_MOVIE: `${API_CONFIG.BASE_URL}/mxvideobymovie`,
  EDIT_MX_VIDEO: `${API_CONFIG.BASE_URL}/editmxvideo`,
  GET_CRAWL_EPISODES: `${API_CONFIG.BASE_URL}/getcrawlepisodes`,
  CRAWL_MOVIES: `${API_CONFIG.BASE_URL}/crawlmovies`,
};

// Default headers for API requests
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Auth': API_CONFIG.AUTH_TOKEN,
};

// Headers for form data requests
export const FORM_HEADERS = {
  'Content-Type': 'application/x-www-form-urlencoded',
  'auth': API_CONFIG.STATIC_KEY,
};

export default API_CONFIG;
