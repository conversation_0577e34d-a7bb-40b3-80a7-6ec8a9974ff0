import 'package:my_video/app_imports.dart';

class LoginPageHelper {
  final LoginPageState _state;
  final Logger _logger = Logger();

  // Loading state
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // Form controllers
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  // Form key
  final GlobalKey<FormState> loginFormKey = GlobalKey<FormState>();

  // UI State
  bool _isPasswordVisible = false;
  bool get isPasswordVisible => _isPasswordVisible;

  // API Status
  ApiStatus apiStatus = ApiStatus.initial;

  LoginPageHelper(this._state) {
    _initializeData();
  }

  void _initializeData() {
    _logger.i('Login page initialized');
  }

  Future<void> login() async {
    if (!loginFormKey.currentState!.validate()) {
      return;
    }

    try {
      _setLoading(true);
      apiStatus = ApiStatus.loading;
      _state.authController.update();

      final hasInternet = await ConnectivityHelper.hasInternetConnection();
      if (!hasInternet) {
        apiStatus = ApiStatus.noInternet;
        AppHelper.showToast('no_internet_connection'.tr, isError: true);
        return;
      }

      final result = await _state.authController.loginFromAPI(
        email: emailController.text.trim(),
        password: passwordController.text.trim(),
      );

      if (result['success'] == true) {
        final token = result['data']['token'] as String;
        final userData = result['data']['user'] as Map<String, dynamic>;

        // Save to local storage
        await AppSharedPreference.setUserToken(token);
        await AppSharedPreference.setUserData(userData);

        apiStatus = ApiStatus.success;
        AppHelper.showToast('login_successful');

        // Navigate to main navigation
        gotoMainNavigationPage();
      } else {
        apiStatus = ApiStatus.error;
        AppHelper.showToast(result['message'] ?? 'login_failed', isError: true);
      }
    } catch (e) {
      apiStatus = ApiStatus.error;
      AppHelper.logError('Login error', e);
      AppHelper.showToast('something_went_wrong', isError: true);
    } finally {
      _setLoading(false);
      _state.authController.update();
    }
  }

  void navigateToForgotPassword() {
    try {
      gotoForgotPasswordPage();
    } catch (e) {
      _logger.e('Error navigating to forgot password: $e');
    }
  }

  void navigateToSignUp() {
    try {
      gotoSignupPage();
    } catch (e) {
      _logger.e('Error navigating to signup: $e');
    }
  }

  void togglePasswordVisibility() {
    _isPasswordVisible = !_isPasswordVisible;
    _state.authController.update();
  }

  // Validation Methods
  String? validateEmail(String? value) {
    return ValidationHelper.validateEmail(value);
  }

  String? validatePassword(String? value) {
    return ValidationHelper.validatePassword(value);
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    _state.authController.update();
  }

  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    _logger.i('Login page helper disposed');
  }
}
