// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'movie_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MoviesResponse _$MoviesResponseFromJson(Map<String, dynamic> json) =>
    MoviesResponse(
      success: json['success'] as bool,
      message: json['message'] as String?,
      data: (json['data'] as List<dynamic>)
          .map((e) => MovieModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalCount: (json['total_count'] as num?)?.toInt(),
      page: (json['page'] as num?)?.toInt(),
      perPage: (json['per_page'] as num?)?.toInt(),
      hasMore: json['has_more'] as bool?,
    );

Map<String, dynamic> _$MoviesResponseToJson(MoviesResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
      'total_count': instance.totalCount,
      'page': instance.page,
      'per_page': instance.perPage,
      'has_more': instance.hasMore,
    };

CategoriesResponse _$CategoriesResponseFromJson(Map<String, dynamic> json) =>
    CategoriesResponse(
      success: json['success'] as bool,
      message: json['message'] as String?,
      data: (json['data'] as List<dynamic>)
          .map((e) => CategoryModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$CategoriesResponseToJson(CategoriesResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };

FeaturedMoviesResponse _$FeaturedMoviesResponseFromJson(
        Map<String, dynamic> json) =>
    FeaturedMoviesResponse(
      success: json['success'] as bool,
      message: json['message'] as String?,
      data: (json['data'] as List<dynamic>)
          .map((e) => MovieModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$FeaturedMoviesResponseToJson(
        FeaturedMoviesResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };

MoviesByCategoryResponse _$MoviesByCategoryResponseFromJson(
        Map<String, dynamic> json) =>
    MoviesByCategoryResponse(
      success: json['success'] as bool,
      message: json['message'] as String?,
      category: json['category'] == null
          ? null
          : CategoryModel.fromJson(json['category'] as Map<String, dynamic>),
      data: (json['data'] as List<dynamic>)
          .map((e) => MovieModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalCount: (json['total_count'] as num?)?.toInt(),
      page: (json['page'] as num?)?.toInt(),
      perPage: (json['per_page'] as num?)?.toInt(),
      hasMore: json['has_more'] as bool?,
    );

Map<String, dynamic> _$MoviesByCategoryResponseToJson(
        MoviesByCategoryResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'category': instance.category,
      'data': instance.data,
      'total_count': instance.totalCount,
      'page': instance.page,
      'per_page': instance.perPage,
      'has_more': instance.hasMore,
    };

SearchMoviesResponse _$SearchMoviesResponseFromJson(
        Map<String, dynamic> json) =>
    SearchMoviesResponse(
      success: json['success'] as bool,
      message: json['message'] as String?,
      query: json['query'] as String?,
      data: (json['data'] as List<dynamic>)
          .map((e) => MovieModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalCount: (json['total_count'] as num?)?.toInt(),
      page: (json['page'] as num?)?.toInt(),
      perPage: (json['per_page'] as num?)?.toInt(),
      hasMore: json['has_more'] as bool?,
    );

Map<String, dynamic> _$SearchMoviesResponseToJson(
        SearchMoviesResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'query': instance.query,
      'data': instance.data,
      'total_count': instance.totalCount,
      'page': instance.page,
      'per_page': instance.perPage,
      'has_more': instance.hasMore,
    };
