import { Window } from "@mui/icons-material";
import { createSlice,createAsyncThunk } from "@reduxjs/toolkit";
import {useCookies} from "react-cookie";

const initialState = {
        loading :true,
        reports :[],
        error:''
}

export const fetchReports = createAsyncThunk('reports/fetchReports',async({page,token})=>{
    console.log(token)
    const res = await   fetch(`${window.path}/adminshowreports?page=${page}`,{
        method:"GET",
        headers:{
            auth:token
        }

    })
    const data = await res.json()
    
    return data;
})


const reportSlice = createSlice({
    name:'reports',
    initialState,
    extraReducers:(builder)=>{
        builder.addCase(fetchReports.pending,(state)=>{
            state.loading = true;
        })
        builder.addCase(fetchReports.fulfilled,(state,action)=>{
            state.loading = false;
            state.reports = action.payload
            state.error = ''
        })
        builder.addCase(fetchReports.rejected,(state,action)=>{
            state.loading = false;
            state.users = ''
            state.error = action.error.message
        })
    }
})


export default reportSlice.reducer