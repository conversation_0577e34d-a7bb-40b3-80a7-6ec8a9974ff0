/* eslint-disable */
// react and useEffect
import React, { useEffect, useState } from "react";
import Grid from "@mui/material/Grid";
// Material Dashboard 2 React components
import { useTheme } from "@material-ui/core/styles";
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
// Material Dashboard 2 React example components
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import Footer from "examples/Footer";
import SearchIcon from "@mui/icons-material/Search";
import DefaultProjectCard from "examples/Cards/ProjectCards/DefaultProjectCard";
import Header from "./components/Header";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import { useSelector, useDispatch } from "react-redux";
import { fetchPost } from "../../redux/postslice";
import { Link, useLocation, useSearchParams } from "react-router-dom";
import { useCookies } from "react-cookie";
import Select from "react-select";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import { debounce } from "lodash";
function Overview() {
  const location = useLocation();
  const { posts } = useSelector((state) => state);
  const [finaldata, setfinaldata] = useState(null);
  const [sortoptionvalue, setSortoptionvalue] = useState({
    label: "Date Modefied",
    value: "date",
  });
  const [backpage, setbackpage] = useState(1);
  const [page, setpage] = useState(backpage);
  let [searchParams, setSearchParams] = useSearchParams({ page: 1 });
  const [cookie, getcookie] = useCookies();
  const token = cookie.admin;
  const [searchkey, setsearchkey] = useState(null);
  const [isdeleted, setisdeleted] = useState(false);
  const dispatch = useDispatch();

  const form = new FormData();
  form.append("sort", sortoptionvalue.value);
  form.append("key", searchkey);
  useEffect(() => {
    dispatch(
      fetchPost({ page: parseInt(searchParams.get("page")), token, form })
    ).then((e) => {
      e?.payload?.result ? setfinaldata(e.payload.result) : setfinaldata([]);
    });
  }, [dispatch, parseInt(searchParams.get("page")), sortoptionvalue]);

  const debouncedFetchPost = debounce(() => {
    const form = new FormData();
    form.append("sort", sortoptionvalue.value);
    form.append("key", searchkey);
    dispatch(
      fetchPost({ page: parseInt(searchParams.get("page")), token, form })
    ).then((e) => {
      setfinaldata(e?.payload?.result || []);
    });
  }, 800);

  useEffect(() => {
    if (searchkey != null || searchkey != undefined) {
      debouncedFetchPost();
      return () => {
        debouncedFetchPost.cancel();
      };
    }
  }, [searchkey]);

  const sortoption = [
    { label: "Date Modefied", value: "date" },
    { label: "Title", value: "title" },
    { label: "Name", value: "name" },
  ];

  const prev = () => {
    if (parseInt(searchParams.get("page")) > 1) {
      setSearchParams({ page: parseInt(searchParams.get("page")) - 1 });
    }
  };
  const next = () => {
    if (posts?.posts?.lastpage != true) {
      setSearchParams({ page: parseInt(searchParams.get("page")) + 1 });
    }
  };
  const pages = Math.ceil(posts?.posts?.records / 8);
  const nopages = [];
  for (let i = 1; i <= pages; i++) {
    nopages.push(i);
  }
  let currentpages = [];
  if (parseInt(searchParams.get("page")) > 10) {
    let s = (Math.ceil(parseFloat(searchParams.get("page") / 10)) - 1) * 10;
    currentpages = [...nopages.splice(s, 10)];
  } else {
    currentpages = [...nopages.splice(0, 10)];
  }

  const sorthandle = (e) => {
    setSortoptionvalue(e);
  };

  const searchHanlde = (e) => {
    e.preventDefault();
    if (e.target.value) {
      setSearchParams({ page: 1 });
      setsearchkey(e.target.value);
    } else {
      setsearchkey("");
    }
  };

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox mb={2} />
      <Header>
        <MDBox
          width="98%"
          display={isMobile ? "block" : "flex"}
          flexDirection={isMobile ? "column" : ""}
          align=""
          lineHeight={1.25}
        >
          <MDBox width={isMobile ? "100%" : "70%"} mt={1} ml={isMobile ? 0 : 4}>
            {" "}
            <MDTypography
              variant="h6"
              fontWeight="medium"
              width={isMobile ? "100%" : "70%"}
            >
              <span>
                <Select
                  placeholder="sort"
                  onChange={sorthandle}
                  height={20}
                  width={20}
                  options={sortoption}
                  value={sortoptionvalue}
                  defaultValue={sortoptionvalue}
                ></Select>
              </span>
            </MDTypography>
          </MDBox>

          <MDBox
            mt={1}
            style={{
              marginBottom: "20px",
              marginLeft: "10%",
              height: "25px",
            }}
          >
            <table
              style={{
                height: "25px",
                border: "1px solid #c7c7c7",
                overflowX: "auto",
              }}
            >
              <thead style={{ display: "block" }}>
                <tr style={{ display: "flex", width: "100%" }}>
                  <td
                    scope="col"
                    style={{
                      width: "40px",
                      textAlign: "center",
                      borderRight: "1px solid #b1b5b2",
                      cursor: "pointer",
                      color: "#646669",
                    }}
                    onClick={() => {
                      parseInt(searchParams.get("page")) - 10 >= 1
                        ? setSearchParams({
                            page:
                              Math.floor(
                                parseInt(searchParams.get("page")) / 10
                              ) *
                                10 -
                              9,
                          })
                        : "";
                    }}
                  >
                    <KeyboardArrowLeftIcon style={{ marginRight: "-10px" }} />
                    <KeyboardArrowLeftIcon />
                  </td>
                  <td
                    scope="col"
                    style={{
                      width: "40px",
                      textAlign: "center",
                      cursor: "pointer",
                      color: "#646669",
                    }}
                    onClick={prev}
                  >
                    <KeyboardArrowLeftIcon />
                  </td>
                  {currentpages[0] > 1 ? (
                    <>
                      <td
                        style={{
                          width: "40px",
                          textAlign: "center",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          borderLeft: "1px solid #b1b5b2",
                          cursor: "pointer",
                          fontSize: "17px",
                          color: "#646669",
                        }}
                        onClick={() => {
                          setSearchParams({ page: 1 });
                        }}
                      >
                        1
                      </td>
                      <td
                        style={{
                          width: "40px",
                          textAlign: "center",
                          borderLeft: "1px solid #b1b5b2",
                          cursor: "pointer",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          fontSize: "17px",
                          color: "#646669",
                        }}
                      >
                        ...
                      </td>
                    </>
                  ) : (
                    ""
                  )}

                  {currentpages?.map((e) => {
                    return (
                      <td
                        style={{
                          borderLeft: "1px solid #b1b5b2",
                          width: "40px",
                          textAlign: "center",
                          cursor: "pointer",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          fontSize: "17px",
                          backgroundColor: `${
                            e == parseInt(searchParams.get("page"))
                              ? "#3993EE"
                              : ""
                          }`,
                        }}
                        onClick={() => {
                          setSearchParams({ page: e });
                        }}
                      >
                        {" "}
                        <span>
                          {" "}
                          <Link
                            to=""
                            style={{
                              position: "absolute",
                              color: `${
                                e == parseInt(searchParams.get("page"))
                                  ? "white"
                                  : "#646669"
                              }`,
                            }}
                            className={`p-1   position-relative  text-underline-hover`}
                          >
                            {e}
                          </Link>
                        </span>
                      </td>
                    );
                  })}

                  {currentpages[currentpages.length - 1] < pages ? (
                    <>
                      <td
                        style={{
                          width: "40px",
                          textAlign: "center",
                          borderLeft: "1px solid #b1b5b2",
                          cursor: "pointer",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          fontSize: "17px",
                          color: "#646669",
                        }}
                      >
                        ...
                      </td>
                      <td
                        style={{
                          width: "40px",
                          textAlign: "center",
                          borderLeft: "1px solid #b1b5b2",
                          cursor: "pointer",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          fontSize: "17px",
                          color: "#646669",
                        }}
                        onClick={() => {
                          setSearchParams({ page: pages });
                        }}
                      >
                        {pages}
                      </td>
                    </>
                  ) : (
                    ""
                  )}

                  <td
                    style={{
                      width: "40px",
                      textAlign: "center",
                      borderLeft: "1px solid #b1b5b2",
                      cursor: "pointer",
                      color: "#646669",
                    }}
                    onClick={next}
                  >
                    {" "}
                    <NavigateNextIcon />{" "}
                  </td>
                  <td
                    style={{
                      width: "40px",
                      textAlign: "center",
                      borderLeft: "1px solid #b1b5b2",
                      cursor: "pointer",
                      color: "#646669",
                    }}
                    onClick={() => {
                      parseInt(searchParams.get("page")) + 10 <= pages
                        ? setSearchParams({
                            page:
                              Math.ceil(
                                parseInt(searchParams.get("page")) / 10
                              ) *
                                10 +
                              1,
                          })
                        : "";
                    }}
                  >
                    {" "}
                    <NavigateNextIcon style={{ marginRight: "-10px" }} />
                    <NavigateNextIcon />
                  </td>
                </tr>
              </thead>
            </table>
          </MDBox>

          <MDBox
            width={isMobile ? "100%" : "120%"}
            align={isMobile ? "left" : "right"}
            style={{ marginTop: isMobile ? "10px" : "" }}
            mt={1}
          >
            {" "}
            {/* Align changes based on device */}
            <MDTypography
              variant="h6"
              width={isMobile ? "100%" : "80%"}
              align={isMobile ? "left" : "right"}
              mb={2}
              mt={0}
              fontWeight="medium"
            >
              <input
                style={{
                  paddingLeft: "10px",
                  height: "40px",
                  borderRadius: "10px",
                  border: "1px solid black",
                  width: isMobile ? "100%" : "250px",
                }}
                width=""
                onChange={(e) => {
                  searchHanlde(e);
                }}
                placeholder="search post here..."
              />
              <SearchIcon
                fontSize="medium"
                style={{ marginLeft: isMobile ? "0px" : "-30px" }}
                color="black"
              ></SearchIcon>
            </MDTypography>
          </MDBox>
        </MDBox>

        <MDBox p={2}>
          {posts.loading == false && posts.error == "" ? (
            <Grid container spacing={3}>
              {finaldata == null ? (
                <MDBox align="center" width="100%" mt={10}>
                  <MDTypography
                    component="a"
                    href="#"
                    variant="button"
                    color="text"
                    fontWeight="medium"
                  >
                    Loading...
                  </MDTypography>
                </MDBox>
              ) : finaldata.length == 0 ? (
                <MDBox align="center" width="100%" mt={10}>
                  <MDTypography
                    component="a"
                    href="#"
                    variant="button"
                    color="text"
                    fontWeight="medium"
                  >
                    No Data Found{" "}
                  </MDTypography>
                </MDBox>
              ) : (
                finaldata?.map((e) => {
                  return (
                    <Grid
                      item
                      xs={12}
                      md={6}
                      xl={3}
                      mt={-2}
                      key={e.post_id}
                      overflow={isMobile ? "auto" : ""}
                    >
                      <DefaultProjectCard
                        post_id={e.post_id}
                        post={e.post != null ? e.post : e.thumbnail}
                        label={e.user_name}
                        title={e.title}
                        description={e.caption}
                        trailer={e.trailer}
                        movie={e.post}
                        poster={e.thumbnail}
                        genre={e.genre}
                        userid={e.user_id}
                        isverified={e.isverified}
                        page={parseInt(searchParams.get("page"))}
                        isdeleted={setisdeleted}
                        action={{
                          type: "internal",
                          route: `/posts/indpost/${e.post_id}`,
                          color: "info",
                          label: "view detail",
                        }}
                      />
                    </Grid>
                  );
                })
              )}
            </Grid>
          ) : (
            <MDTypography variant="h6" fontWeight="medium" textAlign="center">
              loading...
            </MDTypography>
          )}
        </MDBox>
      </Header>
      <Footer />
    </DashboardLayout>
  );
}

export default Overview;
