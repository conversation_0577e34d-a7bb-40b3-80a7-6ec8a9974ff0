import React from "react";
import ReactD<PERSON> from "react-dom";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom";
import App from "App";
import { Provider } from "react-redux";
import { MaterialUIControllerProvider } from "context";
import { CookiesProvider } from "react-cookie";
import Store from './store'
// import "bootstrap/dist/css/bootstrap.min.css"
// import "bootstrap/dist/js/bootstrap"


// Material Dashboard 2 React Context Provider

ReactDOM.render(
  <CookiesProvider>
  <Provider store={Store}>
  <BrowserRouter>
    <MaterialUIControllerProvider>
      <App />
    </MaterialUIControllerProvider>
  </BrowserRouter>
  </Provider>
  </CookiesProvider>,
  document.getElementById("root")
);
