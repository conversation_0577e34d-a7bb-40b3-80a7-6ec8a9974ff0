import 'package:my_video/app_imports.dart';

/// API Adapter to bridge Flutter app's expected endpoints with backend's actual endpoints
class ApiAdapter {
  static final Logger _logger = Logger();

  /// Convert Flutter's REST-style endpoints to backend's actual endpoints
  static Future<Response> adaptedGet(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
  }) async {
    final adaptedEndpoint = _adaptEndpoint(endpoint);
    final adaptedMethod = _getMethodForEndpoint(endpoint);
    
    _logger.d('Adapting endpoint: $endpoint -> $adaptedEndpoint (${adaptedMethod.toUpperCase()})');
    
    if (adaptedMethod == 'post') {
      return await _makePostRequest(adaptedEndpoint, queryParameters, headers);
    } else {
      return await RestHelper.get(adaptedEndpoint, headers: headers, queryParameters: queryParameters);
    }
  }

  /// Make POST request with proper body format for backend
  static Future<Response> _makePostRequest(
    String endpoint,
    Map<String, dynamic>? queryParams,
    Map<String, String>? headers,
  ) async {
    Map<String, dynamic> body = {};
    
    // Add query parameters to body for POST requests
    if (queryParams != null) {
      body.addAll(queryParams);
    }
    
    // Add specific body parameters based on endpoint
    if (endpoint.contains('/showposts')) {
      // For movie listing endpoints
      body['sort'] = 'date';
    } else if (endpoint.contains('/searchpost')) {
      // For search endpoints
      body['key'] = queryParams?['q'] ?? '';
    } else if (endpoint.contains('/dashboard')) {
      // For dashboard endpoint
      body['date'] = DateTime.now().toIso8601String().split('T')[0];
      body['ismovie'] = '1';
    }
    
    return await RestHelper.post(endpoint, headers: headers, body: body);
  }

  /// Map Flutter's expected endpoints to backend's actual endpoints
  static String _adaptEndpoint(String endpoint) {
    // Remove leading slash if present
    final cleanEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;
    
    switch (cleanEndpoint) {
      case 'movies':
        return 'showposts/1'; // Get first page of movies
      case 'movies/featured':
        return 'dashboard'; // Dashboard contains featured content
      case 'categories':
        return 'showfilter'; // Get categories/filters
      default:
        // Handle dynamic endpoints
        if (cleanEndpoint.startsWith('movies/category/')) {
          final categoryName = cleanEndpoint.split('/').last;
          return 'showcatwise/1'; // Category-wise movies
        } else if (cleanEndpoint.startsWith('movies/search')) {
          return 'searchpost/1'; // Search movies
        } else if (cleanEndpoint.startsWith('movies/')) {
          final movieId = cleanEndpoint.split('/').last;
          return 'showposts/1'; // Individual movie (will need filtering)
        }
        
        return cleanEndpoint; // Return as-is if no mapping found
    }
  }

  /// Determine HTTP method for endpoint
  static String _getMethodForEndpoint(String endpoint) {
    // Most backend endpoints expect POST requests
    final cleanEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;
    
    switch (cleanEndpoint) {
      case 'movies':
      case 'movies/featured':
      case 'categories':
        return 'post';
      default:
        if (cleanEndpoint.startsWith('movies/category/') ||
            cleanEndpoint.startsWith('movies/search') ||
            cleanEndpoint.startsWith('movies/')) {
          return 'post';
        }
        return 'get';
    }
  }
}
