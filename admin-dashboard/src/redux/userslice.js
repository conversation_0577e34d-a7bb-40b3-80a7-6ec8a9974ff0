import { createSlice,createAsyncThunk } from "@reduxjs/toolkit";
import { render } from "@testing-library/react";
import React from "react";
import {useCookies} from "react-cookie";


const initialState = {
        loading :true,
        users :[],
        error:''
}


// generates pending request
export const fetchUsers = createAsyncThunk('user/fetchUser',async({page,token,form})=>{
    const res = await   fetch(`${window.path}/adminshowuser/${page}`,{
        method:"POST",
        headers:{
            auth:token
        },
        body:form

    })
    const data = await res.json()
    return data;
})
const userSlice = createSlice({
    name:'user',
    initialState,
    extraReducers:(builder)=>{
        builder.addCase(fetchUsers.pending,(state)=>{
            state.loading = true;
        })
        builder.addCase(fetchUsers.fulfilled,(state,action)=>{
            state.loading = false;
            state.users = action.payload    
            state.error = ''
        })
        builder.addCase(fetchUsers.rejected,(state,action)=>{
            state.loading = false;
            state.users = ''
            state.error = action.error.message
        })
    }
})


export default userSlice.reducer