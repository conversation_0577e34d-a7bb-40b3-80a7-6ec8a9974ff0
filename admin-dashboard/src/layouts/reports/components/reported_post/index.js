/* eslint-disable */
// @mui material components
import Card from "@mui/material/Card";
import React, { useEffect } from "react";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import { useTheme } from "@material-ui/core/styles";

// Material Dashboard 2 React components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import MDButton from "components/MDButton";
import { Link, useSearchParams } from "react-router-dom";
// Billing page components
import Single_report from "layouts/reports/components/view2";
import { useSelector, useDispatch } from "react-redux";
import { fetchReports } from "../../../../redux/reportslice";
import { useState } from "react";
import { useCookies } from "react-cookie";
import { CleanHands } from "@mui/icons-material";
import SearchBar from "material-ui-search-bar";
import { ToastContainer, toast } from "react-toastify";
import moment from "moment/moment";
import Select from "react-select";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import SearchIcon from "@mui/icons-material/Search";
function BillingInformation() {
  const [select, setselect] = useState(false);
  const [selectall, setselectall] = useState(false);
  const [selectdata, setselectdata] = useState([]);
  const [page, setpage] = useState(1);
  const [report, setreport] = useState([]);
  const [cookie, setcookie] = useCookies();
  let [searchParams, setSearchParams] = useSearchParams({
    page: 1,
    seachpage: 0,
  });

  const token = cookie.admin;
  const [finaldata, setfinaldata] = useState(null);
  const [sortopt, setsortopt] = useState({
    label: "Date Modefied",
    value: "date",
  });
  const [key, setkey] = useState(null);
  const fetchReports = async () => {
    const form = new FormData();
    form.append("key", key);
    form.append("sort", sortopt.value);
    const reports = await fetch(
      `${window.path}/adminshowreportcount/${searchParams.get("page")}`,
      {
        method: "POST",
        headers: {
          auth: token,
        },
        body: form,
      }
    );
    const res = await reports.json();
    setreport(res);
    setfinaldata(res.result);
  };
  useEffect(() => {
    fetchReports();
  }, [parseInt(searchParams.get("page")), key, sortopt]);

  const prev = () => {
    if (parseInt(searchParams.get("page")) > 1) {
      setSearchParams({ page: parseInt(searchParams.get("page")) - 1 });
    }
  };
  const next = () => {
    if (report.lastpage == false) {
      setSearchParams({ page: parseInt(searchParams.get("page")) + 1 });
    }
  };
  const pages = Math.ceil(report?.reportlist / 8);
  const nopages = [];
  for (let i = 1; i <= pages; i++) {
    nopages.push(i);
  }
  let currentpages = [];
  if (parseInt(searchParams.get("page")) >= 10) {
    let s = (Math.ceil(parseFloat(searchParams.get("page") / 10)) - 1) * 10;
    currentpages = [...nopages.splice(s, 10)];
  } else {
    currentpages = [...nopages.splice(0, 10)];
  }
  const selecthandle = (e) => {
    if (e.target.checked) {
      setselectdata([...selectdata, e.target.value]);
    } else {
      if (selectdata.includes(e.target.value) === true) {
        for (let i = 0; i <= selectdata.length - 1; i++) {
          if (selectdata[i] == e.target.value) {
            selectdata.splice(i, 1);
          }
        }
      }
    }
  };
  let sortedarray;
  const sorthandle = (e) => {
    setsortopt(e);
  };
  const searchHanlde = (e) => {
    e.preventDefault();
    if (e.target.value) {
      setSearchParams({ page: 1 });

      setkey(e.target.value);
    } else {
      setkey(null);
    }
    // if (e.length > 1 && e.length < 4) {
    //   setSearchParams({ page: 1 });
    // }
    // if (e.length > 2) {
    //   setkey(e);
    // } else {
    //   setkey(null);
    // }
  };
  const unsetsearch = () => {
    setfinaldata(report?.result);
  };
  const delreport = async () => {
    let form = new FormData();
    form.append("data", selectdata);

    const del = await fetch(`${window.path}/admindeletemultyreport`, {
      method: "DELETE",
      headers: { auth: `bearer ${token}` },
      body: form,
    });
    const res = await del.json();
    if (res.status === 1) {
      toast.success("deleted successfully !", {
        position: toast.POSITION.TOP_CENTER,
      });
      fetchReports();
    } else {
      toast.error("invalid credential !", {
        position: toast.POSITION.TOP_CENTER,
      });
    }
  };
  const sortoption = [
    { label: "Date Modefied", value: "date" },
    { label: "Title", value: "title" },
  ];

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <Card id="delete-account" style={{ marginTop: "-60px" }}>
      <ToastContainer />
      <MDBox pt={1} px={2} display="flex">
        <MDBox width={isMobile ? "100%" : "50%"} mt={0} ml={4}>
          <MDTypography
            variant="h6"
            fontWeight="medium"
            width={isMobile ? "90%" : "30%"}
          >
            <span>
              <Select
                placeholder="sort"
                onChange={sorthandle}
                height={20}
                options={sortoption}
                value={sortopt}
                width={20}
              ></Select>
            </span>
          </MDTypography>
        </MDBox>
        <MDTypography variant="h6" px={2} fontWeight="medium" display="flex">
          {report?.result != "" ? (
            select == false ? (
              <button
                className="btn btn-primary"
                onClick={() => {
                  setselect(true);
                }}
                style={{ height: "35px" }}
              >
                SELECT
              </button>
            ) : (
              <button
                className="btn btn-primary"
                onClick={() => {
                  setselect(false);
                }}
                style={{ height: "35px" }}
              >
                DESELECT
              </button>
            )
          ) : (
            ""
          )}
        </MDTypography>
        <MDBox pt={0} px={2} display="flex" flexDirection="row-reverse">
          <MDTypography variant="h6" fontWeight="medium" px={2} display="flex">
            {select == true ? (
              <>
                <button
                  className="btn btn-primary"
                  onClick={delreport}
                  style={{ height: "35px" }}
                >
                  DELETE
                </button>
              </>
            ) : (
              ""
            )}
          </MDTypography>
        </MDBox>
        <MDBox
          width={isMobile ? "100%" : "50%"}
          align={isMobile ? "" : "right"}
        >
          <MDTypography
            variant="h6"
            width={isMobile ? "100%" : "50%"}
            align="right"
            flexDirection="right"
            mb={0}
            fontWeight="medium"
          >
            <input
              style={{
                paddingLeft: "10PX",
                height: "40px",
                borderRadius: "10px",
                border: "1px solid #7b809a",
                width: isMobile ? "90%" : "250px",
                marginTop: isMobile ? "10px" : "",
              }}
              width=""
              onChange={(e) => {
                searchHanlde(e);
              }}
              placeholder="search user here..."
              // onRequestSearch={(e)=>{searchHanlde(e)}}
            />
            <SearchIcon
              fontSize="medium"
              style={{ marginLeft: "-30px" }}
              color="black"
            ></SearchIcon>
          </MDTypography>
        </MDBox>
      </MDBox>

      <MDBox pt={0} pb={2} px={1}>
        <MDBox
          width="98%"
          display={isMobile ? "block" : "flex"}
          flexDirection="space-around"
          align=""
          mt={1}
          lineHeight={1.25}
        ></MDBox>
        <MDBox
          component="ul"
          display="flex"
          flexDirection="column"
          p={0}
          ml={3}
        >
          <div className="row">
            {report?.result != "" ? (
              finaldata == null ? (
                <MDBox align="center" mt={2} overflow={"auto"}>
                  <MDTypography
                    component="a"
                    href="#"
                    variant="button"
                    color="text"
                    fontWeight="medium"
                  >
                    Loading...
                  </MDTypography>
                </MDBox>
              ) : (
                finaldata?.map((e) => {
                  return (
                    <div className="col-lg-4 mt-0">
                      <Single_report
                        post={e.post != null ? e.post : e.thumbnail}
                        title={e.title}
                        poster={e.thumbnail}
                        caption={e.caption}
                        movie={e?.post}
                        views={e.views}
                        reason={e.reason}
                        id={e.id}
                        date={e.created_date.slice(e.created_date, 10)}
                        report_count={e.report_count}
                        select={select}
                        selectall={selectall}
                        selectedData={selecthandle}
                      />
                    </div>
                  );
                })
              )
            ) : (
              <MDBox align="center" mt={10}>
                <MDTypography
                  component="a"
                  href="#"
                  variant="button"
                  color="text"
                  fontWeight="medium"
                >
                  No Data Found
                </MDTypography>
              </MDBox>
            )}
          </div>

          <MDBox
            style={{
              width: "100%",
              align: "center",
              marginTop: "50px",
              display: "flex",
              flexDirection: isMobile ? "" : "space-around",
              justifyContent: isMobile ? "" : "center",
              overflow: "auto",
            }}
          >
            <table style={{ height: "10px", border: "1px solid #c7c7c7" }}>
              <thead style={{}}>
                <tr style={{ display: "flex", width: "100%" }}>
                  <td
                    scope="col"
                    style={{
                      width: "40px",
                      textAlign: "center",
                      borderRight: "1px solid #b1b5b2",
                      cursor: "pointer",
                      color: "#646669",
                    }}
                    onClick={() => {
                      parseInt(searchParams.get("page")) - 10 >= 1
                        ? setSearchParams({
                            page:
                              Math.floor(
                                parseInt(searchParams.get("page")) / 10
                              ) *
                                10 -
                              9,
                          })
                        : "";
                    }}
                  >
                    <KeyboardArrowLeftIcon style={{ marginRight: "-10px" }} />
                    <KeyboardArrowLeftIcon />
                  </td>
                  <td
                    scope="col"
                    style={{
                      width: "40px",
                      textAlign: "center",
                      cursor: "pointer",
                      color: "#646669",
                    }}
                    onClick={prev}
                  >
                    <KeyboardArrowLeftIcon />
                  </td>
                  {currentpages[0] > 1 ? (
                    <>
                      <td
                        style={{
                          width: "40px",
                          textAlign: "center",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          borderLeft: "1px solid #b1b5b2",
                          cursor: "pointer",
                          fontSize: "17px",
                          color: "#646669",
                        }}
                        onClick={() => {
                          setSearchParams({ page: 1 });
                        }}
                      >
                        1
                      </td>
                      <td
                        style={{
                          width: "40px",
                          textAlign: "center",
                          borderLeft: "1px solid #b1b5b2",
                          cursor: "pointer",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          fontSize: "17px",
                          color: "#646669",
                        }}
                      >
                        ...
                      </td>
                    </>
                  ) : (
                    ""
                  )}

                  {currentpages?.map((e) => {
                    return (
                      <td
                        style={{
                          borderLeft: "1px solid #b1b5b2",
                          width: "40px",
                          textAlign: "center",
                          cursor: "pointer",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          fontSize: "17px",
                          backgroundColor: `${
                            e == parseInt(searchParams.get("page"))
                              ? "#3993EE"
                              : ""
                          }`,
                        }}
                        onClick={() => {
                          setSearchParams({ page: e });
                        }}
                      >
                        {" "}
                        <span>
                          {" "}
                          <Link
                            to=""
                            style={{
                              position: "absolute",
                              color: `${
                                e == parseInt(searchParams.get("page"))
                                  ? "white"
                                  : "#646669"
                              }`,
                            }}
                            className={`p-1   position-relative  text-underline-hover`}
                          >
                            {e}
                          </Link>
                        </span>
                      </td>
                    );
                  })}

                  {currentpages[currentpages.length - 1] < pages ? (
                    <>
                      <td
                        style={{
                          width: "40px",
                          textAlign: "center",
                          borderLeft: "1px solid #b1b5b2",
                          cursor: "pointer",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          fontSize: "17px",
                          color: "#646669",
                        }}
                      >
                        ...
                      </td>
                      <td
                        style={{
                          width: "40px",
                          textAlign: "center",
                          borderLeft: "1px solid #b1b5b2",
                          cursor: "pointer",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          fontSize: "17px",
                          color: "#646669",
                        }}
                        onClick={() => {
                          setSearchParams({ page: pages });
                        }}
                      >
                        {pages}
                      </td>
                    </>
                  ) : (
                    ""
                  )}

                  <td
                    style={{
                      width: "40px",
                      textAlign: "center",
                      borderLeft: "1px solid #b1b5b2",
                      cursor: "pointer",
                      color: "#646669",
                    }}
                    onClick={next}
                  >
                    {" "}
                    <NavigateNextIcon />{" "}
                  </td>
                  <td
                    style={{
                      width: "40px",
                      textAlign: "center",
                      borderLeft: "1px solid #b1b5b2",
                      cursor: "pointer",
                      color: "#646669",
                    }}
                    onClick={() => {
                      parseInt(searchParams.get("page")) + 10 <= pages
                        ? setSearchParams({
                            page:
                              Math.ceil(
                                parseInt(searchParams.get("page")) / 10
                              ) *
                                10 +
                              1,
                          })
                        : "";
                    }}
                  >
                    {" "}
                    <NavigateNextIcon style={{ marginRight: "-10px" }} />
                    <NavigateNextIcon />
                  </td>
                </tr>
              </thead>
            </table>
          </MDBox>
        </MDBox>
      </MDBox>
    </Card>
  );
}

export default BillingInformation;
