import 'package:my_video/app_imports.dart';

class MovieSearchController extends GetxController {
  final MovieRepository _movieRepository = GetIt.instance<MovieRepository>();
  final Logger _logger = Logger();

  final TextEditingController searchTextController = TextEditingController();

  // Search state
  bool _isSearching = false;
  bool get isSearching => _isSearching;

  String _searchQuery = '';
  String get searchQuery => _searchQuery;

  // Search results
  List<MovieModel> _searchResults = [];
  List<MovieModel> get searchResults => _searchResults;

  // Search filters
  String _selectedCategory = 'All';
  String get selectedCategory => _selectedCategory;

  List<String> _categories = ['All'];
  List<String> get categories => _categories;

  // Search history
  List<String> _searchHistory = [];
  List<String> get searchHistory => _searchHistory;

  // Popular searches
  List<String> _popularSearches = [
    'Action Movies',
    'Comedy',
    'Drama',
    'Thriller',
    'Romance',
    'Adventure',
    'Sci-Fi',
    'Horror',
  ];
  List<String> get popularSearches => _popularSearches;

  @override
  void onInit() {
    super.onInit();
    _loadCategories();
    _loadSearchHistory();
    searchTextController.addListener(_onSearchTextChanged);
  }

  void _loadCategories() {
    try {
      final categoryModels = HiveHelper.getAllCategories();
      _categories = ['All', ...categoryModels.map((c) => c.name).toList()];
      update();
    } catch (e) {
      _logger.e('Error loading categories: $e');
    }
  }

  void _loadSearchHistory() {
    try {
      final history = HiveHelper.getSetting<List<dynamic>>(
        'search_history',
        defaultValue: [],
      );
      _searchHistory = history?.cast<String>() ?? [];
      update();
    } catch (e) {
      _logger.e('Error loading search history: $e');
    }
  }

  void _onSearchTextChanged() {
    final query = searchTextController.text.trim();
    if (query != _searchQuery) {
      _searchQuery = query;
      if (query.isNotEmpty) {
        _performSearch(query);
      } else {
        _clearSearchResults();
      }
    }
  }

  Future<void> _performSearch(String query) async {
    if (query.isEmpty) return;

    _setSearching(true);
    try {
      // Perform API search
      final response = await _movieRepository.searchMovies(query);

      if (response.success) {
        _searchResults = response.data;

        // Apply category filter if selected
        if (_selectedCategory != 'All') {
          _searchResults = _searchResults
              .where(
                (movie) =>
                    movie.category.toLowerCase() ==
                    _selectedCategory.toLowerCase(),
              )
              .toList();
        }

        _logger.i(
          'Search completed: ${_searchResults.length} results for "$query"',
        );
      } else {
        _searchResults = [];
        _logger.w('Search failed: ${response.message}');
      }
    } catch (e) {
      _logger.e('Error performing search: $e');

      // Fallback to local search
      await _performLocalSearch(query);
    } finally {
      _setSearching(false);
    }
  }

  Future<void> _performLocalSearch(String query) async {
    try {
      final allMovies = HiveHelper.getAllMovies();

      _searchResults = allMovies.where((movie) {
        final matchesQuery =
            movie.title.toLowerCase().contains(query.toLowerCase()) ||
            movie.description?.toLowerCase().contains(query.toLowerCase()) ==
                true ||
            movie.category.toLowerCase().contains(query.toLowerCase()) ||
            movie.genreString.toLowerCase().contains(query.toLowerCase());

        final matchesCategory =
            _selectedCategory == 'All' ||
            movie.category.toLowerCase() == _selectedCategory.toLowerCase();

        return matchesQuery && matchesCategory;
      }).toList();

      _logger.i(
        'Local search completed: ${_searchResults.length} results for "$query"',
      );
    } catch (e) {
      _logger.e('Error performing local search: $e');
      _searchResults = [];
    }
  }

  void searchWithQuery(String query) {
    searchTextController.text = query;
    _searchQuery = query;
    _addToSearchHistory(query);
    _performSearch(query);
  }

  void selectCategory(String category) {
    _selectedCategory = category;
    update();

    // Re-perform search with new category filter
    if (_searchQuery.isNotEmpty) {
      _performSearch(_searchQuery);
    }
  }

  void _addToSearchHistory(String query) {
    if (query.isEmpty) return;

    try {
      // Remove if already exists
      _searchHistory.remove(query);

      // Add to beginning
      _searchHistory.insert(0, query);

      // Keep only last 10 searches
      if (_searchHistory.length > 10) {
        _searchHistory = _searchHistory.take(10).toList();
      }

      // Save to storage
      HiveHelper.saveSetting('search_history', _searchHistory);
      update();
    } catch (e) {
      _logger.e('Error adding to search history: $e');
    }
  }

  void clearSearchHistory() {
    try {
      _searchHistory.clear();
      HiveHelper.deleteSetting('search_history');
      update();
      _logger.i('Search history cleared');
    } catch (e) {
      _logger.e('Error clearing search history: $e');
    }
  }

  void removeFromHistory(String query) {
    try {
      _searchHistory.remove(query);
      HiveHelper.saveSetting('search_history', _searchHistory);
      update();
    } catch (e) {
      _logger.e('Error removing from search history: $e');
    }
  }

  void clearSearch() {
    searchTextController.clear();
    _searchQuery = '';
    _clearSearchResults();
  }

  void _clearSearchResults() {
    _searchResults = [];
    update();
  }

  void playMovie(MovieModel movie) {
    _logger.i('Playing movie from search: ${movie.title}');
    Get.to(
      () => VideoPlayerPage(movie: movie),
      transition: Transition.rightToLeft,
    );
  }

  void _setSearching(bool searching) {
    _isSearching = searching;
    update();
  }

  @override
  void onClose() {
    searchTextController.dispose();
    super.onClose();
  }
}
