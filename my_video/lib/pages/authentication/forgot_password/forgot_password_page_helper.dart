import 'package:my_video/app_imports.dart';

class ForgotPasswordPageHelper {
  final ForgotPasswordPageState _state;
  final Logger _logger = Logger();

  ForgotPasswordPageHelper(this._state) {
    _initializeData();
  }

  void _initializeData() {
    _logger.i('Forgot password page initialized');
  }

  void navigateToLogin() {
    try {
      Get.context!.go(AppRoutes.login);
    } catch (e) {
      _logger.e('Error navigating to login: $e');
    }
  }

  void navigateToContactSupport() {
    try {
      Get.context!.go(AppRoutes.contactSupport);
    } catch (e) {
      _logger.e('Error navigating to contact support: $e');
    }
  }

  void dispose() {
    _logger.i('Forgot password page helper disposed');
  }
}
