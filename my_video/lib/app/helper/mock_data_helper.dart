import 'package:my_video/app_imports.dart';

class MockDataHelper {
  static List<CategoryModel> getMockCategories() {
    return [
      CategoryModel(
        id: '1',
        name: 'Action',
        description: 'Action-packed movies',
        movieCount: 15,
        isActive: true,
        sortOrder: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      CategoryModel(
        id: '2',
        name: 'Drama',
        description: 'Dramatic movies',
        movieCount: 12,
        isActive: true,
        sortOrder: 2,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      CategoryModel(
        id: '3',
        name: 'Comedy',
        description: 'Comedy movies',
        movieCount: 18,
        isActive: true,
        sortOrder: 3,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      CategoryModel(
        id: '4',
        name: 'Thriller',
        description: 'Thriller movies',
        movieCount: 10,
        isActive: true,
        sortOrder: 4,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      CategoryModel(
        id: '5',
        name: 'Romance',
        description: 'Romantic movies',
        movieCount: 8,
        isActive: true,
        sortOrder: 5,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  static List<MovieModel> getMockMovies() {
    return [
      // Featured Movies
      MovieModel(
        id: '1',
        title: 'The Amazing Adventure',
        description: 'An epic journey through unknown lands filled with mystery and wonder.',
        youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        thumbnailUrl: 'https://picsum.photos/400/600?random=1',
        category: 'Action',
        duration: '2h 15m',
        rating: 8.5,
        releaseYear: 2023,
        genre: ['Action', 'Adventure'],
        isFeatured: true,
        viewCount: 1250000,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      MovieModel(
        id: '2',
        title: 'Love in Paris',
        description: 'A romantic story set in the beautiful city of Paris.',
        youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        thumbnailUrl: 'https://picsum.photos/400/600?random=2',
        category: 'Romance',
        duration: '1h 45m',
        rating: 7.8,
        releaseYear: 2023,
        genre: ['Romance', 'Drama'],
        isFeatured: true,
        viewCount: 890000,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      MovieModel(
        id: '3',
        title: 'The Last Stand',
        description: 'A thrilling action movie about survival against all odds.',
        youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        thumbnailUrl: 'https://picsum.photos/400/600?random=3',
        category: 'Action',
        duration: '2h 5m',
        rating: 8.2,
        releaseYear: 2023,
        genre: ['Action', 'Thriller'],
        isFeatured: true,
        viewCount: 1100000,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // Action Movies
      MovieModel(
        id: '4',
        title: 'Speed Racer',
        description: 'High-speed racing action.',
        youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        thumbnailUrl: 'https://picsum.photos/400/600?random=4',
        category: 'Action',
        duration: '1h 55m',
        rating: 7.5,
        releaseYear: 2023,
        genre: ['Action', 'Racing'],
        isFeatured: false,
        viewCount: 650000,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      MovieModel(
        id: '5',
        title: 'Urban Warrior',
        description: 'Street fighting action.',
        youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        thumbnailUrl: 'https://picsum.photos/400/600?random=5',
        category: 'Action',
        duration: '1h 40m',
        rating: 7.2,
        releaseYear: 2023,
        genre: ['Action', 'Crime'],
        isFeatured: false,
        viewCount: 520000,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // Drama Movies
      MovieModel(
        id: '6',
        title: 'The Family Secret',
        description: 'A family drama with deep secrets.',
        youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        thumbnailUrl: 'https://picsum.photos/400/600?random=6',
        category: 'Drama',
        duration: '2h 10m',
        rating: 8.0,
        releaseYear: 2023,
        genre: ['Drama', 'Family'],
        isFeatured: false,
        viewCount: 780000,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      MovieModel(
        id: '7',
        title: 'Broken Dreams',
        description: 'A story of lost hopes and redemption.',
        youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        thumbnailUrl: 'https://picsum.photos/400/600?random=7',
        category: 'Drama',
        duration: '1h 50m',
        rating: 7.9,
        releaseYear: 2023,
        genre: ['Drama'],
        isFeatured: false,
        viewCount: 690000,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // Comedy Movies
      MovieModel(
        id: '8',
        title: 'Laugh Out Loud',
        description: 'A hilarious comedy that will make you laugh.',
        youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        thumbnailUrl: 'https://picsum.photos/400/600?random=8',
        category: 'Comedy',
        duration: '1h 35m',
        rating: 7.6,
        releaseYear: 2023,
        genre: ['Comedy'],
        isFeatured: false,
        viewCount: 850000,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      MovieModel(
        id: '9',
        title: 'Office Chaos',
        description: 'Comedy about office life gone wrong.',
        youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        thumbnailUrl: 'https://picsum.photos/400/600?random=9',
        category: 'Comedy',
        duration: '1h 25m',
        rating: 7.3,
        releaseYear: 2023,
        genre: ['Comedy', 'Workplace'],
        isFeatured: false,
        viewCount: 620000,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // Thriller Movies
      MovieModel(
        id: '10',
        title: 'Dark Secrets',
        description: 'A psychological thriller with twists.',
        youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        thumbnailUrl: 'https://picsum.photos/400/600?random=10',
        category: 'Thriller',
        duration: '1h 58m',
        rating: 8.1,
        releaseYear: 2023,
        genre: ['Thriller', 'Mystery'],
        isFeatured: false,
        viewCount: 920000,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  static Future<void> initializeMockData() async {
    try {
      // Save mock categories
      final categories = getMockCategories();
      await HiveHelper.saveCategories(categories);

      // Save mock movies
      final movies = getMockMovies();
      await HiveHelper.saveMovies(movies);

      Logger().i('Mock data initialized successfully');
    } catch (e) {
      Logger().e('Error initializing mock data: $e');
    }
  }
}
