import React, { useEffect, useState } from "react";
import Grid from "@mui/material/Grid";
import { useTheme } from "@material-ui/core/styles";
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import Footer from "examples/Footer";
import DefaultProjectCard from "examples/Cards/ProjectCards/DefaultProjectCard/index1";
import EpisodeCard from "examples/Cards/ProjectCards/DefaultProjectCard/EpisodeCard";
import Modal from "examples/Cards/ProjectCards/DefaultProjectCard/Modal3";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import { useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import { useCookies } from "react-cookie";
import { ToastContainer, toast } from "react-toastify";

function Mxcrawl() {
  const { posts } = useSelector((state) => state);
  const [finaldata, setFinalData] = useState([]);
  const [loading, setLoading] = useState(true);

  const [isOpen, setIsOpen] = useState();
  const [deletee, setdeletee] = useState(false);
  const [selectedMovies, setSelectedMovies] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  const [searchParams] = useSearchParams();
  const page = parseInt(searchParams.get("page"), 10) || 1;
  const [cookie] = useCookies();
  const token = cookie.admin;

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const jsonBody = { key: null };
        const response = await fetch(`http://206.189.137.63:8010/getcrawlepisodes`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(jsonBody),
        });

        if (!response.ok) throw new Error("Network response was not ok");

        const jsonData = await response.json();
        setFinalData(jsonData.data || []);
      } catch (error) {
        console.error("Error fetching data:", error);
        setFinalData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [page]);

  const handleSelectMovie = (id) => {
    setSelectedMovies((prev) =>
      prev.includes(id) ? prev.filter((movieId) => movieId !== id) : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedMovies([]);
    } else {
      setSelectedMovies(finaldata.map((movie) => movie.post_id));
    }
    setSelectAll(!selectAll);
  };

  const handleDeleteSelected = async () => {
    if (selectedMovies.length === 0) {
      toast.error("No movies selected for deletion", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 1500,
      });
      return;
    }

    try {
      const response = await fetch(`${window.path}/getcrawlepisodes`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ movieIds: selectedMovies }),
      });

      if (response.status === 200) {
        toast.success("Selected Movies Deleted Successfully", {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 1500,
        });
        setFinalData((prev) => prev.filter((movie) => !selectedMovies.includes(movie.post_id)));
        setSelectedMovies([]);
      } else {
        throw new Error("Failed to delete selected movies");
      }
    } catch (error) {
      toast.error("Something Went Wrong", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 1500,
      });
    }
  };

  useEffect(() => {
    if (deletee) {
      handleDeleteSelected();
      setdeletee(false); // Reset `deletee` to prevent further calls
    }
  }, [deletee]);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  let content;
  if (loading) {
    content = (
      <MDBox align="center" width="100%" mt={10}>
        <MDTypography variant="button" color="text" fontWeight="medium">
          Loading...
        </MDTypography>
      </MDBox>
    );
  } else if (finaldata.length === 0) {
    content = (
      <MDBox align="center" width="100%" mt={10}>
        <MDTypography variant="button" color="text" fontWeight="medium">
          No Data Found
        </MDTypography>
      </MDBox>
    );
  } else {
    content = (
      <Grid container spacing={1}>
        {finaldata.map((e) => (
          <Grid item xs={12} md={6} xl={3} key={e.post_id} overflow={isMobile ? "auto" : ""}>
            <div>
              <EpisodeCard
                post_id={e?.post_id}
                poster={e?.thumbnail}
                movieid={e?.movieid}
                // label={e?.user_name}
                title={e.title}
                epno={e.epno}
                // trailer={e.trailer}
                movie={e.link}
                // poster={e.thumbnail}
                // genre={e.genre}
                userid={e.username}
                // isverified={e.isverified}
                action={{
                  type: "internal",
                  route: `/posts/indpost/${e.post_id}`,
                  color: "info",
                  label: "view detail",
                }}
              />
            </div>
          </Grid>
        ))}
      </Grid>
    );
  }

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox mb={1} />
      <div style={{ display: "flex", justifyContent: "flex-end", gap: "10px" }}>
        {/* <button type="button" className="btn btn-warning" onClick={handleSelectAll}>
          {selectAll ? "Deselect All" : "Select All"}
        </button> */}
        {/* <button
          type="button"
          className="btn btn-danger"
          onClick={() => {
            setIsOpen(true);
            // setUnblockEmail(e.email);
          }}
        >
          Delete Selected
        </button> */}
      </div>
      {/* <Header> */}
      {isOpen && <Modal setOpenModal={setIsOpen} setdelete={setdeletee} />}
      <MDBox p={2}>{content}</MDBox>
      {/* </Header> */}
      <Footer />
    </DashboardLayout>
  );
}

export default Mxcrawl;
