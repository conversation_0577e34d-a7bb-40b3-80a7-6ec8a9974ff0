/* eslint-disable */
import React, { useEffect, useRef, useState } from "react";

import MDBox from "components/MDBox";
import TextField from "@mui/material/TextField";
import Card from "@mui/material/Card";
import OutlinedInput from "@mui/material/OutlinedInput";
import CardContent from "@mui/material/CardContent";
import MDTypography from "components/MDTypography";
import { MenuItem } from "@material-ui/core";
import { useTheme } from "@mui/material/styles";
import InputLabel from "@mui/material/InputLabel";
import MDButton from "components/MDButton";
import { ToastContainer, toast } from "react-toastify";
import "../uploadpost/postform.css";
import Chip from "@mui/material/Chip";
import Select from "react-select";
import CreatableSelect from "react-select/creatable";
import FileUploadIcon from "@mui/icons-material/FileUpload";
import { useCookies } from "react-cookie";
import DataTable from "examples/Tables/DataTable";
import { CardHeader, CardMedia } from "@mui/material";
import { Topic } from "@mui/icons-material";
// import FileInput from 'react-file-input'
import useMediaQuery from "@material-ui/core/useMediaQuery";
let colourStyles = {
  control: (base) => ({
    ...base,
    fontSize: "15px",
    marginLeft: "",
    //border:"1px solid red"
  }),
  menuList: (styles) => ({
    ...styles,
    background: "white",
  }),
  option: (styles, { isFocused, isSelected }) => ({
    ...styles,
    background: isSelected ? "hsla(291, 0%, 0%, 0.1)" : undefined,
    zIndex: 1,
  }),
  menu: (base) => ({
    ...base,
    zIndex: 100,
  }),
  placeholder: () => ({
    fontSize: "15px",
    marginBottom: "-100px",
    position: "relative",
    bottom: "67px",
    left: "10px",
    fontWeight: "300",
  }),
};

const Uploadpost = ({ id }) => {
  const [thumbpreview, setthumbpreview] = useState(null);
  const [maxVideoRows, setMaxVideoRows] = useState([
    { id: 1, field1: "", field2: "", field3: "" },
  ]);
  const [cookie, setcookie] = useCookies();
  const [result, setresult] = useState(null);
  const [data, setdata] = useState({
    title: "",
    link: "",
    caption: "",
    ismovie: "",
    season: "",
    caption: "",
    views: "",
    grating: "",
    irating: "",
    cat: "",
    lang: "",
    gen: "",
    post: "",
    thumb: "",
    free_ep: "",
  });
  const [movieid, setmovieid] = useState(null);
  const [episodeLoading, setEpisodeLoading] = useState(false);

  const addMaxVideoRow = () => {
    const newId = maxVideoRows.length + 1;
    console.log("mxvideo data", JSON.stringify(maxVideoRows));
    setMaxVideoRows([
      ...maxVideoRows,
      { id: newId, field1: "", field2: "", field3: "" },
    ]);
  };

  const handleMaxVideoChange = (id, field, value) => {
    const updatedRows = maxVideoRows.map((row) =>
      row.id === id ? { ...row, [field]: value } : row
    );
    setMaxVideoRows(updatedRows);
  };

  const removeMaxVideoRow = (id) => {
    if (maxVideoRows.length > 1) {
      const updatedRows = maxVideoRows.filter((row) => row.id !== id);
      setMaxVideoRows(updatedRows);
    }
  };

  const callCreateShortsAPI = async (epno, link, movieid, subTitle) => {
    try {
      const url = "http://206.189.137.63:8010/createshorts";

      const formData = new URLSearchParams();
      formData.append("epno", epno);
      formData.append("movieid", movieid);
      formData.append("link", link);
      formData.append("sub_title", subTitle || "");

      const response = await fetch(url, {
        method: "POST",
        headers: {
          auth: "Habt5o0cDNWjc42y",
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: formData,
      });

      const data = await response.json();
      return { success: true, data, epno, link };
    } catch (error) {
      console.error(
        `Error calling API for epno: ${epno}, link: ${link}`,
        error
      );
      return { success: false, error, epno, link };
    }
  };

  const addallmaxvideo = async (e) => {
    setEpisodeLoading(true);
    if (maxVideoRows[0]?.field1?.length === 0) {
      setEpisodeLoading(false);
      return false;
    }
    e.preventDefault();
    console.log("Max Video Rows Data:", maxVideoRows);

    const results = [];
    // const movieid = movieid;

    for (const row of maxVideoRows) {
      const epno = row.field1;
      const link = row.field2;
      const subTitle = row.field3;

      console.log(`Processing: Episode ${epno}, Link: ${link}`);

      const result = await callCreateShortsAPI(epno, link, movieid, subTitle);
      results.push(result);

      await new Promise((resolve) => setTimeout(resolve, 300));
    }

    console.log("All API calls completed:", results);

    const successCount = results.filter((r) => r.success).length;
    toast.success("All Episodes uploaded successfully !", {
      position: toast.POSITION.TOP_CENTER,
      autoClose: 300,
    });
    // setmovieid(res.movieid);
    setdata({
      title: "",
      ismovie: "",
      season: "",
      caption: "",
      views: "",
      grating: "",
      irating: "",
      cat: "",
      lang: "",
      post: "",
      gen: "",
      link: "",
      free_ep: "",
    });
    setEpisodeLoading(false);
    setmovieid(null);
    setMaxVideoRows([{ id: 1, field1: "", field2: "", field3: "" }]);
    setthumbpreview(null);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name === "post") {
      console.log("trailer link called ...");
    }
    setdata({ ...data, [name]: value });
  };

  const catref = useRef();
  const langref = useRef();
  const genref = useRef();
  const typeref = useRef();
  const fileref = useRef();
  const [titlefocus, settitlefocus] = useState(false);
  const bordernone = (e) => {
    document.getElementsByName(e.target.name)[0].style.border = "none";
  };
  const filenone = (e) => {
    document.getElementsByClassName(
      fileref.current.classList[0]
    )[0].style.border = "none";
  };
  const catnone = () => {
    document.getElementsByClassName(
      catref.current.classList[0]
    )[0].style.border = "none";
  };

  const handlePaste = (e) => {
    e.preventDefault();

    // Get the pasted content and remove leading/trailing whitespace
    const paste = (e.clipboardData || window.clipboardData)
      .getData("text")
      .trim();

    // Regular expression to allow digits, ., -, and , with optional decimal point
    const regex = /^\d+(?:[.,]\d+)*$/;

    if (regex.test(paste)) {
      // Paste the content only if it matches the allowed format
      document.execCommand("insertText", false, paste);
    }
  };

  const genone = () => {
    document.getElementsByClassName(
      genref.current.classList[0]
    )[0].style.border = "none";
  };
  const langnone = () => {
    console.log("langref", langref.current.classList[0]);
    document.getElementsByClassName(
      langref.current.classList[0]
    )[0].style.border = "none";
  };
  const typenone = () => {
    document.getElementsByClassName(
      typeref.current.classList[0]
    )[0].style.border = "none";
  };
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const token = cookie.admin;
  const handleUploadPost = async (e) => {
    e.preventDefault();
    const formdata = new FormData();
    if (
      data.title.length < 1 ||
      data.link.length < 1 ||
      data.ismovie.length < 1 ||
      data.cat.length < 1 ||
      data.views.length < 1 ||
      data.caption.length < 1 ||
      data.lang.length < 1 ||
      data.gen.length < 1 ||
      (data.post.length < 1 && data.ismovie.value !== "Mxvideos") ||
      (data.irating.length < 1 && data.ismovie.value !== "Mxvideos") ||
      (data.grating.length < 1 && data.ismovie.value !== "Mxvideos") ||
      (data.grating > 100 && data.ismovie.value !== "Mxvideos") ||
      (data.irating > 10 && data.ismovie.value !== "Mxvideos") ||
      data.ismovie.value == "series"
        ? data.season.length < 1
        : ""
    ) {
      if (data.title.length < 5) {
        let err = document.getElementsByName("title");
        err[0].style.border = "2px solid red";
      } else {
        let err = document.getElementsByName("title");
        err[0].style.border = "none";
      }
      console.log("ismovie valiudation", data.ismovie);
      if (data.ismovie.value == "series") {
        if (data.season.length < 1) {
          let err = document.getElementsByName("season");
          err[0].style.border = "2px solid red";
        } else {
          let err = document.getElementsByName("season");
          err[0].style.border = "none";
        }
      }
      if (data.link.length < 1) {
        let err = document.getElementsByName("link");
        err[0].style.border = "2px solid red";
      } else {
        let err = document.getElementsByName("link");
        err[0].style.border = "none";
      }

      if (data.irating.length < 1 && data.ismovie.value !== "Mxvideos") {
        let err = document.getElementsByName("irating");
        err[0].style.border = "2px solid red";
      } else {
        console.log("irating.......", data.irating);
        if (data.irating > 10) {
          let err = document.getElementsByName("irating");
          err[0].style.border = "2px solid red";
        } else {
          let err = document.getElementsByName("irating");
          err[0].style.border = "none";
        }
      }

      if (data.grating.length < 1 && data.ismovie.value !== "Mxvideos") {
        let err = document.getElementsByName("grating");
        err[0].style.border = "2px solid red";
        console.log("data.grating", data.grating);
      } else {
        let err = document.getElementsByName("grating");
        err[0].style.border = "none";
      }

      if (data.grating > 100 && data.ismovie.value !== "Mxvideos") {
        console.log("grating in if > 100");
        let err = document.getElementsByName("grating");
        err[0].style.border = "2px solid red";
      }

      if (data.irating > 100 && data.ismovie.value !== "Mxvideos") {
        console.log("grating in if > 100");
        let err = document.getElementsByName("irating");
        err[0].style.border = "2px solid red";
      }

      if (data.post.length < 1 && data.ismovie.value !== "Mxvideos") {
        let err = document.getElementsByName("post");
        err[0].style.border = "2px solid red";
      } else {
        let err = document.getElementsByName("post");
        err[0].style.border = "none";
      }

      if (data.views.length < 1) {
        let err = document.getElementsByName("views");
        err[0].style.border = "2px solid red";
      } else {
        let err = document.getElementsByName("views");
        err[0].style.border = "none";
      }
      if (data.caption.length < 1) {
        let err = document.getElementsByName("caption");
        err[0].style.border = "2px solid red";
      } else {
        let err = document.getElementsByName("caption");
        err[0].style.border = "none";
      }
      if (data.ismovie.length < 1) {
        let err = document.getElementsByClassName("ismovie");
        err[0].style.border = "2px solid red";
        //err[0].style.display = "inline"
      } else {
        let err = document.getElementsByClassName("ismovie");
        err[0].style.border = "none";
      }
      var format = /^[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]*$/;

      if (data?.views?.match(format)) {
        let err = document.getElementsByClassName("errorv");
        // err[0].innerHTML = "only integers are allowed";
        //err[0].style.display = "inline";
      } else {
        let err = document.getElementsByClassName("errorv");
        err[0].style.display = "none";
      }
      if (data.cat.length < 1) {
        let err = document.getElementsByClassName("cat");
        err[0].style.border = "2px solid red";
      } else {
        let err = document.getElementsByClassName("cat");
        err[0].style.border = "none";
      }
      if (data.lang.length < 1) {
        let err = document.getElementsByClassName("lang");
        err[0].style.border = "2px solid red";
      } else {
        let err = document.getElementsByClassName("lang");
        err[0].style.border = "none";
      }

      if (data.gen.length < 1) {
        console.log("gen has data");
        let err = document.getElementsByClassName("gen");
        err[0].style.border = "2px solid red";
      } else {
        console.log("gen has no data");
        let err = document.getElementsByClassName("gen");
        err[0].style.border = "none";
      }

      if (data.thumb.length < 1) {
        let err = document.getElementsByClassName("errorfile");
        err[0].innerHTML = "plese select a thumbnail";
        err[0].style.display = "inline";
      } else {
        let err = document.getElementsByClassName("errorfile");
        err[0].style.display = "none";
      }
    } else {
      if (data.ismovie.value === "Mxvideos") {
        let err = document.getElementsByName("post");
        err[0].style.border = "none";

        let err2 = document.getElementsByName("irating");
        err2[0].style.border = "none";

        let err3 = document.getElementsByName("grating");
        err3[0].style.border = "none";

        if (data.thumb.length < 1) {
          let err = document.getElementsByClassName("errorfile");
          err[0].innerHTML = "plese select a thumbnail";
          err[0].style.display = "inline";
        } else {
          let err = document.getElementsByClassName("errorfile");
          err[0].style.display = "none";
        }
      }
      console.log("movie link", data.link);
      const regexp =
        /^.*(?:(?:youtu\.be\/|v\/|vi\/|u\/\w\/|embed\/|shorts\/)|(?:(?:watch)?\?v(?:i)?=|\&v(?:i)?=))([^#\&\?]*).*/;
      const videoid = data.link.match(regexp);
      let isrestric = false;
      if (videoid != null) {
        const response =
          await fetch(`https://www.googleapis.com/youtube/v3/videos?id=${videoid[1]}&key=AIzaSyCHWLzPxk8hO6jW-vAH4zn-pG54ihUcYa8&part=snippet,contentDetails,status
    `);
        const jsonData = await response.json();

        if (
          jsonData?.items[0]?.status?.embeddable === false ||
          jsonData?.items[0]?.contentDetails?.contentRating?.ytRating ===
            "ytAgeRestricted" ||
          jsonData?.items.length == 0
        ) {
          if (jsonData?.items.length === 0) {
            return toast.success("Movie link not found", {
              position: toast.POSITION.TOP_CENTER,
              autoClose: 300,
            });
          }
          isrestric = true;
        }
      }

      console.log("cat", data.cat, "gen", data.gen, "lan", data.lang);
      formdata.append("id", id);
      formdata.append("title", data.title);
      formdata.append("link", data.link);
      formdata.append("ismovie", data.ismovie.value);
      formdata.append("season", data.season);
      formdata.append("caption", data.caption);
      formdata.append("views", data.views.length > 0 ? data.views : 0);
      formdata.append("grating", data.grating);
      formdata.append("irating", data.irating);
      formdata.append("cat", JSON.stringify(data.cat));
      formdata.append("genre", JSON.stringify(data.gen));
      formdata.append("lang", JSON.stringify(data.lang));
      formdata.append("post", data.post);
      formdata.append("thumb", data.thumb);
      formdata.append("isrestric", isrestric);
      formdata.append("free_ep", data.free_ep);
      const senddata = await fetch(`${window.path}/adminaddpost`, {
        method: "POST",
        headers: {
          auth: token,
        },
        body: formdata,
      });
      const res = await senddata.json();
      if (res.status === 1) {
        toast.success("post uploaded successfully !", {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 300,
        });
        console.log("is movie...", data.ismovie.value);
        console.log("movie id is ", res.movieid);
        setmovieid(res.movieid);
        if (data.ismovie.value !== "Mxvideos") {
          setdata({
            title: "",
            ismovie: "",
            season: "",
            caption: "",
            views: "",
            grating: "",
            irating: "",
            cat: "",
            lang: "",
            post: "",
            gen: "",
            link: "",
            free_ep: "",
          });
          setthumbpreview(null);
        }
      }
    }
  };
  const getfilter = async () => {
    const fetchfilter = await fetch(`${window.path}/adminshowfilters`, {
      method: "POST",
      headers: {
        auth: token,
      },
    });
    const resp = await fetchfilter.json();
    setresult(resp);
  };
  useEffect(() => {
    getfilter();
  }, [data?.cat?.__isNew__, data?.gen?.__isNew__, data?.lang?.__isNew__]);

  function generateRandomString(length) {
    let result = "";
    const characters =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }

  const handlefile = (e) => {
    let file = e.target.files[0];
    const fileSizeInMB = file.size / (1024 * 1024);

    if (fileSizeInMB > 2) {
      return toast.error("File size is more than 2 MB", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 1000,
      });
    }
    console.log("files name", file);
    const originalFileName = file.name;
    const randomString = generateRandomString(15);
    const newFileName = randomString;
    const newFile = new File([file], newFileName, { type: file.type });
    console.log("file name after update", newFile);
    setdata({ ...data, [e.target.name]: newFile, fileName: newFileName });
    setthumbpreview(URL.createObjectURL(file));
  };

  const handleChanged = (event, name) => {
    console.log(event);
    setdata({ ...data, [name.name]: event });
  };
  const names = [
    { value: "movie", label: "movie" },
    { value: "series", label: "series" },
    { value: "Mxvideos", label: "maxvideos" },
  ];

  const cat = result?.category?.map((e) => {
    return { value: e.category, label: e.category };
  });
  const gen = result?.genre?.map((e) => {
    return { value: e.genre, label: e.genre };
  });
  const lang = result?.language?.map((e) => {
    return { value: e.language, label: e.language };
  });

  const fetchthumb = async (e) => {
    const url = e.target.value;
    const regexp =
      /^.*(?:(?:youtu\.be\/|v\/|vi\/|u\/\w\/|embed\/|shorts\/)|(?:(?:watch)?\?v(?:i)?=|\&v(?:i)?=))([^#\&\?]*).*/;
    const videoid = url?.match(regexp);

    const response =
      await fetch(`https://www.googleapis.com/youtube/v3/videos?id=${videoid[1]}&key=AIzaSyCHWLzPxk8hO6jW-vAH4zn-pG54ihUcYa8&part=snippet,contentDetails,status
    `);
    const jsonData = await response.json();
    console.log("jsonData", jsonData?.items.length);

    if (
      jsonData?.items[0]?.status?.embeddable === false ||
      jsonData?.items[0]?.contentDetails?.contentRating?.ytRating ===
        "ytAgeRestricted" ||
      jsonData?.items[0]?.contentDetails?.regionRestriction?.allowed?.length >
        0 ||
      jsonData?.items.length == 0
    ) {
      let err = document.getElementsByName("post");
      err[0].style.border = "2px solid red";
      return toast.error("Trailer link is not supported", {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 1000,
      });
    }
    let err = document.getElementsByName("post");
    err[0].style.border = "none";
    console.log("videoid", videoid, "length", jsonData?.items.length);
    if (videoid != null && jsonData?.items.length != 0) {
      console.log("videoid in if", videoid, "length", jsonData?.items.length);
      setdata({
        ...data,
        thumb: `https://img.youtube.com/vi/${videoid[1]}/0.jpg`,
      });
      setthumbpreview(`https://img.youtube.com/vi/${videoid[1]}/0.jpg`);
    } else {
      setdata({ ...data, thumb: "" });
      setthumbpreview(null);
    }
  };
  const [titledata, settitledata] = useState(null);
  var handletitle = async (e) => {
    if (e?.target?.value?.length >= 3) {
      const titleform = new FormData();
      titleform.append("keyword", e?.target?.value);
      const fetchtitle = await fetch(`${window.path}/checktitle`, {
        method: "POST",
        headers: {
          auth: token,
        },
        body: titleform,
      });
      const gettitle = await fetchtitle.json();
      if (gettitle.status > 0) {
        settitledata(gettitle);
      } else {
        settitledata(null);
      }
    } else {
      settitledata(null);
    }
  };

  const handleKeyDown = (event) => {
    // Check if the pressed key is a digit (0-9)
    if (/\d/.test(event.key)) {
      event.preventDefault();
    }
  };

  return (
    <>
      <MDBox
        variant=""
        sx={{
          border: "none",
          padding: 2,
          marginTop: -10,
          height: isMobile ? "1100px" : "700px",
        }}
      >
        <form>
          <MDBox
            width={isMobile ? "100%" : "70%"}
            align={isMobile ? "" : "left"}
            marginRight={isMobile ? "" : "55px"}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                e.preventDefault();
              }
            }}
          >
            <TextField
              required
              style={{
                width: isMobile ? "100%" : "400px",
                margin: "7px",
                align: "left",
              }}
              type="text"
              label="Title"
              name="title"
              variant="outlined"
              className="error"
              value={data.title}
              onChange={(e) => {
                handleChange(e);
                handletitle(e);
              }}
              onFocus={(e) => {
                bordernone(e);
                settitlefocus(true);
              }}
              onBlur={() => {
                settitlefocus(false);
              }}
            />
            {titledata != null && titlefocus == true ? (
              <ul
                style={{
                  listStyle: "none",
                  overflowY: "scroll",
                  position: "absolute",
                  marginLeft: 180,
                  zIndex: 100,
                  width: "400px",
                  height: "140px",
                  backgroundColor: "white",
                  border: "1px solid black",
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "left",
                }}
              >
                {titledata?.data?.map((e) => (
                  <li
                    onClick={() => {}}
                    style={{
                      textAlign: "left",
                      marginLeft: -15,
                      width: "100%",
                      borderBottom: "1px solid gray",
                    }}
                  >
                    {e.title}
                  </li>
                ))}
              </ul>
            ) : (
              ""
            )}
            <TextField
              style={{
                width: isMobile ? "100%" : "400px",
                margin: "7px",
                align: "left",
              }}
              type="text"
              label="Trailer Link"
              name="post"
              variant="outlined"
              value={data.post}
              className="error"
              onChange={handleChange}
              onFocus={bordernone}
              onBlur={fetchthumb}
            />
            <br />
            <TextField
              style={{
                width: isMobile ? "100%" : "400px",
                margin: "7px",
                align: "left",
              }}
              type="text"
              label="Movie Link"
              name="link"
              variant="outlined"
              value={data.link}
              className="error"
              onChange={handleChange}
              required
              onFocus={bordernone}
            />
            <br />
            {thumbpreview != null ? (
              <Card
                className="thumbnail-div"
                style={{
                  minWidth: 200,
                  minHeight: 180,
                  position: isMobile ? "" : "absolute",
                  top: isMobile ? "" : 150,
                  left: isMobile ? 10 : 80,
                }}
              >
                <CardContent sx={{ marginBottom: -5 }}>
                  <MDTypography>THUMBNAIL</MDTypography>
                </CardContent>
                <CardMedia>
                  <img src={thumbpreview} />
                </CardMedia>
                <CardContent>
                  <MDTypography variant="h6">{thumbpreview}</MDTypography>
                </CardContent>
              </Card>
            ) : (
              ""
            )}
            <MDBox width={isMobile ? "100%" : "50%"} align="left">
              <MDBox
                width={isMobile ? "100%" : "200px"}
                p={-7}
                align="left"
                variant=""
                mt="10px"
                ml="5px"
                border="1px"
                className="ismovie"
                name="typeselect"
                onFocus={typenone}
                ref={typeref}
                onKeyDown={handleKeyDown}
              >
                <Select
                  width="50px"
                  align="center"
                  placeholder="Type"
                  name="ismovie"
                  onChange={handleChanged}
                  options={names}
                  styles={colourStyles}
                  value={data.ismovie}
                  required
                  onKeyDown={handleKeyDown}
                ></Select>
              </MDBox>
            </MDBox>
            {data.ismovie.value == "Mxvideos" ? (
              <>
                <TextField
                  style={{ width: isMobile ? "100%" : "180px", margin: "9px" }}
                  type="number"
                  label="Free Episode"
                  variant="outlined"
                  name="free_ep"
                  value={data.free_ep}
                  onChange={handleChange}
                  onFocus={bordernone}
                />
              </>
            ) : (
              ""
            )}
            {data.ismovie.value == "series" ? (
              <>
                <TextField
                  style={{ width: isMobile ? "100%" : "180px", margin: "9px" }}
                  type="number"
                  label="Season"
                  variant="outlined"
                  name="season"
                  value={data.season}
                  onChange={handleChange}
                  onFocus={bordernone}
                />
              </>
            ) : (
              ""
            )}
            {data.ismovie.value === "Mxvideos" && (
              <MDBox
                position="absolute"
                right={isMobile ? "10px" : "70px"}
                top="50px"
                zIndex="1"
                bgcolor="white"
                p={2}
                borderRadius="4px"
                boxShadow="0 4px 20px 0 rgba(0, 0, 0, 0.14)"
              >
                <MDTypography variant="h6" mb={2}>
                  MX Video Details
                </MDTypography>
                <div style={{ height: "630px", overflowY: "auto" }}>
                  {maxVideoRows.map((row) => (
                    <MDBox
                      key={row.id}
                      display="flex"
                      alignItems="center"
                      mb={2}
                      width="100%"
                    >
                      <TextField
                        style={{ width: "100px", marginRight: "10px" }}
                        type="text"
                        label="Ep. No."
                        variant="outlined"
                        value={row.field1}
                        onChange={(e) =>
                          handleMaxVideoChange(row.id, "field1", e.target.value)
                        }
                      />
                      <TextField
                        style={{ width: "200px", marginRight: "10px" }}
                        type="text"
                        label="Episode sub title"
                        variant="outlined"
                        value={row.field3}
                        onChange={(e) =>
                          handleMaxVideoChange(row.id, "field3", e.target.value)
                        }
                      />
                      <TextField
                        style={{ width: "200px", marginRight: "10px" }}
                        type="text"
                        label="Episode Link"
                        variant="outlined"
                        value={row.field2}
                        onChange={(e) =>
                          handleMaxVideoChange(row.id, "field2", e.target.value)
                        }
                      />
                      <MDButton
                        variant="contained"
                        color="success"
                        style={{ minWidth: "40px", height: "40px" }}
                        onClick={addMaxVideoRow}
                      >
                        +
                      </MDButton>
                      {maxVideoRows.length > 1 && (
                        <MDButton
                          variant="contained"
                          color="error"
                          style={{
                            minWidth: "40px",
                            height: "40px",
                            marginLeft: "10px",
                          }}
                          onClick={() => removeMaxVideoRow(row.id)}
                        >
                          -
                        </MDButton>
                      )}
                    </MDBox>
                  ))}
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "center",
                      paddingBottom: "10px",
                    }}
                  >
                    <button
                      onClick={addallmaxvideo}
                      style={{
                        width: "150px",
                        borderRadius: "10px",
                        fontSize: "15px",
                        paddingLeft: "5px",
                        paddingRight: "5px",
                        paddingBottom: "5px",
                        paddingTop: "5px",
                        background: "#4caf50",
                        border: "none",
                        color: "white",
                        display: "flex",
                        gap: "10px",
                        justifyContent: "center",
                      }}
                    >
                      {episodeLoading ? (
                        <div className="episode_loader"></div>
                      ) : (
                        <></>
                      )}{" "}
                      Add All Episode
                    </button>
                  </div>
                </div>
              </MDBox>
            )}
            <TextField
              style={{
                width: `${
                  data.ismovie.value != "series" &&
                  data.ismovie.value !== "Mxvideos"
                    ? isMobile
                      ? "100%"
                      : "400px"
                    : isMobile
                    ? "100%"
                    : "200px"
                }`,
                margin: "9px",
              }}
              type="number"
              label="Views"
              variant="outlined"
              name="views"
              value={data.views}
              required
              onChange={handleChange}
              onFocus={bordernone}
              onPaste={handlePaste}
            />{" "}
            <p className="errorv">please fill properly</p>
            <TextField
              style={{
                width: isMobile ? "100%" : "400px",
                margin: "7px",
                align: "left",
              }}
              type="text"
              label="Caption"
              variant="outlined"
              name="caption"
              required
              value={data.caption}
              onChange={handleChange}
              onFocus={bordernone}
            />
            <br />
            <MDBox width={isMobile ? "100%" : "50%"} align="left">
              <MDBox
                width={isMobile ? "100%" : "400px"}
                align="left"
                variant=""
                mt={1}
                ml="5px"
                className="cat"
                name="catselect"
                onFocus={catnone}
                ref={catref}
                onKeyDown={handleKeyDown}
              >
                <CreatableSelect
                  align="center"
                  placeholder="Select Category"
                  onChange={handleChanged}
                  options={cat}
                  styles={colourStyles}
                  value={data.cat}
                  name="cat"
                  isMulti
                  isValidNewOption={() => false}
                  required
                  onKeyDown={handleKeyDown}
                ></CreatableSelect>
              </MDBox>
            </MDBox>
            <MDBox width={isMobile ? "100%" : "50%"} align="left">
              <MDBox
                width={isMobile ? "100%" : "400px"}
                p={-7}
                align="left"
                variant=""
                mt="10px"
                ml="5px"
                className="lang"
                name="langselect"
                onFocus={langnone}
                ref={langref}
                onKeyDown={handleKeyDown}
              >
                <CreatableSelect
                  width="50px"
                  align="center"
                  placeholder="Select Language"
                  onChange={handleChanged}
                  options={lang}
                  styles={colourStyles}
                  isMulti
                  name="lang"
                  id="lang"
                  value={data.lang}
                  className=""
                  isValidNewOption={() => false}
                  required
                  onKeyDown={handleKeyDown}
                ></CreatableSelect>
              </MDBox>
            </MDBox>
            <MDBox width={isMobile ? "100%" : "50%"} align="left">
              <MDBox
                width={isMobile ? "100%" : "400px"}
                align="left"
                variant=""
                mt={1}
                ml="5px"
                className="gen"
                onFocus={genone}
                ref={genref}
                onKeyDown={handleKeyDown}
              >
                <CreatableSelect
                  align="center"
                  placeholder="Select Genre"
                  onChange={handleChanged}
                  options={gen}
                  styles={colourStyles}
                  classNamePrefix="filters"
                  className="gen"
                  value={data.gen}
                  name="gen"
                  id="gen"
                  isValidNewOption={() => false}
                  required
                  onKeyDown={handleKeyDown}
                ></CreatableSelect>
              </MDBox>
            </MDBox>
            <TextField
              style={{
                width: isMobile ? "100%" : "195px",
                margin: "7px",
                align: "left",
              }}
              type="number"
              label="Google Rating"
              variant="outlined"
              InputLabelProps={{
                style: { paddingBottom: "10px" },
              }}
              onChange={handleChange}
              name="grating"
              value={data.grating}
              onFocus={bordernone}
            />
            <TextField
              style={{
                width: isMobile ? "100%" : "195px",
                margin: "7px",
                align: "left",
              }}
              type="number"
              label="IMDB Rating"
              variant="outlined"
              InputLabelProps={{
                style: { paddingBottom: "10px" },
              }}
              onChange={handleChange}
              name="irating"
              value={data.irating}
              onFocus={bordernone}
            />
            <MDBox
              display={isMobile ? "block" : "flex"}
              width="500px"
              marginLeft="-125px"
              mt={2}
            >
              <MDBox
                display="flex"
                width="80%"
                justifyContent="space-around"
                mb={2}
                mt={1}
                style={{ marginLeft: isMobile ? "22%" : "" }}
              >
                <input
                  id="inputthumb"
                  type="file"
                  className="custominput"
                  name="thumb"
                  onChange={handlefile}
                  required
                />
                <label
                  className="custominputlable"
                  htmlFor="inputthumb"
                  ref={fileref}
                  onFocus={filenone}
                >
                  <FileUploadIcon /> &nbsp; Upload Thumbnail
                </label>
                <br />
              </MDBox>
              <MDBox style={{ marginLeft: isMobile ? "45%" : "" }}>
                <MDButton
                  variant="contained"
                  className="button"
                  color="success"
                  onClick={handleUploadPost}
                  type="submit"
                >
                  Upload
                </MDButton>
              </MDBox>
            </MDBox>
            <span className="errorfile"></span>
          </MDBox>
          {/* <div className="errorfile" style={{ marginLeft: "50px" }}>
            <p>please select a thumbnail</p>
          </div> */}
        </form>
      </MDBox>
    </>
  );
};

export default Uploadpost;
