import 'package:my_video/app_imports.dart';

class SignupPage extends StatefulWidget {
  const SignupPage({super.key});

  @override
  State<SignupPage> createState() => SignupPageState();
}

class SignupPageState extends State<SignupPage> {
  SignupPageHelper? _signupPageHelper;
  late AuthenticationController authenticationController;

  @override
  void initState() {
    super.initState();
    authenticationController = Get.find<AuthenticationController>();
  }

  @override
  Widget build(BuildContext context) {
    MySize.init(context);
    _signupPageHelper = _signupPageHelper ?? SignupPageHelper(this);
    
    return GetBuilder<AuthenticationController>(
      builder: (controller) {
        return AppScaffold(
          body: SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(MySize.width(16)),
              child: Form(
                key: controller.signupForm<PERSON>ey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Space.height(40),

                    // Logo and Title
                    Column(
                      children: [
                        Container(
                          width: MySize.width(80),
                          height: MySize.height(80),
                          decoration: BoxDecoration(
                            color: AppColorConstants.primaryColor,
                            borderRadius: BorderRadius.circular(MySize.radius(20)),
                          ),
                          child: Icon(
                            Icons.movie_outlined,
                            size: MySize.height(40),
                            color: AppColorConstants.textPrimary,
                          ),
                        ),
                        
                        Space.height(24),
                        
                        AppText(
                          text: 'Create Account',
                          fontSize: MySize.fontSize(28),
                          fontWeight: FontWeight.bold,
                          color: AppColorConstants.textPrimary,
                          textAlign: TextAlign.center,
                        ),
                        
                        Space.height(8),
                        
                        AppText(
                          text: 'Join us to enjoy unlimited movies',
                          fontSize: MySize.fontSize(16),
                          color: AppColorConstants.textSecondary,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                    
                    Space.height(40),
                    
                    // Full Name Field
                    AppTextFormField(
                      controller: controller.nameController,
                      labelText: 'Full Name',
                      hintText: 'Enter your full name',
                      keyboardType: TextInputType.name,
                      textInputAction: TextInputAction.next,
                      prefixIcon: Icon(
                        Icons.person_outline,
                        size: MySize.height(20),
                      ),
                      validator: controller.validateName,
                    ),
                    
                    Space.height(16),
                    
                    // Email Field
                    AppTextFormField(
                      controller: controller.emailController,
                      labelText: 'Email',
                      hintText: 'Enter your email',
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.next,
                      prefixIcon: Icon(
                        Icons.email_outlined,
                        size: MySize.height(20),
                      ),
                      validator: controller.validateEmail,
                    ),
                    
                    Space.height(16),
                    
                    // Password Field
                    Obx(() => AppTextFormField(
                      controller: controller.passwordController,
                      labelText: 'Password',
                      hintText: 'Enter your password',
                      obscureText: !controller.isPasswordVisible,
                      textInputAction: TextInputAction.next,
                      prefixIcon: Icon(
                        Icons.lock_outlined,
                        size: MySize.height(20),
                      ),
                      suffixIcon: IconButton(
                        icon: Icon(
                          controller.isPasswordVisible
                              ? Icons.visibility_off
                              : Icons.visibility,
                          size: MySize.height(20),
                        ),
                        onPressed: controller.togglePasswordVisibility,
                      ),
                      validator: controller.validatePassword,
                    )),
                    
                    Space.height(16),
                    
                    // Confirm Password Field
                    Obx(() => AppTextFormField(
                      controller: controller.confirmPasswordController,
                      labelText: 'Confirm Password',
                      hintText: 'Confirm your password',
                      obscureText: !controller.isConfirmPasswordVisible,
                      textInputAction: TextInputAction.done,
                      prefixIcon: Icon(
                        Icons.lock_outlined,
                        size: MySize.height(20),
                      ),
                      suffixIcon: IconButton(
                        icon: Icon(
                          controller.isConfirmPasswordVisible
                              ? Icons.visibility_off
                              : Icons.visibility,
                          size: MySize.height(20),
                        ),
                        onPressed: controller.toggleConfirmPasswordVisibility,
                      ),
                      validator: controller.validateConfirmPassword,
                    )),
                    
                    Space.height(24),
                    
                    // Terms and Conditions
                    Obx(() => Row(
                      children: [
                        Checkbox(
                          value: controller.acceptTerms,
                          onChanged: controller.toggleAcceptTerms,
                          activeColor: AppColorConstants.primaryColor,
                        ),
                        Expanded(
                          child: GestureDetector(
                            onTap: controller.toggleAcceptTerms,
                            child: RichText(
                              text: TextSpan(
                                style: TextStyle(
                                  fontSize: MySize.fontSize(14),
                                  color: AppColorConstants.textSecondary,
                                ),
                                children: [
                                  const TextSpan(text: 'I agree to the '),
                                  TextSpan(
                                    text: 'Terms of Service',
                                    style: TextStyle(
                                      color: AppColorConstants.primaryColor,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const TextSpan(text: ' and '),
                                  TextSpan(
                                    text: 'Privacy Policy',
                                    style: TextStyle(
                                      color: AppColorConstants.primaryColor,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    )),
                    
                    Space.height(32),
                    
                    // Signup Button
                    Obx(() => AppButton(
                      text: 'Create Account',
                      onPressed: controller.apiStatus.isLoading ? null : controller.signup,
                      isLoading: controller.apiStatus.isLoading,
                      backgroundColor: AppColorConstants.primaryColor,
                      textColor: AppColorConstants.textPrimary,
                    )),
                    
                    Space.height(24),
                    
                    // Login Link
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AppText(
                          text: "Already have an account? ",
                          fontSize: MySize.fontSize(14),
                          color: AppColorConstants.textSecondary,
                        ),
                        GestureDetector(
                          onTap: () => context.go(AppRoutes.login),
                          child: AppText(
                            text: 'Sign In',
                            fontSize: MySize.fontSize(14),
                            color: AppColorConstants.primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    
                    Space.height(40),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
