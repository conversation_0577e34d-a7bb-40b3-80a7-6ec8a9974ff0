import 'package:my_video/app_imports.dart';

class NoInternetConnectionPage extends StatefulWidget {
  const NoInternetConnectionPage({super.key});

  @override
  State<NoInternetConnectionPage> createState() => _NoInternetConnectionPageState();
}

class _NoInternetConnectionPageState extends State<NoInternetConnectionPage> {
  bool _isRetrying = false;

  @override
  void initState() {
    super.initState();
    _listenToConnectivity();
  }

  void _listenToConnectivity() {
    ConnectivityHelper.listenToConnectionChanges(
      onConnected: () {
        if (mounted) {
          AppHelper.showToast('Internet connection restored');
          context.go(AppRoutes.splash);
        }
      },
      onDisconnected: () {
        // Already on no internet page
      },
    );
  }

  Future<void> _retryConnection() async {
    setState(() {
      _isRetrying = true;
    });

    try {
      final hasConnection = await ConnectivityHelper.hasInternetConnection();
      
      if (hasConnection) {
        if (mounted) {
          AppHelper.showToast('Connection restored!');
          context.go(AppRoutes.splash);
        }
      } else {
        if (mounted) {
          AppHelper.showToast('Still no internet connection', isError: true);
        }
      }
    } catch (e) {
      AppHelper.logError('Retry connection error', e);
      if (mounted) {
        AppHelper.showToast('Failed to check connection', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRetrying = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      body: Padding(
        padding: const EdgeInsets.all(AppSizeConstants.paddingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // No Internet Icon
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppColorConstants.colorRed.withOpacity(0.1),
                borderRadius: BorderRadius.circular(60),
              ),
              child: const Icon(
                Icons.wifi_off_rounded,
                size: 60,
                color: AppColorConstants.colorRed,
              ),
            ),
            
            const SizedBox(height: AppSizeConstants.marginLarge),
            
            // Title
            const AppText(
              text:  'no_internet_connection',
              textAlign: TextAlign.center,
              color: AppColorConstants.colorDarkGrey,
            ),
            
            const SizedBox(height: AppSizeConstants.marginMedium),
            
            // Description
            const AppText(
              text:  'Please check your internet connection and try again.',
              textAlign: TextAlign.center,
              color: AppColorConstants.colorGrey,
            ),
            
            const SizedBox(height: AppSizeConstants.marginExtraLarge),
            
            // Retry Button
            AppButton.primary(
              text: 'retry',
              onPressed: _isRetrying ? null : _retryConnection,
              isLoading: _isRetrying,
              icon: const Icon(
                Icons.refresh,
                color: AppColorConstants.colorWhite,
              ),
            ),
            
            const SizedBox(height: AppSizeConstants.marginMedium),
            
            // Settings Button
            AppButton.outlined(
              text: 'Open Settings',
              onPressed: () async {
                // Open device settings
                try {
                  await launchUrl(
                    Uri.parse('app-settings:'),
                    mode: LaunchMode.externalApplication,
                  );
                } catch (e) {
                  AppHelper.logError('Failed to open settings', e);
                  AppHelper.showToast('Unable to open settings', isError: true);
                }
              },
              icon: const Icon(
                Icons.settings,
                color: AppColorConstants.colorPrimary,
              ),
            ),
            
            const SizedBox(height: AppSizeConstants.marginLarge),
            
            // Tips
            Container(
              padding: const EdgeInsets.all(AppSizeConstants.paddingMedium),
              decoration: BoxDecoration(
                color: AppColorConstants.colorLightGrey,
                borderRadius: BorderRadius.circular(AppSizeConstants.radiusMedium),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const AppText(
                   text:  'Troubleshooting Tips:',
                  ),
                  const SizedBox(height: AppSizeConstants.marginSmall),
                  _buildTipItem('• Check if WiFi or mobile data is enabled'),
                  _buildTipItem('• Try switching between WiFi and mobile data'),
                  _buildTipItem('• Restart your router or modem'),
                  _buildTipItem('• Move closer to your WiFi router'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTipItem(String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: AppText(
        text:  tip,
        color: AppColorConstants.colorDarkGrey,
      ),
    );
  }
}
