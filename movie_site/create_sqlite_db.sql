-- SQLite Database Schema for Movie Site
-- Converted from MySQL schema

-- Categories table
CREATE TABLE category_tbl (
    cat_id INTEGER PRIMARY KEY,
    cat_name VARCHAR(30) NOT NULL
);

INSERT INTO category_tbl (cat_id, cat_name) VALUES
(1, 'Action'),
(2, 'Comedy'),
(3, 'Drama'),
(4, 'War'),
(5, 'Horror'),
(6, 'Mystery'),
(10, 'Adventure'),
(11, 'Love Story'),
(19, 'Thriller'),
(21, 'Sci-fi'),
(25, 'Fantasy'),
(28, 'Crime'),
(29, 'Political'),
(30, 'Romantic');

-- Languages table
CREATE TABLE language_tbl (
    lang_id INTEGER PRIMARY KEY,
    language VARCHAR(30) NOT NULL
);

INSERT INTO language_tbl (lang_id, language) VALUES
(1, 'English'),
(2, 'Hindi'),
(3, 'Tamil'),
(4, 'Telugu');

-- Genres table
CREATE TABLE genre_tbl (
    id INTEGER PRIMARY KEY,
    genre VARCHAR(30) NOT NULL
);

INSERT INTO genre_tbl (id, genre) VALUES
(1, 'Action'),
(2, 'Comedy'),
(3, 'Drama'),
(4, 'Horror'),
(5, 'Thriller');

-- Users table
CREATE TABLE user_tbl (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE,
    profile VARCHAR(100),
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample admin user
INSERT INTO user_tbl (id, name, email, profile) VALUES
(1, 'Admin', '<EMAIL>', 'default_profile.jpg');

-- Main posts/movies table
CREATE TABLE post_tbl (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_id INTEGER NOT NULL DEFAULT 1,
    source_name VARCHAR(50) NOT NULL DEFAULT 'Admin',
    source_profile VARCHAR(100) NOT NULL DEFAULT 'default_profile.jpg',
    title VARCHAR(100) NOT NULL,
    link TEXT NOT NULL,
    ismovie INTEGER NOT NULL DEFAULT 1,
    season INTEGER DEFAULT NULL,
    caption TEXT NOT NULL,
    views INTEGER NOT NULL DEFAULT 0,
    thumbnail VARCHAR(100) NOT NULL,
    trailer_link TEXT,
    report_count INTEGER DEFAULT 0,
    imdb_rating REAL DEFAULT NULL,
    google_rating INTEGER DEFAULT NULL,
    cat_id VARCHAR(100) NOT NULL,
    genre_id VARCHAR(20) DEFAULT NULL,
    lang_id VARCHAR(100) NOT NULL,
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    -- Additional fields for video validation
    isrestric INTEGER DEFAULT 0,
    isembeded INTEGER DEFAULT 0,
    ismoviecrawl INTEGER DEFAULT 0,
    istrailercrawl INTEGER DEFAULT 0,
    iscopyright INTEGER DEFAULT 0,
    isprivate INTEGER DEFAULT 0,
    isUltra INTEGER DEFAULT 0,
    isrestricmovie INTEGER DEFAULT 0,
    agerestric INTEGER DEFAULT 0
);

-- Sample movie data
INSERT INTO post_tbl (title, link, caption, thumbnail, trailer_link, cat_id, genre_id, lang_id) VALUES
('Sample Movie 1', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'A sample movie for testing', 'sample1.jpg', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', '1,2', '1', '1,2'),
('Sample Movie 2', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'Another sample movie', 'sample2.jpg', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', '2,3', '2', '2');

-- MX Video episodes table (for series episodes)
CREATE TABLE mxvideo (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(100) NOT NULL,
    link TEXT NOT NULL,
    thumbnail VARCHAR(100),
    epno INTEGER,
    movieid INTEGER,
    sub_title VARCHAR(200),
    is_trailer INTEGER DEFAULT 0,
    iscrawl INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (movieid) REFERENCES post_tbl(id)
);

-- Likes table
CREATE TABLE like_tbl (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_id INTEGER NOT NULL,
    dest_id INTEGER NOT NULL,
    post_id INTEGER NOT NULL,
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (post_id) REFERENCES post_tbl(id)
);

-- Views table
CREATE TABLE view_tbl (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    post_id INTEGER NOT NULL,
    ip_address VARCHAR(45),
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (post_id) REFERENCES post_tbl(id)
);

-- Search history table
CREATE TABLE search_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    search_term VARCHAR(100) NOT NULL,
    results_count INTEGER DEFAULT 0,
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Admin settings table
CREATE TABLE admin_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_key VARCHAR(50) UNIQUE NOT NULL,
    setting_value TEXT,
    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Insert default admin settings
INSERT INTO admin_settings (setting_key, setting_value) VALUES
('site_name', 'Movie Streaming Site'),
('admin_email', '<EMAIL>'),
('max_upload_size', '10485760'),
('youtube_api_key', 'your_youtube_api_key_here');
