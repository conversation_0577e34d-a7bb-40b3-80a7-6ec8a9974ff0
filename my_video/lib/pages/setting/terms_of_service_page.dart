import 'package:my_video/app_imports.dart';

class TermsOfServicePage extends StatelessWidget {
  const TermsOfServicePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const AppText(
          text: 'Terms of Service',
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        backgroundColor: AppColorConstants.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: AppColorConstants.textPrimary,
          ),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(MySize.width(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection(
              'Acceptance of Terms',
              'By downloading, installing, or using the MyVideo app, you agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use our service.',
            ),
            Space.height(20),

            _buildSection(
              'Description of Service',
              'MyVideo is a video streaming application that allows users to watch movies and videos. Our service provides access to a curated collection of entertainment content.',
            ),
            Space.height(20),

            _buildSection(
              'User Accounts',
              'You may be required to create an account to access certain features. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.',
            ),
            Space.height(20),

            _buildSection(
              'Content Usage',
              'The content available through MyVideo is for personal, non-commercial use only. You may not reproduce, distribute, modify, or create derivative works from our content without explicit permission.',
            ),
            Space.height(20),

            _buildSection(
              'Privacy',
              'Your privacy is important to us. Please review our Privacy Policy to understand how we collect, use, and protect your information.',
            ),
            Space.height(20),

            _buildSection(
              'Prohibited Activities',
              'You agree not to:\n• Use the service for any illegal purposes\n• Attempt to gain unauthorized access to our systems\n• Upload or transmit malicious code\n• Interfere with other users\' experience\n• Violate any applicable laws or regulations',
            ),
            Space.height(20),

            _buildSection(
              'Subscription and Payments',
              'Some features may require a paid subscription. Subscription fees are charged in advance and are non-refundable except as required by law. You can cancel your subscription at any time.',
            ),
            Space.height(20),

            _buildSection(
              'Intellectual Property',
              'All content, trademarks, and intellectual property rights in the MyVideo app belong to us or our licensors. You may not use our intellectual property without permission.',
            ),
            Space.height(20),

            _buildSection(
              'Limitation of Liability',
              'MyVideo is provided "as is" without warranties. We are not liable for any damages arising from your use of the service, except as required by law.',
            ),
            Space.height(20),

            _buildSection(
              'Termination',
              'We may terminate or suspend your access to MyVideo at any time, with or without notice, for any reason including violation of these terms.',
            ),
            Space.height(20),

            _buildSection(
              'Changes to Terms',
              'We reserve the right to modify these terms at any time. Continued use of the service after changes constitutes acceptance of the new terms.',
            ),
            Space.height(20),

            _buildSection(
              'Contact Information',
              'If you have questions about these Terms of Service, please contact us through the app\'s support section.',
            ),
            Space.height(20),

            Container(
              width: double.infinity,
              padding: EdgeInsets.all(MySize.width(16)),
              decoration: BoxDecoration(
                color: AppColorConstants.cardColor,
                borderRadius: BorderRadius.circular(MySize.radius(8)),
              ),
              child: Column(
                children: [
                  AppText(
                    text:
                        'Last Updated: ${DateFormat('MMMM dd, yyyy').format(DateTime.now())}',
                    fontSize: MySize.fontSize(12),
                    color: AppColorConstants.textSecondary,
                    textAlign: TextAlign.center,
                  ),
                  Space.height(8),
                  AppText(
                    text: 'MyVideo v1.0.0',
                    fontSize: MySize.fontSize(12),
                    color: AppColorConstants.textSecondary,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            Space.height(100), // Bottom padding
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          text: title,
          fontSize: MySize.fontSize(18),
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        Space.height(8),
        AppText(
          text: content,
          fontSize: MySize.fontSize(14),
          color: AppColorConstants.textSecondary,
        ),
      ],
    );
  }
}
