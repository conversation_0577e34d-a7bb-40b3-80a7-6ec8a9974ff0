const express = require("express");
const cookieParser = require("cookie-parser");
const bodyParser = require("body-parser");
const multer = require("multer");
const form = multer();
const router = require("./routes/simple_router");
const cors = require("cors");
const session = require("express-session");
const passport = require("passport");
const GoogleStrategy = require("passport-google-oauth20").Strategy;
var fs = require("fs");
const axios = require("axios");
const querystring = require("querystring");
const crypto = require("crypto");
var CryptoJS = require("crypto-js");
const base64url = require("base64url");
const cron = require("node-cron");
const { google } = require("googleapis");
const { authenticate } = require("@google-cloud/local-auth");
const database = require("./db/database");
const { lookup } = require("geoip-lite");

require("dotenv").config();

const app = express();
app.use(bodyParser.json());

// CORS configuration
const corsOptions = {
  origin: "*",
  credentials: true,
  optionsSuccessStatus: 200,
  methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
};

app.use(cors(corsOptions));
app.use(cookieParser());

// Session configuration
app.use(
  session({
    secret: process.env.USER_SECRET || "default_secret",
    resave: false,
    saveUninitialized: true,
  })
);

app.use(bodyParser.urlencoded({ extended: true }));

// Cache control for images
const setImageCacheControl = (req, res, next) => {
  if (req.url.includes("/thumbnail") || req.url.includes("/profile")) {
    res.setHeader("Cache-Control", "public, max-age=31536000");
  }
  next();
};

app.use(setImageCacheControl);
app.use(passport.initialize());
app.use(passport.session());

// Google OAuth strategies
passport.use(
  "google1",
  new GoogleStrategy(
    {
      clientID: "274926775178-6s9ffe31b1re6ekv845drmrgmjch5gil.apps.googleusercontent.com",
      clientSecret: "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",
      callbackURL: "https://appzone99.science:3001/auth/google/callback",
    },
    (accessToken, refreshToken, profile, done) => {
      return done(null, profile.emails[0].value);
    }
  )
);

passport.use(
  "google2",
  new GoogleStrategy(
    {
      clientID: "576494607076-3kja9rpqkqn4vadlhf12c7rr1lr5bobr.apps.googleusercontent.com",
      clientSecret: "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",
      callbackURL: "https://appzone99.science:3001/status/auth/google/callback",
    },
    (accessToken, refreshToken, profile, done) => {
      return done(null, profile.emails[0].value);
    }
  )
);

// Static file serving
app.use("/upload/thumbnail", express.static("uploads/thumbnail"));
app.use("/upload/profile", express.static("uploads/profile"));
app.use("/upload/demo", express.static("uploads/demo"));

// Session middleware for different routes
app.use(
  "/",
  session({
    key: "user",
    secret: process.env.USER_SECRET || "default_user_secret",
    resave: true,
    saveUninitialized: false,
  })
);

app.use(
  "/adminlogin",
  session({
    key: "admin",
    secret: process.env.ADMIN_SECRET || "default_admin_secret",
    resave: true,
    saveUninitialized: false,
  })
);

// Use router
app.use(router);

const port = process.env.PORT || 8010;
const host = process.env.HOST || "localhost";

// Passport serialization
passport.serializeUser((user, done) => {
  done(null, user);
});

passport.deserializeUser((user, done) => {
  done(null, user);
});

// Basic routes
app.get("/login", (req, res) => {
  res.sendFile(__dirname + "/views/moviefliker/login.html");
});

app.get("/statussaver", (req, res) => {
  res.sendFile(__dirname + "/views/statusaver/statussaver.html");
});

app.get("/", (req, res) => {
  res.send("Movie Streaming Backend is running!");
});

app.set("view engine", "ejs");

app.get("/deleteacoount", (req, res) => {
  const userEmail = req.session.userEmail;
  res.render("moviefliker/account", { userEmail });
});

app.get("/viewmovies", (req, res) => {
  res.render("viewmovies/index");
});

app.get("/status/deleteacoount", (req, res) => {
  let userEmail = req.session.userEmail;
  if (userEmail) {
    return res.render("statusaver/status", { userEmail });
  } else {
    return res.redirect("/statussaver");
  }
});

// Start server
app.listen(port, () => {
  console.log(`Server running on http://${host}:${port}`);
  console.log("SQLite database connected successfully");
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('Shutting down gracefully...');
  database.close();
  process.exit(0);
});
