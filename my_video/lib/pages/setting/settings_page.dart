import 'package:my_video/app_imports.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => SettingsPageState();
}

class SettingsPageState extends State<SettingsPage> {
  SettingsPageHelper? _settingsPageHelper;
  late SettingsController settingsController;

  @override
  Widget build(BuildContext context) {
    _settingsPageHelper = _settingsPageHelper ?? SettingsPageHelper(this);

    return GetBuilder(
      init: SettingsController(),
      builder: (SettingsController controller) {
        settingsController = controller;
        return Scaffold(
          appBar: AppBar(
            title: const AppText(
              text: 'Settings',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
            ),
            backgroundColor: AppColorConstants.backgroundColor,
            elevation: 0,
          ),
          body: SingleChildScrollView(
            padding: EdgeInsets.all(MySize.width(16)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User Profile Section
                _buildUserProfile(),
                Space.height(24),

                // Account Section
                _buildSectionTitle('Account'),
                Space.height(12),
                _buildSettingTile(
                  icon: Icons.person_outline,
                  title: 'Edit Profile',
                  subtitle: 'Update your personal information',
                  onTap: () => context.go(AppRoutes.profile),
                ),
                Space.height(24),

                // App Settings Section
                _buildSectionTitle('App Settings'),
                Space.height(12),
                _buildSettingTile(
                  icon: Icons.notifications_outlined,
                  title: 'Notifications',
                  subtitle: 'Receive updates and alerts',
                  trailing: Obx(
                    () => Switch(
                      value: _settingsPageHelper!.notificationsEnabled,
                      onChanged: _settingsPageHelper!.updateNotifications,
                      activeColor: AppColorConstants.primaryColor,
                    ),
                  ),
                ),
                _buildSettingTile(
                  icon: Icons.play_arrow_outlined,
                  title: 'Auto Play',
                  subtitle: 'Automatically play next video',
                  trailing: Obx(
                    () => Switch(
                      value: _settingsPageHelper!.autoPlayEnabled,
                      onChanged: _settingsPageHelper!.updateAutoPlay,
                      activeColor: AppColorConstants.primaryColor,
                    ),
                  ),
                ),
                _buildSettingTile(
                  icon: Icons.wifi_outlined,
                  title: 'Download on WiFi Only',
                  subtitle: 'Save mobile data',
                  trailing: Obx(
                    () => Switch(
                      value: _settingsPageHelper!.downloadOnWifiOnly,
                      onChanged: _settingsPageHelper!.updateDownloadWifiOnly,
                      activeColor: AppColorConstants.primaryColor,
                    ),
                  ),
                ),
                Obx(
                  () => _buildSettingTile(
                    icon: Icons.hd_outlined,
                    title: 'Video Quality',
                    subtitle: _settingsPageHelper!.videoQuality,
                    onTap: () => _showVideoQualityDialog(),
                  ),
                ),

                Space.height(24),

                // Support Section
                _buildSectionTitle('Support'),
                Space.height(12),
                _buildSettingTile(
                  icon: Icons.help_outline,
                  title: 'Contact Support',
                  subtitle: 'Get help with the app',
                  onTap: _settingsPageHelper!.contactSupport,
                ),
                _buildSettingTile(
                  icon: Icons.star_outline,
                  title: 'Rate App',
                  subtitle: 'Rate us on the App Store',
                  onTap: _settingsPageHelper!.rateApp,
                ),
                _buildSettingTile(
                  icon: Icons.share_outlined,
                  title: 'Share App',
                  subtitle: 'Tell your friends about us',
                  onTap: _settingsPageHelper!.shareApp,
                ),

                Space.height(24),

                // Legal Section
                _buildSectionTitle('Legal'),
                Space.height(12),
                _buildSettingTile(
                  icon: Icons.description_outlined,
                  title: 'Terms of Service',
                  subtitle: 'Read our terms',
                  onTap: _settingsPageHelper!.openTermsOfService,
                ),
                _buildSettingTile(
                  icon: Icons.privacy_tip_outlined,
                  title: 'Privacy Policy',
                  subtitle: 'Read our privacy policy',
                  onTap: _settingsPageHelper!.openPrivacyPolicy,
                ),

                Space.height(24),

                // Storage Section
                _buildSectionTitle('Storage'),
                Space.height(12),
                _buildSettingTile(
                  icon: Icons.cleaning_services_outlined,
                  title: 'Clear Cache',
                  subtitle: 'Free up storage space',
                  onTap: _settingsPageHelper!.clearCache,
                ),

                Space.height(24),

                // App Info Section
                _buildSectionTitle('App Info'),
                Space.height(12),
                _buildSettingTile(
                  icon: Icons.info_outline,
                  title: 'Version',
                  subtitle: _settingsPageHelper!.appVersion,
                ),

                Space.height(32),

                // Logout Button
                SizedBox(
                  width: double.infinity,
                  child: AppButton(
                    text: 'Logout',
                    onPressed: _settingsPageHelper!.logout,
                    backgroundColor: AppColorConstants.colorRed,
                    icon: const Icon(
                      Icons.logout,
                      color: AppColorConstants.textPrimary,
                    ),
                  ),
                ),

                Space.height(180), // Bottom padding
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUserProfile() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(MySize.width(16)),
      decoration: BoxDecoration(
        color: AppColorConstants.cardColor,
        borderRadius: BorderRadius.circular(MySize.radius(12)),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: MySize.height(30),
            backgroundColor: AppColorConstants.primaryColor,
            child: AppText(
              text: _settingsPageHelper!.userName.isNotEmpty
                  ? _settingsPageHelper!.userName[0].toUpperCase()
                  : 'U',
              fontSize: MySize.fontSize(24),
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
            ),
          ),
          Space.width(16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppText(
                  text: _settingsPageHelper!.userName,
                  fontSize: MySize.fontSize(18),
                  fontWeight: FontWeight.bold,
                  color: AppColorConstants.textPrimary,
                ),
                AppText(
                  text: _settingsPageHelper!.userEmail,
                  fontSize: MySize.fontSize(14),
                  color: AppColorConstants.textSecondary,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return AppText(
      text: title,
      fontSize: MySize.fontSize(16),
      fontWeight: FontWeight.w600,
      color: AppColorConstants.textPrimary,
    );
  }

  Widget _buildSettingTile({
    required IconData icon,
    required String title,
    required String subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: MySize.height(8)),
      decoration: BoxDecoration(
        color: AppColorConstants.cardColor,
        borderRadius: BorderRadius.circular(MySize.radius(8)),
      ),
      child: ListTile(
        leading: Container(
          padding: EdgeInsets.all(MySize.size10),
          decoration: BoxDecoration(
            color: AppColorConstants.colorCCE1F5,
            borderRadius: BorderRadius.circular(MySize.radius(8)),
          ),
          child: Icon(
            icon,
            color: AppColorConstants.primaryColor,
            size: MySize.height(24),
          ),
        ),
        title: AppText(
          text: title,
          fontSize: MySize.fontSize(16),
          fontWeight: FontWeight.w500,
          color: AppColorConstants.textPrimary,
        ),
        subtitle: AppText(
          text: subtitle,
          fontSize: MySize.fontSize(14),
          color: AppColorConstants.textSecondary,
        ),
        trailing:
            trailing ??
            Icon(
              Icons.chevron_right,
              color: AppColorConstants.textSecondary,
              size: MySize.height(20),
            ),
        onTap: onTap,
      ),
    );
  }

  void _showVideoQualityDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const AppText(
            text: 'Video Quality',
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColorConstants.textPrimary,
          ),
          backgroundColor: AppColorConstants.surfaceColor,
          content: Obx(() {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: _settingsPageHelper!.videoQualityOptions.map((quality) {
                return RadioListTile<String>(
                  title: AppText(
                    text: quality,
                    color: AppColorConstants.textPrimary,
                  ),
                  value: quality,
                  groupValue: _settingsPageHelper!.videoQuality,
                  onChanged: (value) {
                    if (value != null) {
                      _settingsPageHelper!.updateVideoQuality(value);
                      Get.back();
                    }
                  },
                  activeColor: AppColorConstants.primaryColor,
                );
              }).toList(),
            );
          }),
        );
      },
    );
  }
}
